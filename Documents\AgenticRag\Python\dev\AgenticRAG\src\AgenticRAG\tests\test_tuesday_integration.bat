@echo off
echo ========================================
echo TUESDAY INTEGRATION TESTING - AgenticRAG
echo ========================================
echo Running comprehensive integration test suite...
echo.

:: Set up environment
set PYTHONPATH=%CD%\src
set CLAUDE_CODE=1
set ANTHROPIC_USER_ID=test-claude

:: Install/upgrade testing dependencies
echo Installing testing dependencies...
..\..\..\.venv\Scripts\python.exe -m pip install pytest pytest-asyncio pytest-mock pytest-timeout --quiet

:: Create test results directory
if not exist "test_results" mkdir test_results

:: Run integration tests
echo.
echo Running integration tests...
echo ----------------------------------------
..\..\..\.venv\Scripts\python.exe -m pytest tests\integration\ ^
    --junit-xml=test_results\integration_test_results.xml ^
    -v ^
    --tb=short ^
    --strict-markers ^
    --timeout=300

set TEST_EXIT_CODE=%ERRORLEVEL%

:: Run existing IMAP integration test
echo.
echo Running existing IMAP workflow test...
echo ----------------------------------------
..\..\..\.venv\Scripts\python.exe tests\integration\test_imap_idle_workflow.py

set IMAP_EXIT_CODE=%ERRORLEVEL%

:: Run email testing scripts
echo.
echo Running email functionality tests...
echo ----------------------------------------
echo Testing direct email functionality...
..\..\..\.venv\Scripts\python.exe test_email_direct.py

set EMAIL_DIRECT_EXIT_CODE=%ERRORLEVEL%

echo.
echo Testing OAuth email functionality...
..\..\..\.venv\Scripts\python.exe test_mail_oauth.py

set EMAIL_OAUTH_EXIT_CODE=%ERRORLEVEL%

:: Generate test summary
echo.
echo ========================================
echo INTEGRATION TEST SUMMARY
echo ========================================

if %TEST_EXIT_CODE% == 0 (
    echo INTEGRATION TESTS: PASSED
) else (
    echo INTEGRATION TESTS: FAILED ^(Exit code: %TEST_EXIT_CODE%^)
)

if %IMAP_EXIT_CODE% == 0 (
    echo IMAP WORKFLOW TEST: PASSED
) else (
    echo IMAP WORKFLOW TEST: FAILED ^(Exit code: %IMAP_EXIT_CODE%^)
)

if %EMAIL_DIRECT_EXIT_CODE% == 0 (
    echo EMAIL DIRECT TEST: PASSED
) else (
    echo EMAIL DIRECT TEST: FAILED ^(Exit code: %EMAIL_DIRECT_EXIT_CODE%^)
)

if %EMAIL_OAUTH_EXIT_CODE% == 0 (
    echo EMAIL OAUTH TEST: PASSED
) else (
    echo EMAIL OAUTH TEST: FAILED ^(Exit code: %EMAIL_OAUTH_EXIT_CODE%^)
)

:: Calculate overall result
set /a OVERALL_EXIT_CODE=%TEST_EXIT_CODE% + %IMAP_EXIT_CODE% + %EMAIL_DIRECT_EXIT_CODE% + %EMAIL_OAUTH_EXIT_CODE%

echo.
if %OVERALL_EXIT_CODE% == 0 (
    echo OVERALL STATUS: ALL TESTS PASSED
    echo All integration tests completed successfully!
) else (
    echo OVERALL STATUS: SOME TESTS FAILED
    echo Please review the output above for failed tests.
)

echo.
echo Integration test components:
echo - Communication endpoints ^(Discord, Slack, Teams, WhatsApp^)
echo - Multi-service coordination and OAuth flows
echo - Database coordination ^(PostgreSQL + Qdrant^)
echo - RAG document processing pipeline
echo - Supervisor workflow coordination
echo - IMAP email workflow
echo - Direct email functionality
echo - OAuth email authentication
echo.
echo JUnit XML report: test_results\integration_test_results.xml
echo Run time: %date% %time%
echo ========================================

:: Keep window open if run directly
if "%1"=="auto" goto :end
pause

:end
exit /b %OVERALL_EXIT_CODE%