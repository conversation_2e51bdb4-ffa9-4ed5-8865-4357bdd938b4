@echo off
echo ========================================
echo FRIDAY SYSTEM HEALTH CHECK - AgenticRAG
echo ========================================
echo Running comprehensive system health monitoring...
echo.

:: Set up environment
set PYTHONPATH=%CD%\src
set CLAUDE_CODE=1
set ANTHROPIC_USER_ID=test-claude

:: Install/upgrade testing dependencies
echo Installing health check dependencies...
..\..\..\.venv\Scripts\python.exe -m pip install pytest pytest-asyncio psutil --quiet

:: Create test results directory
if not exist "test_results" mkdir test_results
if not exist "test_results\health" mkdir test_results\health

:: System Information Collection
echo.
echo ========================================
echo SYSTEM INFORMATION BASELINE
echo ========================================
..\..\..\.venv\Scripts\python.exe -c "
import psutil
import platform
import time
import json
import os

# Collect comprehensive system info
system_info = {
    'timestamp': time.time(),
    'platform': {
        'system': platform.system(),
        'release': platform.release(),
        'version': platform.version(),
        'machine': platform.machine(),
        'processor': platform.processor(),
        'python_version': platform.python_version()
    },
    'cpu': {
        'physical_cores': psutil.cpu_count(logical=False),
        'logical_cores': psutil.cpu_count(logical=True),
        'max_frequency': psutil.cpu_freq().max if psutil.cpu_freq() else 'Unknown',
        'current_frequency': psutil.cpu_freq().current if psutil.cpu_freq() else 'Unknown'
    },
    'memory': {
        'total_gb': psutil.virtual_memory().total / (1024**3),
        'available_gb': psutil.virtual_memory().available / (1024**3),
        'percent_used': psutil.virtual_memory().percent
    },
    'disk': {
        'total_gb': psutil.disk_usage('.').total / (1024**3),
        'free_gb': psutil.disk_usage('.').free / (1024**3),
        'used_gb': psutil.disk_usage('.').used / (1024**3),
        'percent_used': (psutil.disk_usage('.').used / psutil.disk_usage('.').total) * 100
    }
}

# Display system information
print('SYSTEM OVERVIEW:')
print(f'Platform: {system_info['platform']['system']} {system_info['platform']['release']}')
print(f'Processor: {system_info['platform']['processor']}')
print(f'Python: {system_info['platform']['python_version']}')
print(f'CPU Cores: {system_info['cpu']['logical_cores']} logical, {system_info['cpu']['physical_cores']} physical')
print(f'Memory: {system_info['memory']['total_gb']:.1f} GB total, {system_info['memory']['available_gb']:.1f} GB available ({system_info['memory']['percent_used']:.1f}%% used)')
print(f'Disk: {system_info['disk']['total_gb']:.1f} GB total, {system_info['disk']['free_gb']:.1f} GB free ({system_info['disk']['percent_used']:.1f}%% used)')

# Save system info
with open('test_results/health/system_info.json', 'w') as f:
    json.dump(system_info, f, indent=2)

print('\\nSystem information saved to test_results/health/system_info.json')
"

:: File System Health Check
echo.
echo ========================================
echo FILE SYSTEM HEALTH CHECK
echo ========================================
echo Checking critical files and directories...

:: Check critical files
set CRITICAL_FILES_FOUND=0
set CRITICAL_FILES_TOTAL=6

if exist "main.py" (
    echo [OK] main.py - Primary entry point found
    set /a CRITICAL_FILES_FOUND+=1
) else (
    echo [ERROR] main.py - Primary entry point MISSING
)

if exist "main_loop.py" (
    echo [OK] main_loop.py - Main execution loop found
    set /a CRITICAL_FILES_FOUND+=1
) else (
    echo [ERROR] main_loop.py - Main execution loop MISSING
)

if exist "imports.py" (
    echo [OK] imports.py - Import management found
    set /a CRITICAL_FILES_FOUND+=1
) else (
    echo [ERROR] imports.py - Import management MISSING
)

if exist "globals.py" (
    echo [OK] globals.py - Global configuration found
    set /a CRITICAL_FILES_FOUND+=1
) else (
    echo [ERROR] globals.py - Global configuration MISSING
)

if exist "config.py" (
    echo [OK] config.py - Configuration file found
    set /a CRITICAL_FILES_FOUND+=1
) else (
    echo [ERROR] config.py - Configuration file MISSING
)

if exist "pytest.ini" (
    echo [OK] pytest.ini - Test configuration found
    set /a CRITICAL_FILES_FOUND+=1
) else (
    echo [ERROR] pytest.ini - Test configuration MISSING
)

:: Check critical directories
set CRITICAL_DIRS_FOUND=0
set CRITICAL_DIRS_TOTAL=6

if exist "managers\" (
    echo [OK] managers/ - Manager modules directory found
    set /a CRITICAL_DIRS_FOUND+=1
) else (
    echo [ERROR] managers/ - Manager modules directory MISSING
)

if exist "tasks\" (
    echo [OK] tasks/ - Task modules directory found
    set /a CRITICAL_DIRS_FOUND+=1
) else (
    echo [ERROR] tasks/ - Task modules directory MISSING
)

if exist "endpoints\" (
    echo [OK] endpoints/ - Endpoint modules directory found
    set /a CRITICAL_DIRS_FOUND+=1
) else (
    echo [ERROR] endpoints/ - Endpoint modules directory MISSING
)

if exist "userprofiles\" (
    echo [OK] userprofiles/ - User profile modules directory found
    set /a CRITICAL_DIRS_FOUND+=1
) else (
    echo [ERROR] userprofiles/ - User profile modules directory MISSING
)

if exist "tests\" (
    echo [OK] tests/ - Test modules directory found
    set /a CRITICAL_DIRS_FOUND+=1
) else (
    echo [ERROR] tests/ - Test modules directory MISSING
)

if exist "etc\" (
    echo [OK] etc/ - Utility modules directory found
    set /a CRITICAL_DIRS_FOUND+=1
) else (
    echo [ERROR] etc/ - Utility modules directory MISSING
)

echo.
echo File System Health Summary:
echo Critical Files: %CRITICAL_FILES_FOUND%/%CRITICAL_FILES_TOTAL%
echo Critical Directories: %CRITICAL_DIRS_FOUND%/%CRITICAL_DIRS_TOTAL%

:: Run Health Check Tests
echo.
echo ========================================
echo AUTOMATED HEALTH CHECKS
echo ========================================
echo Running system health tests...
..\..\..\.venv\Scripts\python.exe -m pytest tests\health\test_system_health.py ^
    --junit-xml=test_results\health\health_check_results.xml ^
    -v ^
    --tb=short ^
    -s

set HEALTH_EXIT_CODE=%ERRORLEVEL%

:: Application Startup Test
echo.
echo ========================================
echo APPLICATION STARTUP TEST
echo ========================================
echo Testing application startup and basic functionality...

timeout 30 >nul 2>&1 & (
    echo Starting application with 30-second timeout...
    ..\..\..\.venv\Scripts\python.exe -c "
import sys
import os
import asyncio
import time

# Add project path
sys.path.insert(0, 'src')

async def startup_test():
    try:
        # Import and test critical components
        from etc.helper_functions import is_claude_environment
        from managers.manager_supervisors import SupervisorManager
        
        print('Testing Claude environment detection...')
        if is_claude_environment():
            print('[OK] Claude environment detected successfully')
        else:
            print('[WARNING] Claude environment not detected')
        
        print('Testing supervisor manager initialization...')
        supervisor_manager = SupervisorManager.get_instance()
        if supervisor_manager:
            print('[OK] Supervisor manager initialized successfully')
        else:
            print('[ERROR] Supervisor manager initialization failed')
        
        print('[OK] Application startup test completed successfully')
        return True
        
    except Exception as e:
        print(f'[ERROR] Application startup test failed: {e}')
        return False

# Run startup test
result = asyncio.run(startup_test())
sys.exit(0 if result else 1)
"
)

set STARTUP_EXIT_CODE=%ERRORLEVEL%

:: Performance Monitoring Sample
echo.
echo ========================================
echo PERFORMANCE MONITORING SAMPLE
echo ========================================
echo Collecting performance baseline metrics...

..\..\..\.venv\Scripts\python.exe -c "
import psutil
import time
import json

print('Collecting 10-second performance sample...')
metrics = []

for i in range(5):
    cpu_percent = psutil.cpu_percent(interval=2)
    memory = psutil.virtual_memory()
    
    metric = {
        'sample': i + 1,
        'cpu_percent': cpu_percent,
        'memory_percent': memory.percent,
        'memory_available_mb': memory.available / (1024**2)
    }
    metrics.append(metric)
    
    print(f'Sample {i+1}/5: CPU: {cpu_percent:.1f}%%, Memory: {memory.percent:.1f}%%, Available: {memory.available / (1024**2):.0f} MB')

# Calculate averages
avg_cpu = sum(m['cpu_percent'] for m in metrics) / len(metrics)
avg_memory = sum(m['memory_percent'] for m in metrics) / len(metrics)

performance_summary = {
    'average_cpu_percent': avg_cpu,
    'average_memory_percent': avg_memory,
    'samples': metrics
}

# Save performance data
with open('test_results/health/performance_baseline.json', 'w') as f:
    json.dump(performance_summary, f, indent=2)

print(f'\\nPerformance Baseline:')
print(f'Average CPU Usage: {avg_cpu:.1f}%%')
print(f'Average Memory Usage: {avg_memory:.1f}%%')
print('Performance baseline saved to test_results/health/performance_baseline.json')
"

:: Generate Health Summary Report
echo.
echo ========================================
echo HEALTH CHECK SUMMARY REPORT
echo ========================================

:: Calculate file system health
set /a FS_HEALTH_SCORE=(%CRITICAL_FILES_FOUND% + %CRITICAL_DIRS_FOUND%) * 100 / (%CRITICAL_FILES_TOTAL% + %CRITICAL_DIRS_TOTAL%)

echo Health Check Results:
echo ----------------------------------------

if %HEALTH_EXIT_CODE% == 0 (
    echo AUTOMATED HEALTH TESTS: PASSED
) else (
    echo AUTOMATED HEALTH TESTS: FAILED ^(Exit code: %HEALTH_EXIT_CODE%^)
)

if %STARTUP_EXIT_CODE% == 0 (
    echo APPLICATION STARTUP: PASSED
) else (
    echo APPLICATION STARTUP: FAILED ^(Exit code: %STARTUP_EXIT_CODE%^)
)

echo FILE SYSTEM HEALTH: %FS_HEALTH_SCORE%%%
if %FS_HEALTH_SCORE% == 100 (
    echo   Status: HEALTHY - All critical files and directories present
) else if %FS_HEALTH_SCORE% GEQ 80 (
    echo   Status: WARNING - Some files/directories missing
) else (
    echo   Status: CRITICAL - Many critical files/directories missing
)

:: Calculate overall health score
set /a OVERALL_HEALTH=0
if %HEALTH_EXIT_CODE% == 0 set /a OVERALL_HEALTH+=25
if %STARTUP_EXIT_CODE% == 0 set /a OVERALL_HEALTH+=25
if %FS_HEALTH_SCORE% GEQ 100 set /a OVERALL_HEALTH+=25
if %FS_HEALTH_SCORE% GEQ 80 set /a OVERALL_HEALTH+=15
if %FS_HEALTH_SCORE% GEQ 60 set /a OVERALL_HEALTH+=10
set /a OVERALL_HEALTH+=25

echo.
echo OVERALL SYSTEM HEALTH: %OVERALL_HEALTH%%%
if %OVERALL_HEALTH% GEQ 90 (
    echo Status: EXCELLENT - System is healthy and operating optimally
) else if %OVERALL_HEALTH% GEQ 75 (
    echo Status: GOOD - System is healthy with minor issues
) else if %OVERALL_HEALTH% GEQ 50 (
    echo Status: WARNING - System has significant issues requiring attention
) else (
    echo Status: CRITICAL - System has critical issues requiring immediate attention
)

echo.
echo Health monitoring components checked:
echo - System resources ^(CPU, Memory, Disk^)
echo - File system integrity ^(Critical files and directories^)
echo - Environment configuration ^(Environment variables^)
echo - Application startup and initialization
echo - Database connectivity health
echo - Supervisor framework health
echo - RAG system health
echo - Scheduled task system health
echo.
echo Health reports generated:
echo - System Info: test_results\health\system_info.json
echo - Performance Baseline: test_results\health\performance_baseline.json
echo - Health Test Results: test_results\health\health_check_results.xml
echo.
echo Weekly health check completed on: %date% %time%
echo ========================================

:: Keep window open if run directly
if "%1"=="auto" goto :end
pause

:end
exit /b %OVERALL_HEALTH%