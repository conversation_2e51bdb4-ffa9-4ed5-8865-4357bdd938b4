from imports import *
from asyncpg import Record
from uuid import NAMESPACE_DNS, uuid5
from datetime import datetime

from managers.manager_postgreSQL import PostgreSQLManager
from managers.manager_retrieval import RetrievalManager
from managers.manager_qdrant import QDrantManager

class MeltanoManager:
    _instance = None
    _initialized = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        instance._initialized = True

    @classmethod
    async def RunTap(cls, tap_name: str):
        target = "target-postgres"
        if not Globals.is_docker():
            target += "-local"
        result = etc.helper_functions.call_network_docker("meltano", f"run tap-{tap_name} {target}")

    @classmethod
    async def RunUtility(cls, util_name: str):
        result = etc.helper_functions.call_network_docker("meltano", f"run {util_name}")
        #result = etc.helper_functions.call_network_docker("meltano", f"invoke {util_name}:run")

    @classmethod
    async def ConvertFilesToVectorStore(cls, folder_path, user = None, enable_multimodal: bool = True):
        from os import listdir, path, remove
        from unstructured.partition.auto import partition
        from managers.manager_multimodal import MultimodalManager

        print(f"Starting ConvertFilesToVectorStore for folder: {folder_path}")
        files = listdir(folder_path)
        print(f"Found {len(files)} files to process: {files}")

        total_chunks = 0
        processed_files = 0

        # Setup multimodal manager if enabled
        if enable_multimodal:
            await MultimodalManager.setup()

        for filename in files:
            file_path = path.join(folder_path, filename)
            if path.isfile(file_path):
                try:
                    print(f"Processing file: {filename}")
                    
                    # Generate unique document ID
                    import uuid
                    doc_id = str(uuid.uuid4())
                    
                    if enable_multimodal:
                        # Use multimodal processing
                        print(f"  - Extracting multimodal content...")
                        multimodal_data = await MultimodalManager.extract_multimodal_elements(file_path, doc_id)
                        
                        print(f"  - Found {len(multimodal_data.get('images', []))} images, {len(multimodal_data.get('tables', []))} tables")
                        
                        # Create chunks preserving multimodal context
                        chunked_content = await RetrievalManager.chunk_multimodal_elements(multimodal_data)
                        print(f"  - Created {len(chunked_content)} multimodal-aware chunks")
                        
                    else:
                        # Fallback to original processing
                        unstructured = partition(filename=file_path)
                        
                        clean_text_parts = []
                        for element in unstructured:
                            element_text = str(element).strip()
                            if element_text:
                                clean_text_parts.append(element_text)
                        
                        markdown_content = "\n\n".join(clean_text_parts)
                        chunked_content = await RetrievalManager.chunk_text(data=markdown_content)
                        multimodal_data = {"images": [], "tables": [], "figures": [], "captions": []}

                    # Create metadata for the file
                    import os
                    from datetime import datetime
                    import mimetypes

                    file_stats = os.stat(file_path)
                    file_metadata = {
                        "file_path": file_path,
                        "file_name": filename,
                        "file_type": mimetypes.guess_type(file_path)[0] or "application/octet-stream",
                        "file_size": file_stats.st_size,
                        "total_chunks": len(chunked_content),
                        "processing_mode": "multimodal" if enable_multimodal else "text_only",
                        #"creation_date": datetime.fromtimestamp(file_stats.st_ctime).strftime("%Y-%m-%d"),
                        #"last_modified_date": datetime.fromtimestamp(file_stats.st_mtime).strftime("%Y-%m-%d"),
                        #"source": "meltano_file_processing",
                    }

                    # Store multimodal document in QDrant
                    if enable_multimodal and multimodal_data:
                        print(f"  - Storing multimodal document in vector store...")
                        nodes_created = await QDrantManager.upsert_multimodal(
                            doc_id=doc_id,
                            text_chunks=chunked_content,
                            multimodal_data=multimodal_data,
                            base_metadata=file_metadata
                        )
                        print(f"  - Created {nodes_created} nodes with multimodal metadata")
                    else:
                        # Fallback to traditional processing
                        chunk_ids = []
                        chunks = []
                        chunk_metadatas = []
                        for chunk_index, chunk in enumerate(chunked_content):
                            print(f"  - Processing chunk {chunk_index + 1}/{len(chunked_content)}")
                            # Create unique ID for each chunk
                            chunk_id = str(uuid5(NAMESPACE_DNS, f"{filename}_chunk_{chunk_index}"))

                            # Add chunk-specific metadata
                            chunk_metadata = file_metadata.copy()
                            chunk_metadata["chunk_index"] = chunk_index

                            chunk_ids.append(chunk_id)
                            chunks.append(chunk)
                            chunk_metadatas.append(chunk_metadata)
                        
                        await QDrantManager.upsert_multiple(chunk_ids, chunks, metadatas=chunk_metadatas)

                    remove(file_path)
                    total_chunks += len(chunked_content)
                    processed_files += 1
                    LogFire.log("RETRIEVE", "Bestand succesvol geupload en verwijderd.", f" File: {file_path}. Markdown: {markdown_content}", user)

                except Exception as e:
                    print(f"? Error processing file {filename}: {e}")
                    import traceback
                    traceback.print_exc()
                    # Don't remove the file if there was an error
                    continue

        print(f"?? Processing complete! {processed_files} files processed, {total_chunks} total chunks created.")

    @classmethod
    async def ConvertSQLToVectorStore(cls, user = None):
        # Connect to the database using a direct connection
        await PostgreSQLManager.connect_to_database("meltanodb")
        # Get the table names
        table_names = await PostgreSQLManager.get_table_names("meltanodb")
        # Execute a query to fetch data
        for table_name in table_names:
            raw_data = await PostgreSQLManager.execute_query("meltanodb", "SELECT * FROM public." + table_name)
            #ids, jsons = zip(*raw_data)
            for record_index, json_data in enumerate(raw_data):
                record: Record = json_data
                # Extract clean text from database record
                elements = dict(record)

                # Convert record to clean text format
                text_parts = []
                for key, value in elements.items():
                    if value is not None and str(value).strip():
                        text_parts.append(f"{key}: {value}")

                # Join all parts with newlines for better readability
                markdown_content = "\n".join(text_parts)
                chunked_content = await RetrievalManager.chunk_text(data=markdown_content)

                # Create metadata for the database record
                record_metadata = {
                    "source": "meltano_database_processing",
                    "table_name": table_name,
                    "record_index": record_index,
                    "record_id": str(next(iter(record.values()))),  # Get first value as record ID
                    "data_type": "database_record"
                }

                # Process each chunk separately
                chunk_ids = []
                chunks = []
                chunk_metadatas = []
                for chunk_index, chunk in enumerate(chunked_content):
                    # Create unique ID for each chunk
                    record_id = str(next(iter(record.values())))
                    chunk_id = str(uuid5(NAMESPACE_DNS, f"{table_name}_record_{record_index}_chunk_{chunk_index}_{record_id}"))

                    # Add chunk-specific metadata
                    chunk_metadata = record_metadata.copy()
                    chunk_metadata["chunk_index"] = chunk_index
                    chunk_metadata["total_chunks"] = len(chunked_content)

                    chunk_ids.append(chunk_id)
                    chunks.append(chunk)
                    chunk_metadatas.append(chunk_metadata)
                    #await QDrantManager.upsert(id=chunk_id, data_as_str=chunk, metadata=chunk_metadata)
                await QDrantManager.upsert_multiple(chunk_ids, chunks, metadatas=chunk_metadatas)
            LogFire.log("RETRIEVE", "SQL conversie succesvol.", f" Table_name: {table_name}. Raw data: {raw_data}", user)
        await PostgreSQLManager.close_connection("meltanodb")
