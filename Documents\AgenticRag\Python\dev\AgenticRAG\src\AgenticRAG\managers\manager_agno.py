from imports import *

from agno.agent import Agent, RunResponse
from agno.models.ollama import Ollama
from agno.knowledge.pdf_url import PDFUrlKnowledgeBase
from agno.vectordb.qdrant import Qdrant
from agno.storage.agent.postgres import PostgresAgentStorage
from agno.embedder.fastembed import FastEmbedEmbedder
from agno.knowledge.agent import AgentKnowledge
from langchain_core.messages import AnyMessage

class AgnoManager:
    _instance = None
    _initialized = False
    
    knowledge_base: AgentKnowledge = None
    vector_db: Qdrant = None

# region init
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    async def _init_vector_db(self):
        host = "qdrant" if Globals.is_docker() else "localhost"
        return Qdrant(
            collection="mainCollection",
            url=f"http://{host}:{PORT_QDRANT}",
            embedder=FastEmbedEmbedder(dimensions=EMBEDDING_SIZE),
        )

    async def _init_knowledge_base(self):
        kb = PDFUrlKnowledgeBase(
            urls=["https://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf"],
            vector_db=self.vector_db,
        )
        kb.load(recreate=False)
        return kb

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        #instance.vector_db = await instance._init_vector_db()
        #instance.knowledge_base = await instance._init_knowledge_base()

        # agent = await IntegrationAgno.CreateAgent("You are a Thai cuisine expert!")
        # IntegrationAgno.RunAgent(agent, "test input")

        instance._initialized = True
# endregion

    @classmethod
    async def CreateAgent(cls, prompt: str) -> Agent:
        instance = cls.get_instance()
        if not instance._initialized:
            await cls.setup()
            instance = cls.get_instance()

        host = "pgvector:5432" if Globals.is_docker() else f"localhost:{PORT_POSTGRESQL}"
        return Agent(
            model=Ollama(id=AGENT_MODEL_OLLAMA),
            description=prompt,
            knowledge=instance.knowledge_base,
            storage=PostgresAgentStorage(
                table_name="agent_sessions",
                db_url=f"postgresql+psycopg://ai:ai@{host}/ai"
            ),
            markdown=True,
        )

    @classmethod
    async def RunAgent(cls, agent: Agent, user_input: str) -> str:
        response: RunResponse = agent.run(user_input)
        if isinstance(response, etc.helper_functions.get_any_message_as_type()):
            response = response.content
        return response
