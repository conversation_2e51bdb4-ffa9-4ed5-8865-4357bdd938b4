from imports import *

from asyncio import create_task, sleep, Task
from pydantic import BaseModel
from typing import  Any, Optional
from pydantic import ConfigDict

from userprofiles.ZairaMessage import ZairaMessage

class MyBot_Generic(BaseModel):
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.LongRunningZairaTask import LongRunningZairaTask
    parent_instance: Optional[Any] = None
    name: str = ""
    asyncio_Task: Optional[Task] = None # Python-only

    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, parent_instance, name, **kwargs):
        super().__init__(parent_instance=parent_instance,name=name,asyncio_Task=None,**kwargs)

    async def on_ready(self):
        await self.send_broadcast("Gegroet collega's! Hoe mag ik u vandaag gehoorzamen? Om zeker te zijn dat ik weet dat je het tegen mij hebt hoef je enkel en alleen je berichten met een '!' te beginnen.")

    async def on_message(self, text: str, message):
        pass

    async def on_member_join(self, member_name: str, message):
        from endpoints.teams_endpoint import MyTeamsBot
        from endpoints.discord_endpoint import MyDiscordBot
        from endpoints.whatsapp_endpoint import MyWhatsappBot

        welcome_text = f"Hello and welcome {member_name}!"
        if self.parent_instance == None:
            # Python call
            print("HACKING ATTEMPT! Should never occur!")
        elif isinstance(self.parent_instance, MyTeamsBot):
            await message.send_activity(welcome_text)
        elif isinstance(self.parent_instance, MyDiscordBot):
            await message.create_dm()
            await message.dm_channel.send(welcome_text)
        elif isinstance(self.parent_instance, MyWhatsappBot):
            # For WhatsApp, message would be the phone number
            await MyWhatsappBot.send_a_whatsapp_message(message, welcome_text)

    async def request_human_in_the_loop_internal(self, request:str, task: "LongRunningZairaTask", message, halt_until_response = False):
        if self.name == "Python":
            result = input(request)
            await task.user.on_message(result, self, [], task.original_physical_message)
        elif self.name == "Discord":
            # # If wait is requested
            # def check(m):
            #     # Check if the reply is from the same user in the same channel
            #     return m.author == message.author and m.channel == message.channel

            # async def handle_query():
            #     try:
            #         result = await MyDiscordBot.bot.wait_for('message', check=check, timeout=360)
            #         #await self.send_reply(f'You replied: "{reply.content}" within 6 minutes! Nice!', message)
            #         #callback(result) is handled inside start_task(), so simply ignore the result and return the function
            #     except TimeoutError:
            #         await self.send_reply(f'{message.author.mention}, time\’s up! You didn\’t reply within 6 minutes.', message)
            # MyDiscordBot.bot.loop.create_task(handle_query())

            await self.send_reply(request, task, message)
        elif self.name == "Teams":
            await self.send_reply(request, task, message)
        elif self.name == "Whatsapp":
            await self.send_reply(request, task, message)
        if halt_until_response == True:
            while True:
                await sleep(1)
                if task.human_in_the_loop_callback == None:
                    break
    
    async def send_broadcast(self, text):
        if self.name == "Python":
            print(text)
        elif self.name == "Discord":
            from endpoints.discord_endpoint import MyDiscordBot
            await MyDiscordBot.send_discord_broadcast(text)
        elif self.name == "Teams":
            from endpoints.teams_endpoint import MyTeamsBot
            await MyTeamsBot.send_teams_broadcast(text)

    async def send_reply(self, text: str, task: "LongRunningZairaTask", physical_message, add_to_chat_history = True):
        async def split_and_process_text(text, callback, max_length=2000, min_last_chunk_length=500):
            from re import split as re_split

            # Split only on dots followed by a space or end of line, keeping the dot
            sentences = re_split(r'(?<=\.)\s+', text)

            chunks = []
            current_chunk = ""

            for sentence in sentences:
                if len(current_chunk) + len(sentence) + 1 <= max_length:
                    current_chunk += sentence + " "
                else:
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence + " "

            # Add the last chunk
            if current_chunk.strip():
                chunks.append(current_chunk.strip())

            # Rebalance the last two chunks if the last one is too small
            if len(chunks) >= 2 and len(chunks[-1]) < min_last_chunk_length:
                last = chunks.pop()
                prev = chunks.pop()

                merged = prev + ' ' + last
                if len(merged) <= max_length:
                    chunks.append(merged.strip())
                else:
                    # Re-split the merged chunk safely within max_length
                    split_point = merged.rfind(' ', 0, max_length)
                    if split_point == -1:
                        # If no space found, force split at max_length
                        chunks.append(merged[:max_length].strip())
                        chunks.append(merged[max_length:].strip())
                    else:
                        chunks.append(merged[:split_point].strip())
                        chunks.append(merged[split_point:].strip())

            # Ensure no chunk exceeds max_length
            final_chunks = []
            for chunk in chunks:
                while len(chunk) > max_length:
                    split_point = chunk.rfind(' ', 0, max_length)
                    if split_point == -1:
                        # No good split point, force cut
                        final_chunks.append(chunk[:max_length].strip())
                        chunk = chunk[max_length:].strip()
                    else:
                        final_chunks.append(chunk[:split_point].strip())
                        chunk = chunk[split_point:].strip()
                if chunk:
                    final_chunks.append(chunk)

            # Process chunks with callback
            for chunk in final_chunks:
                await callback(chunk)
        if self.name == "Python":
            async def wait_for_no_task(self: "MyBot_Generic", text):
                while True:
                   # Wait with outputting to the Python log until there's nothing being logged anymore
                   await sleep(1)
                   if task.user.my_task == None:
                       break
                print(text)
                print("")
            self.asyncio_Task = create_task(wait_for_no_task(self, text))
            self.asyncio_Task.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
        elif self.name == "Teams":
            from endpoints.teams_endpoint import MyTeamsBot
            await MyTeamsBot.send_a_teams_message(physical_message, text)
        elif self.name == "Discord":
            await split_and_process_text(text, callback=(lambda c: physical_message.reply(c)), max_length=2000, min_last_chunk_length=500)
        elif self.name == "Whatsapp":
            from endpoints.whatsapp_endpoint import MyWhatsappBot
            # For WhatsApp, physical_message contains the sender's phone number
            sender_id = physical_message
            await split_and_process_text(text, callback=(lambda c: MyWhatsappBot.send_a_whatsapp_message(sender_id, c)), max_length=1000, min_last_chunk_length=200)

        if add_to_chat_history:
            task.user.chat_history[task.user.sessionGUID].append(ZairaMessage.create_assistant_message(text, task.user.conversationGUID, task.user.sessionGUID, len(text)))
