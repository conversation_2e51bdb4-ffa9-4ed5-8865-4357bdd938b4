# from imports import *

# from langchain_core.tools import tool
# import logging
# import smtplib
# import base64
# from typing import Optional
# from datetime import datetime, timedelta
# import asyncio
# import re
# import pytz

# from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState, SupervisorTask_Base
# from endpoints.oauth._verifier_ import OAuth2Verifier
# from managers.manager_users import ZairaUserManager
# from userprofiles.LongRunningZairaTask import LongRunningZairaTask

# import json
# from googleapiclient.discovery import build
# from google.oauth2.credentials import Credentials





# @tool
# async def datechecker_tool(date_str: str, time_str: Optional[str] = None, state: Optional[SupervisorTaskState] = None):
#     """Enhanced tool for checking and parsing dates and times with comprehensive Dutch support. Supports:
#     - Parsing dates in various formats (YYYY-MM-DD, DD-MM-YYYY, YYYY/MM/DD)
#     - Dutch date formats like '8 juli', '15 december 2024', '3 jan'
#     - Handling relative dates (today/vandaag, tomorrow/morgen, overmorgen)
#     - Dutch day names (maandag, dinsdag, etc.)
#     - Parsing time in various formats (24h HH:MM, 12h with am/pm)
#     - Handling relative times (in X hours/uur)
#     Returns dates in YYYY/MM/DD format with Amsterdam timezone."""
    
#     if not state:
#         logging.error("No state provided to datechecker_tool")
#         return "Error: No state provided"
        
#     if not hasattr(state, 'user_guid'):
#         logging.error("No user_guid found in state")
#         return "Error: No user_guid found in state"

#     user = await ZairaUserManager.find_user(state.user_guid)
#     if not user:
#         logging.error(f"No user found for guid: {state.user_guid}")
#         return "Error: User not found"

#     # Dutch month names mapping
#     DUTCH_MONTHS = {
#         'januari': 1, 'jan': 1,
#         'februari': 2, 'feb': 2,
#         'maart': 3, 'mrt': 3, 'mar': 3,
#         'april': 4, 'apr': 4,
#         'mei': 5,
#         'juni': 6, 'jun': 6,
#         'juli': 7, 'jul': 7,
#         'augustus': 8, 'aug': 8,
#         'september': 9, 'sep': 9, 'sept': 9,
#         'oktober': 10, 'okt': 10, 'oct': 10,
#         'november': 11, 'nov': 11,
#         'december': 12, 'dec': 12
#     }
    
#     # Dutch day names mapping
#     DUTCH_DAYS = {
#         'maandag': 0, 'ma': 0,
#         'dinsdag': 1, 'di': 1,
#         'woensdag': 2, 'wo': 2,
#         'donderdag': 3, 'do': 3,
#         'vrijdag': 4, 'vr': 4, 'vrij': 4,
#         'zaterdag': 5, 'za': 5, 'zat': 5,
#         'zondag': 6, 'zo': 6
#     }
    
#     def parse_dutch_date(date_text: str, current_year: int = None) -> datetime:
#         """Parse Dutch date formats like '8 juli', '15 december 2024', etc."""
#         if current_year is None:
#             current_year = datetime.now().year
            
#         date_text = date_text.lower().strip()
        
#         # Pattern for "day month" or "day month year"
#         # Matches: "8 juli", "15 december 2024", "3 jan", etc.
#         pattern = r'(\d{1,2})\s+([a-z]+)(?:\s+(\d{4}))?'
#         match = re.match(pattern, date_text)
        
#         if match:
#             day = int(match.group(1))
#             month_name = match.group(2)
#             year = int(match.group(3)) if match.group(3) else current_year
            
#             if month_name in DUTCH_MONTHS:
#                 month = DUTCH_MONTHS[month_name]
#                 try:
#                     return datetime(year, month, day)
#                 except ValueError:
#                     # Invalid date (e.g., Feb 30)
#                     raise ValueError(f"Ongeldige datum: {day} {month_name} {year}")
        
#         # Pattern for "month day" format
#         # Matches: "juli 8", "december 15", etc.
#         pattern2 = r'([a-z]+)\s+(\d{1,2})(?:\s+(\d{4}))?'
#         match2 = re.match(pattern2, date_text)
        
#         if match2:
#             month_name = match2.group(1)
#             day = int(match2.group(2))
#             year = int(match2.group(3)) if match2.group(3) else current_year
            
#             if month_name in DUTCH_MONTHS:
#                 month = DUTCH_MONTHS[month_name]
#                 try:
#                     return datetime(year, month, day)
#                 except ValueError:
#                     raise ValueError(f"Ongeldige datum: {day} {month_name} {year}")
        
#         raise ValueError(f"Kon datum niet herkennen: {date_text}")
    
#     def find_next_weekday(weekday: int) -> datetime:
#         """Find the next occurrence of a weekday (0=Monday, 6=Sunday)"""
#         today = datetime.now()
#         days_ahead = weekday - today.weekday()
#         if days_ahead <= 0:  # Target day already happened this week
#             days_ahead += 7
#         return today + timedelta(days=days_ahead)

#     try:
#         # Parse the date and time input
#         now = datetime.now()
#         result_date = now
        
#         # Handle date part
#         date_str = date_str.lower().strip()
        
#         # Handle relative dates (Dutch and English)
#         if date_str in ['today', 'vandaag']:
#             result_date = now
#         elif date_str in ['tomorrow', 'morgen']:
#             result_date = now + timedelta(days=1)
#         elif date_str in ['day after tomorrow', 'overmorgen']:
#             result_date = now + timedelta(days=2)
#         elif date_str in ['yesterday', 'gisteren']:
#             result_date = now - timedelta(days=1)
#         # Handle Dutch weekday names
#         elif date_str in DUTCH_DAYS:
#             weekday = DUTCH_DAYS[date_str]
#             result_date = find_next_weekday(weekday)
#         else:
#             # Try Dutch date format first (e.g., "8 juli", "15 december 2024")
#             try:
#                 result_date = parse_dutch_date(date_str, now.year)
#                 parsed = True
#             except ValueError:
#                 # Try standard numeric date formats
#                 formats = ['%Y-%m-%d', '%d-%m-%Y', '%Y/%m/%d', '%d/%m/%Y', '%d-%m', '%d/%m']
#                 parsed = False
#                 for fmt in formats:
#                     try:
#                         if fmt in ['%d-%m', '%d/%m']:  # Add current year for day-month formats
#                             result_date = datetime.strptime(f"{date_str}-{now.year}", f"{fmt}-%Y")
#                         else:
#                             result_date = datetime.strptime(date_str, fmt)
#                         parsed = True
#                         break
#                     except ValueError:
#                         continue
                        
#                 if not parsed:
#                     # Ask the user for a valid date if parsing failed
#                     async def handle_date_input(task: LongRunningZairaTask, response: str):
#                         nonlocal parsed, result_date
#                         try:
#                             # Try Dutch date parsing first
#                             try:
#                                 result_date = parse_dutch_date(response, now.year)
#                                 parsed = True
#                                 return True
#                             except ValueError:
#                                 pass
                            
#                             # Try standard formats
#                             for fmt in formats:
#                                 try:
#                                     if fmt in ['%d-%m', '%d/%m']:
#                                         result_date = datetime.strptime(f"{response}-{now.year}", f"{fmt}-%Y")
#                                     else:
#                                         result_date = datetime.strptime(response, fmt)
#                                     parsed = True
#                                     return True
#                                 except ValueError:
#                                     continue
#                             if not parsed:
#                                 return False
#                         except Exception:
#                             return False

#                     await user.my_task.request_human_in_the_loop(
#                         "Ik begrijp de datum niet. Probeer een van deze formaten:\n"
#                         "- Nederlandse datum: '8 juli', '15 december 2024', '3 jan'\n"
#                         "- Numerieke datum: 2024-01-31, 31-01-2024\n"
#                         "- Relatieve datum: 'vandaag', 'morgen', 'overmorgen'\n"
#                         "- Weekdag: 'maandag', 'dinsdag', 'vrijdag'\n", 
#                         handle_date_input,
#                         True
#                     )
                    
#                     if not parsed:
#                         return {
#                             'error': True,
#                             'message': ("Fout: Kon datum niet begrijpen. Gebruik een van deze formaten:\n"
#                                        "- Nederlandse datum: '8 juli', '15 december 2024', '3 jan'\n"
#                                        "- Numerieke datum: 2024-01-31, 31-01-2024\n"
#                                        "- Relatieve datum: 'vandaag', 'morgen', 'overmorgen'\n"
#                                        "- Weekdag: 'maandag', 'dinsdag', 'vrijdag'")
#                         }

#         # Handle time if provided
#         if time_str:
#             time_str = time_str.lower().strip()
            
#             # Handle relative times in Dutch and English
#             if any(word in time_str for word in ['hours', 'uur', 'minuten', 'minutes']):
#                 try:
#                     # Extract number from string
#                     numbers = re.findall(r'\d+', time_str)
#                     if numbers:
#                         value = int(numbers[0])
#                         if 'hours' in time_str or 'uur' in time_str:
#                             result_date = result_date + timedelta(hours=value)
#                         elif 'minutes' in time_str or 'minuten' in time_str or 'minuut' in time_str:
#                             result_date = result_date + timedelta(minutes=value)
#                     else:
#                         raise ValueError("No number found in relative time")
#                 except ValueError:
#                     return {
#                         'error': True,
#                         'message': "Fout: Kon relatieve tijd niet begrijpen. Geef het aantal uren of minuten (bijv. '3 uur', '30 minuten')"
#                     }
#             else:
#                 # Handle absolute time formats
#                 is_pm = 'pm' in time_str
#                 time_str = time_str.replace('am', '').replace('pm', '').strip()
                
#                 # Handle Dutch time indicators
#                 if "'s ochtends" in time_str or "ochtend" in time_str:
#                     time_str = re.sub(r"'s ochtends|ochtend", '', time_str).strip()
#                     # Morning time (AM)
#                 elif "'s middags" in time_str or "middag" in time_str:
#                     time_str = re.sub(r"'s middags|middag", '', time_str).strip()
#                     is_pm = True  # Afternoon (PM)
#                 elif "'s avonds" in time_str or "avond" in time_str:
#                     time_str = re.sub(r"'s avonds|avond", '', time_str).strip()
#                     is_pm = True  # Evening (PM)
#                 elif "'s nachts" in time_str or "nacht" in time_str:
#                     time_str = re.sub(r"'s nachts|nacht", '', time_str).strip()
#                     # Night time (late PM or early AM)
                
#                 # If only hour is provided, add minutes
#                 if ':' not in time_str and '.' not in time_str:
#                     time_str += ':00'
                
#                 # Handle Dutch decimal time format (e.g., "14.30")
#                 time_str = time_str.replace('.', ':')
                
#                 try:
#                     # Parse hour and minutes
#                     parts = time_str.split(':')
#                     hour = int(parts[0])
#                     minute = int(parts[1]) if len(parts) > 1 else 0
                    
#                     # Handle 12-hour format conversion
#                     if is_pm and hour < 12:
#                         hour += 12
#                     elif not is_pm and hour == 12:
#                         hour = 0
                        
#                     # Validate hours and minutes
#                     if not (0 <= hour <= 23) or not (0 <= minute <= 59):
#                         raise ValueError("Invalid hours/minutes")
                        
#                     result_date = result_date.replace(hour=hour, minute=minute, second=0, microsecond=0)
#                 except (ValueError, IndexError):
#                     return {
#                         'error': True,
#                         'message': ("Fout: Kon tijd niet begrijpen. Gebruik een van deze formaten:\n"
#                                    "- 24-uurs formaat: 14:30, 09:00, 14.30\n"
#                                    "- 12-uurs formaat: 2:30pm, 9am\n"
#                                    "- Alleen het uur: 14, 9pm\n"
#                                    "- Nederlandse aanduiding: '2 uur 's middags', '9 uur 's ochtends'")
#                     }

#         # Format the result with forward slashes
#         date_part = result_date.strftime('%Y/%m/%d')
#         time_part = result_date.strftime('%H:%M')
        
#         # Create Dutch date description for user feedback
#         dutch_months = [
#             'januari', 'februari', 'maart', 'april', 'mei', 'juni',
#             'juli', 'augustus', 'september', 'oktober', 'november', 'december'
#         ]
#         dutch_days = ['maandag', 'dinsdag', 'woensdag', 'donderdag', 'vrijdag', 'zaterdag', 'zondag']
        
#         day_name = dutch_days[result_date.weekday()]
#         month_name = dutch_months[result_date.month - 1]
#         dutch_date = f"{day_name} {result_date.day} {month_name} {result_date.year}"
        
#         return {
#             'date': date_part,
#             'time': time_part,
#             'full_datetime': result_date.isoformat(),
#             'timezone': 'Europe/Amsterdam',
#             'dutch_description': dutch_date,
#             'message': f"Gevonden datum en tijd: {dutch_date} om {time_part} (Europa/Amsterdam)"
#         }
    
#     except Exception as e:
#         logging.error(f"Date parsing error: {e}")
#         return {
#             'error': True,
#             'message': (f"Fout bij het verwerken van datum/tijd: {str(e)}\n"
#                        f"Gebruik standaard datumformaten zoals:\n"
#                        f"- Nederlandse datum: '8 juli', '15 december 2024'\n"
#                        f"- Numerieke datum: 2024-01-31, 31-01-2024\n"
#                        f"- Tijd: 14:30, 2:30pm, '2 uur 's middags'")
#         }



  

# @tool
# async def calendar_tool(action: str, calendar_id: Optional[str] = 'primary', event_details: Optional[dict] = None, max_capacity: Optional[int] = 50, state: Optional[SupervisorTaskState] = None):
#     """Calendar tool for managing Google Calendar events and calendars. Supports:
#     - list_calendar_list: Lists available calendars
#     - list_calendar_events: Lists events from a calendar
#     - create_calendar_list: Creates a new calendar
#     - insert_calendar_event: Creates a new event"""
    
#     if not state:
#         logging.error("No state provided to calendar_tool")
#         return "Error: No state provided"
        
#     if not hasattr(state, 'user_guid'):
#         logging.error("No user_guid found in state")
#         return "Error: No user_guid found in state"

#     user = await ZairaUserManager.find_user(state.user_guid)
#     if not user:
#         logging.error(f"No user found for guid: {state.user_guid}")
#         return "Error: User not found"

#     class CalendarClient:
#         """Handles calendar operations through Google Calendar API"""
#         bearer_token: str = ""
#         refresh_token: str = ""

#         async def init_client(self):
#             try:
#                 # Get OAuth tokens with timeout handling
#                 self.bearer_token = await asyncio.wait_for(
#                     OAuth2Verifier.get_token("gcalendar"),
#                     timeout=30.0
#                 )
#                 self.refresh_token = await asyncio.wait_for(
#                     OAuth2Verifier.get_token("gcalendar", "refresh_token"),
#                     timeout=30.0
#                 )

#                 if not self.bearer_token:
#                     await user.my_task.request_human_in_the_loop(
#                         "Google Calendar is nog niet verbonden. Wil je dit nu doen? (ja/nee)", 
#                         callback=self.handle_oauth_request, 
#                         halt_until_response=True
#                     )
#                     return False

#                 # Set up Google Calendar client            
#                 from google.oauth2.credentials import Credentials
#                 from googleapiclient.discovery import build

#                 # Get the OAuth2App instance for gcalendar
#                 gcalendar_app = OAuth2Verifier.get_instance().apps["gcalendar"]
                    
#                 token_info = {
#                     "client_id": gcalendar_app.client_id,
#                     "client_secret": gcalendar_app.client_secret,
#                     "refresh_token": self.refresh_token,
#                     "token_uri": gcalendar_app.token_url,
#                     "access_token": self.bearer_token,
#                     "expires_in": await OAuth2Verifier.get_token("gcalendar", "expires_in"),
#                     "scopes": gcalendar_app.scopes
#                 }

#                 self.service = build("calendar", "v3", credentials=Credentials.from_authorized_user_info(token_info))
#                 return True
#             except asyncio.TimeoutError:
#                 await user.my_task.send_response(
#                     "Time-out bij het verbinden met Google Calendar. Probeer het later opnieuw.", 
#                     False
#                 )
#                 return False
#             except Exception as e:
#                 await user.my_task.send_response(
#                     f"Fout bij het initialiseren van de calendar client: {str(e)}", 
#                     False
#                 )
#                 return False

#         async def handle_oauth_request(self, task: LongRunningZairaTask, response: str):
#             if response.lower().startswith("y"):
#                 # Redirect user to OAuth flow
#                 await task.send_response("I'll redirect you to the Google OAuth consent screen. Please follow the instructions there.")
#                 # Start OAuth flow
#                 await OAuth2Verifier.setup()
#                 return True
#             else:
#                 await task.send_response("Calendar functions will not be available without Google Calendar access.")
#                 return False

#         async def list_calendars(self, max_results: int = 100) -> list:
#             """Get list of calendars"""
#             try:
#                 calendar_list = self.service.calendarList().list(maxResults=max_results).execute()
#                 return calendar_list.get('items', [])
#             except Exception as e:
#                 return []

#         async def list_events(self, calendar_id: str = 'primary', max_results: int = 20, time_min: str = None, time_max: str = None, state: Optional[SupervisorTaskState] = None) -> list:
#             """Get list of events from a calendar with optional time range"""
#             try:
#                 params = {
#                     'calendarId': calendar_id,
#                     'maxResults': max_results,
#                     'orderBy': 'startTime',
#                     'singleEvents': True,
#                 }
#                 if time_min:
#                     params['timeMin'] = time_min
#                 if time_max:
#                     params['timeMax'] = time_max

#                 events_result = self.service.events().list(**params).execute()
#                 events_list = events_result.get('items', [])
                
#                 # Log for debugging
#                 logging.info(f"Retrieved {len(events_list)} events from calendar {calendar_id}")
                
#                 return events_list
#             except Exception as e:
#                 logging.error(f"Failed to list events from calendar {calendar_id}: {str(e)}")
#                 await user.my_task.send_response(f"Failed to list events: {e}")
#                 return []

#         async def create_calendar(self, summary: str) -> dict:
#             """Create a new calendar"""
#             try:
#                 calendar = {
#                     'summary': summary,
#                     'timeZone': 'Europe/Amsterdam'
#                 }
#                 created_calendar = self.service.calendars().insert(body=calendar).execute()
#                 return (f"Calendar created: {created_calendar}")
#             except Exception as e:
#                 await user.my_task.send_response(f"Failed to create calendar: {e}")
#                 return "error: Failed to create calendar"

     

#         async def create_event(self, calendar_id: str, event: dict) -> dict:
#             """Create an event in a calendar"""
#             try:
#                 # Ensure the event has proper timezone information
#                 if 'start' in event and 'dateTime' in event['start']:
#                     event['start']['timeZone'] = 'Europe/Amsterdam'
#                 if 'end' in event and 'dateTime' in event['end']:
#                     event['end']['timeZone'] = 'Europe/Amsterdam'
                
#                 created_event = self.service.events().insert(calendarId=calendar_id, body=event).execute()
#                 return (f"Event created: {created_event})")
#             except Exception as e:
#                 await user.my_task.send_response(f"Failed to create event: {e}")
#                 return "error: Failed to create event"

#         async def format_events_response(self, events: list, is_dutch: bool = True) -> str:
#             """Format events list into a readable response"""
#             if not events:
#                 return "Geen afspraken gevonden." if is_dutch else "No appointments found."
            
#             response = []
#             for event in events:
#                 start = event.get('start', {}).get('dateTime', event.get('start', {}).get('date'))
#                 if start:
#                     try:
#                         start_dt = datetime.fromisoformat(start.replace('Z', '+00:00'))
#                         time_str = start_dt.strftime('%H:%M')
#                         date_str = start_dt.strftime('%Y-%m-%d')
#                     except:
#                         time_str = "??:??"
#                         date_str = start
#                 else:
#                     time_str = "??:??"
#                     date_str = "Unknown"

#                 summary = event.get('summary', 'Geen titel' if is_dutch else 'No title')
#                 location = event.get('location', '')
#                 location_str = f" @ {location}" if location else ""
                
#                 response.append(f"- {date_str} {time_str}: {summary}{location_str}")
            
#             return "\n".join(response)

#     calendar_client = CalendarClient()
#     if not await calendar_client.init_client():
#         return "Error: Could not initialize calendar client. Please make sure Google Calendar is connected."    
#     if action == 'list_calendar_list':
#         calendars = await calendar_client.list_calendars(max_capacity)
#         if isinstance(calendars, list):
#             if not calendars:
#                 return "No calendars found."
#             calendar_list = []
#             for calendar in calendars:
#                 summary = calendar.get('summary', 'Unknown Calendar')
#                 calendar_id = calendar.get('id', 'unknown')
#                 calendar_list.append(f"- {summary} (ID: {calendar_id})")
#             return "Available calendars:\n" + "\n".join(calendar_list)
#         return str(calendars)   
#     elif action == 'list_calendar_events':
#         # Initialize events variable
#         events = []
        
#         # Handle date-specific queries
#         time_min = None
#         time_max = None
        
#         try:
#             # Check if there's a specific date to filter by
#             if event_details is not None and 'date_filter' in event_details:
#                 date_str = event_details['date_filter']
#                 time_str = event_details.get('time_filter', None)
                
#                 # Use datechecker_tool to parse the date
#                 date_result = await datechecker_tool(date_str, time_str, state)
#                 if isinstance(date_result, dict) and not date_result.get('error'):
#                     # Parse the date to get start and end of day
#                     from datetime import datetime
#                     import pytz
                    
#                     # Convert the parsed date to datetime
#                     parsed_date = datetime.fromisoformat(date_result['full_datetime'])
                    
#                     # Set time range for the entire day
#                     amsterdam_tz = pytz.timezone('Europe/Amsterdam')
#                     start_of_day = parsed_date.replace(hour=0, minute=0, second=0, microsecond=0)
#                     end_of_day = parsed_date.replace(hour=23, minute=59, second=59, microsecond=999999)
                    
#                     # Convert to UTC for Google Calendar API
#                     time_min = start_of_day.astimezone(pytz.UTC).isoformat()
#                     time_max = end_of_day.astimezone(pytz.UTC).isoformat()
#                 else:
#                     return f"Error: Kon datum niet verwerken: {date_result.get('message', 'Onbekende fout')}"
            
#             elif event_details is not None and 'time_range' in event_details:
#                 time_range = event_details['time_range']
#                 now = datetime.now()
                
#                 if time_range == 'today':
#                     time_min = datetime(now.year, now.month, now.day).isoformat() + 'Z'
#                     time_max = datetime(now.year, now.month, now.day, 23, 59, 59).isoformat() + 'Z'
#                 elif time_range == 'tomorrow':
#                     tomorrow = now + timedelta(days=1)
#                     time_min = datetime(tomorrow.year, tomorrow.month, tomorrow.day).isoformat() + 'Z'
#                     time_max = datetime(tomorrow.year, tomorrow.month, tomorrow.day, 23, 59, 59).isoformat() + 'Z'
                
#             # Get events from calendar
#             events = await calendar_client.list_events(calendar_id, max_capacity, time_min, time_max, state)
    
#             # Format the response with default Dutch language unless specified
#             return await calendar_client.format_events_response(
#                 events, 
#                 event_details.get('is_dutch', True) if event_details else True
#             )
#         except Exception as e:
#             return f"Error: Er is een fout opgetreden bij het ophalen van de afspraken: {str(e)}"
#     elif action == 'create_calendar_list':
#         if not event_details or 'summary' not in event_details:
#             async def set_calendar_name(task: LongRunningZairaTask, response: str):
#                 nonlocal event_details
#                 event_details = {'summary': response}
#             await user.my_task.request_human_in_the_loop("Wat moet de naam van de nieuwe kalender zijn?", set_calendar_name, True)
#         calendar = await calendar_client.create_calendar(event_details.get('summary', 'New Calendar'))
#         return str(calendar)
#     elif action == 'insert_calendar_event':
#         if not event_details:
#             event_details = {}
            
#         # Check required parameters
#         if 'summary' not in event_details:
#             async def set_event_title(task: LongRunningZairaTask, response: str):
#                 try:
#                     if not response or response.strip() == '':
#                         await task.send_response("Een titel is verplicht. Probeer opnieuw.", False)
#                         return False
#                     event_details['summary'] = response.strip()
#                     return True
#                 except Exception as e:
#                     await task.send_response(f"Er is een fout opgetreden bij het instellen van de titel: {str(e)}", False)
#                     return False
#             await user.my_task.request_human_in_the_loop(
#                 "Wat is de titel van de afspraak? (dit is verplicht)", 
#                 set_event_title, 
#                 True
#             )

#         if 'start' not in event_details:
#             async def set_event_start(task: LongRunningZairaTask, response: str):
#                 try:
#                     # First ask for the date
#                     date_result = await datechecker_tool(response, state=state)
#                     if isinstance(date_result, dict) and date_result.get('error'):
#                         raise ValueError(date_result.get('message', 'Date parsing error'))
                    
#                     # Then ask for the time
#                     async def set_event_time(task: LongRunningZairaTask, time_response: str):
#                         try:
#                             # Let datechecker_tool handle all time parsing
#                             time_result = await datechecker_tool(date_result['date'], time_response, state)
#                             if isinstance(time_result, dict) and time_result.get('error'):
#                                 raise ValueError(time_result.get('message', 'Time parsing error'))
                            
#                             event_details['start'] = {
#                                 'dateTime': time_result['full_datetime'],
#                                 'timeZone': time_result['timezone']
#                             }
#                             return True
#                         except Exception as e:
#                             await user.my_task.send_response(
#                                 "Ongeldige tijd. Je kunt zeggen:\n"
#                                 "- 24-uurs formaat (14:30 of 09:00)\n"
#                                 "- 12-uurs formaat (2:30pm of 9am)\n"
#                                 "- Alleen het uur (14 of 9pm)", False)
#                             return False

#                     await user.my_task.request_human_in_the_loop(
#                         "Hoe laat begint de afspraak?\n"
#                         "Je kunt zeggen:\n"
#                         "- 24-uurs formaat (14:30 of 09:00)\n"
#                         "- 12-uurs formaat (2:30pm of 9am)\n"
#                         "- Alleen het uur (14 of 9pm)", set_event_time, True)

#                 except Exception as e:
#                     await user.my_task.send_response(
#                         "Ongeldige datum. Je kunt zeggen:\n"
#                         "- Een datum (2024-01-31 of 31-01-2024)\n"
#                         "- 'vandaag' of 'today'\n"
#                         "- 'morgen' of 'tomorrow'\n"
#                         "- 'overmorgen' of 'day after tomorrow'", False)
#                     return False
            
#             await user.my_task.request_human_in_the_loop("Op welke dag is de afspraak? (bijvoorbeeld: 'vandaag', 'morgen', of '2025-06-11')", set_event_start, True)
            
#         if 'end' not in event_details:           
#             async def set_event_duration(task: LongRunningZairaTask, response: str):
#                 try:
#                     # Extract number of minutes from response
#                     duration = 0
#                     if 'uur' in response.lower():
#                         hours = int(''.join(filter(str.isdigit, response)))
#                         duration = hours * 60
#                     elif 'minuten' in response.lower() or 'minuut' in response.lower():
#                         duration = int(''.join(filter(str.isdigit, response)))
#                     else:
#                         # Default to interpreting as minutes
#                         duration = int(''.join(filter(str.isdigit, response)))
                    
#                     # Calculate end time based on start time
#                     start_dt = datetime.fromisoformat(event_details['start']['dateTime'])
#                     end_dt = start_dt + timedelta(minutes=duration)
                    
#                     event_details['end'] = {
#                         'dateTime': end_dt.isoformat(),
#                         'timeZone': 'Europe/Amsterdam'
#                     }
#                 except Exception as e:
#                     await user.my_task.send_response("Ongeldige duur. Geef een aantal minuten of uren (bijvoorbeeld: '30 minuten' of '2 uur')", False)
#             await user.my_task.request_human_in_the_loop("Voor hoe lang moet ik de afspraak inplannen? (bijvoorbeeld: '30 minuten' of '2 uur')", set_event_duration, True)

#         if 'location' not in event_details:
#             async def set_event_location(task: LongRunningZairaTask, response: str):
#                 if response.lower() not in ['nee', 'n', 'skip']:
#                     event_details['location'] = response
#             await user.my_task.request_human_in_the_loop("Wat is de locatie van de afspraak? (typ 'nee' om over te slaan)", set_event_location, True)

#         if 'description' not in event_details:
#             async def set_event_description(task: LongRunningZairaTask, response: str):
#                 if response.lower() not in ['nee', 'n', 'skip']:
#                     event_details['description'] = response
#             await user.my_task.request_human_in_the_loop("Geef een beschrijving voor de afspraak: (typ 'nee' om over te slaan)", set_event_description, True)

#         if 'attendees' not in event_details:
#             async def set_event_attendees(task: LongRunningZairaTask, response: str):
#                 if response.lower() not in ['nee', 'n', 'skip']:
#                     # Split comma-separated email addresses
#                     emails = [e.strip() for e in response.split(',')]
#                     event_details['attendees'] = [{'email': email} for email in emails if '@' in email]

#             await user.my_task.request_human_in_the_loop("Wie zijn de deelnemers? Geef email adressen, gescheiden door komma's: (typ 'nee' om over te slaan)", set_event_attendees, True)

#      
#         # Use a shared result storage to coordinate between callback and main flow
#         result_container = {"message": None, "completed": False}
        
#         # Define the confirmation callback  
#         async def confirm_and_create_event(task: LongRunningZairaTask, response: str):
#             try:
#                 if response and response[0].lower() == 'j':
#                     event = await calendar_client.create_event(calendar_id, event_details)
#                     if event and not event.startswith("error:"):
#                         result_container["message"] = "Afspraak is succesvol aangemaakt en staat nu in je agenda!"
#                         result_container["completed"] = True
#                         return True
#                     else:
#                         result_container["message"] = "Er is een fout opgetreden bij het aanmaken van de afspraak."
#                         result_container["completed"] = True
#                         return False
#                 else:
#                     result_container["message"] = "Afspraak aanmaken geannuleerd door gebruiker."
#                     result_container["completed"] = True
#                     return False
#             except Exception as e:
#                 result_container["message"] = f"Er is een fout opgetreden: {str(e)}"
#                 result_container["completed"] = True
#                 return False

#         # Request confirmation from user
#         await user.my_task.request_human_in_the_loop(
#             f"Wil je deze afspraak aanmaken? (j/n)\n\n{event_preview}", 
#             confirm_and_create_event,
#             True
#         )

#         # Wait for the callback to complete and return the appropriate message
#         timeout_counter = 0
#         while not result_container["completed"] and timeout_counter < 300:  # 5 minute timeout
#             await asyncio.sleep(1)
#             timeout_counter += 1
            
#         if result_container["completed"]:
#             return result_container["message"]
#         else:
#             return "Timeout: Er is geen reactie ontvangen binnen 5 minuten."
#     else:
#         return f"Error: Unknown action '{action}'. Supported actions: list_calendar_list, list_calendar_events, create_calendar_list, insert_calendar_event"

# # class SupervisorTask_AgendaPlanner(SupervisorTask_Base):
# #     async def llm_call(self, state: SupervisorTaskState):
# #         """Execute the current task"""
        
# #         return ""



# async def create_supervisor_agenda_planner() -> SupervisorSupervisor:
#     class TaskCreator:
#         agenda_task: SupervisorTask_SingleAgent = None
#         datechecker_task: SupervisorTask_SingleAgent = None

#         async def create_tasks(self):
#             # Create datechecker task first
#             self.datechecker_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(
#                 name="datechecker_task", 
#                 tools=[datechecker_tool],
#                 prompt="""You are a helpful agent equipped with an advanced datechecker function that understands Dutch date and time formats.
                
#                 The datechecker_tool supports comprehensive Dutch date parsing including:
#                 - Dutch date formats: '8 juli', '15 december 2024', '3 jan'
#                 - Dutch weekdays: 'maandag', 'dinsdag', 'vrijdag'
#                 - Relative dates: 'vandaag', 'morgen', 'overmorgen', 'gisteren'
#                 - Standard numeric formats: YYYY-MM-DD, DD-MM-YYYY
#                 - Dutch time formats: '14.30', "'s middags", "'s ochtends"
#                 - Time parsing: 24h (14:30), 12h (2:30pm), relative (3 uur)

               
               
                
#                 Examples:
#                 - Input: "8 juli om 2 uur 's middags" → datechecker_tool("8 juli", "2 uur 's middags", state=state)
#                 - Input: "morgen 14:30" → datechecker_tool("morgen", "14:30", state=state)
#                 - Input: "vrijdag" → datechecker_tool("vrijdag", None, state=state)
                
#                 The tool returns structured data that can be passed to the calendar expert for event creation."""
#             ))

#             # Register the agenda task with updated prompt to handle the new datechecker response format
#             self.agenda_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(
#                 name="agenda_expert", 
#                 tools=[calendar_tool], 
#                 prompt="""You are a helpful agent equipped with Google calendar functions. Always ensure you have proper state information before making calendar operations.
#                 You can understand and respond to queries in both English and Dutch.

#                 For calendar management requests:
#                 1. List calendars: calendar_tool(action='list_calendar_list', state=state)
#                 2. List events (general): calendar_tool(action='list_calendar_events', calendar_id='primary', state=state)
#                 3. List events for specific date: list_events_for_date_tool(date_str='vandaag', state=state)
#                 4. Create calendar: calendar_tool(action='create_calendar_list', state=state)
#                 5. Create event: calendar_tool(action='insert_calendar_event', calendar_id='primary', state=state)

#                 IMPORTANT: 
                
#                 - After performing actions, provide a clear summary of what was done"""
                
#             ))

#         async def create_supervisor(self) -> SupervisorSupervisor:
#             return SupervisorManager.register_supervisor(SupervisorSupervisor(
#                 name="agenda_supervisor",
#                 prompt="""You are an intelligent agenda and calendar management assistant that coordinates between date parsing and calendar operations.

                 
#                 When you receive a date/time request:
#                 1. Extract the date and time components from the user input
#                 2. Call datechecker_tool(date_str, time_str, state=state)
#                 3. Return the complete result including the dutch_description field
#                 4. Use this result as input for the agenda_expert task.
             
                
#                 Calander creation tasks can be handled directly by the agenda_expert.
#                 Processing Examples:
                
#                 - "Plan een afspraak op 8 juli om 2 uur" → datechecker_task first, then agenda_expert
#                 - "Laat mijn kalenders zien" → agenda_expert directly
                
#                 Always ensure clear, actionable results are returned.
#                 Only use every task ONCE. This is very important. """
#             )) \
#             .add_task(self.datechecker_task, priority=1)\
#             .add_task(self.agenda_task, priority=2)\
#             .compile()

#     creator = TaskCreator()
#     await creator.create_tasks()
#     return await creator.create_supervisor()
