from imports import *


from llama_index.core.query_engine import BaseQueryEngine
from langchain_core.messages import  HumanMessage, SystemMessage
from typing import TYPE_CHECKING

from managers.manager_prompts import PromptManager

if TYPE_CHECKING:
    # This only runs for type checkers, not at runtime — safe to "reach inside"
    from userprofiles.ZairaUser import <PERSON>air<PERSON><PERSON><PERSON>

# Create the llama_index OpenAI LLM instance
# llm = OpenAI(
#     model="gpt-4-mini",
#     temperature=0,
#     max_tokens=2000
# )
# reranker = LLMRerank(top_n=5, llm=llm)

async def rag_data(query_engine: BaseQueryEngine, user_input: str, user: "ZairaUser" = None) -> str:
    """Perform RAG search using the provided query engine"""
    try:
        print(f"[DEBUG] Starting retrieve_data for: {user_input}")
        # filters = MetadataFilters(filters=[])
        # # Configure the query engine with advanced settings
        # configured_engine = query_engine.as_query_engine(
        #     filters=filters,
        #     similarity_top_k=10,
        #     sparse_top_k=10,
        #     vector_store_query_mode="hybrid",
        #     node_postprocessors=[
        #         LLMRerank(top_n=5)
        #     ]
        # )
        # Use aquery for async operation
        #response = await configured_engine.aquery(user_input)
        print(f"[DEBUG] About to call query_engine.aquery...")
        chat_history = user.chat_history[user.sessionGUID] if user else []
        input = ""
        for chat in chat_history:
            input += f"{chat.role}: {chat.content}\n"
        input += f"user: {user_input}\nassistant:"
        response = await query_engine.aquery(f"{PromptManager.get_prompt('AskZaira_Prompt')}. Prompt: {PromptManager.get_prompt('Quick_RAG_Search')}. User query: {input}")
        print(f"[DEBUG] Query completed, response: {response}")
        return f"{response}"
    except Exception as e:
        print(f"[DEBUG] Error in retrieve_data: {e}")
        etc.helper_functions.exception_triggered(e)
        return f"An error occurred: {e}"

async def llm_data(user_input: str, user: "ZairaUser" = None) -> str:
    """Perform generic search using the provided query engine"""
    try:
        chat_history = user.get_chat_history() if user else []
        messages = chat_history
        messages.insert(0, SystemMessage(PromptManager.get_prompt("AskZaira_Prompt") + ". Prompt: " + PromptManager.get_prompt("Quick_LLM_Search")))
        messages.append(HumanMessage(user_input))
        result = await ZairaSettings.llm.ainvoke(
            input=messages
        )
        if isinstance(result, etc.helper_functions.get_any_message_as_type()):
            result = result.content
        return result#Command(update={"messages":result})
    except Exception as e:
        etc.helper_functions.exception_triggered(e)

async def complexity_data(user_input: str, user: "ZairaUser" = None) -> int:
    return 1
