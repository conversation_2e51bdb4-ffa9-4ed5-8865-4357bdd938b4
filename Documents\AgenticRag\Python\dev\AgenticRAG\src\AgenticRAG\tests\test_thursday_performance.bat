@echo off
echo ========================================
echo THURSDAY PERFORMANCE TESTING - AgenticRAG
echo ========================================
echo Running comprehensive performance test suite...
echo.

:: Set up environment
set PYTHONPATH=%CD%\src
set CLAUDE_CODE=1
set ANTHROPIC_USER_ID=test-claude

:: Install/upgrade testing dependencies
echo Installing performance testing dependencies...
..\..\..\.venv\Scripts\python.exe -m pip install pytest pytest-asyncio pytest-mock psutil --quiet

:: Create test results directory
if not exist "test_results" mkdir test_results
if not exist "test_results\performance" mkdir test_results\performance

:: Get system information for baseline
echo.
echo ========================================
echo SYSTEM PERFORMANCE BASELINE
echo ========================================
..\..\..\.venv\Scripts\python.exe -c "
import psutil
import platform
print(f'CPU: {platform.processor()}')
print(f'CPU Cores: {psutil.cpu_count()} logical, {psutil.cpu_count(logical=False)} physical')
print(f'Memory: {psutil.virtual_memory().total / (1024**3):.1f} GB total')
print(f'Available Memory: {psutil.virtual_memory().available / (1024**3):.1f} GB')
print(f'Python Version: {platform.python_version()}')
print(f'Platform: {platform.system()} {platform.release()}')
"

:: Run performance tests
echo.
echo ========================================
echo RUNNING PERFORMANCE TESTS
echo ========================================

:: RAG System Performance Tests
echo.
echo Running RAG System Performance Tests...
echo ----------------------------------------
..\..\..\.venv\Scripts\python.exe -m pytest tests\performance\test_rag_performance.py ^
    --junit-xml=test_results\performance\rag_performance_results.xml ^
    -v ^
    --tb=short ^
    -s

set RAG_EXIT_CODE=%ERRORLEVEL%

:: Database Performance Tests
echo.
echo Running Database Performance Tests...
echo ----------------------------------------
..\..\..\.venv\Scripts\python.exe -m pytest tests\performance\test_database_performance.py ^
    --junit-xml=test_results\performance\database_performance_results.xml ^
    -v ^
    --tb=short ^
    -s

set DB_EXIT_CODE=%ERRORLEVEL%

:: Supervisor Performance Tests
echo.
echo Running Supervisor Framework Performance Tests...
echo ----------------------------------------
..\..\..\.venv\Scripts\python.exe -m pytest tests\performance\test_supervisor_performance.py ^
    --junit-xml=test_results\performance\supervisor_performance_results.xml ^
    -v ^
    --tb=short ^
    -s

set SUPERVISOR_EXIT_CODE=%ERRORLEVEL%

:: Memory and Resource Monitoring
echo.
echo Running System Resource Monitoring...
echo ----------------------------------------
..\..\..\.venv\Scripts\python.exe -c "
import psutil
import time
import json

print('Collecting system resource metrics...')
metrics = []

for i in range(10):
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('.')
    
    metric = {
        'timestamp': time.time(),
        'cpu_percent': cpu_percent,
        'memory_percent': memory.percent,
        'memory_available_gb': memory.available / (1024**3),
        'disk_free_gb': disk.free / (1024**3)
    }
    metrics.append(metric)
    print(f'Sample {i+1}/10: CPU: {cpu_percent:.1f}%%, Memory: {memory.percent:.1f}%%, Available: {memory.available / (1024**3):.1f} GB')

# Save metrics
with open('test_results/performance/system_metrics.json', 'w') as f:
    json.dump(metrics, f, indent=2)

print('System metrics saved to test_results/performance/system_metrics.json')
"

:: Generate performance summary
echo.
echo ========================================
echo PERFORMANCE TEST SUMMARY
echo ========================================

if %RAG_EXIT_CODE% == 0 (
    echo RAG PERFORMANCE TESTS: PASSED
) else (
    echo RAG PERFORMANCE TESTS: FAILED ^(Exit code: %RAG_EXIT_CODE%^)
)

if %DB_EXIT_CODE% == 0 (
    echo DATABASE PERFORMANCE TESTS: PASSED
) else (
    echo DATABASE PERFORMANCE TESTS: FAILED ^(Exit code: %DB_EXIT_CODE%^)
)

if %SUPERVISOR_EXIT_CODE% == 0 (
    echo SUPERVISOR PERFORMANCE TESTS: PASSED
) else (
    echo SUPERVISOR PERFORMANCE TESTS: FAILED ^(Exit code: %SUPERVISOR_EXIT_CODE%^)
)

:: Calculate overall result
set /a OVERALL_EXIT_CODE=%RAG_EXIT_CODE% + %DB_EXIT_CODE% + %SUPERVISOR_EXIT_CODE%

echo.
if %OVERALL_EXIT_CODE% == 0 (
    echo OVERALL STATUS: ALL PERFORMANCE TESTS PASSED
    echo System performance meets all benchmarks!
) else (
    echo OVERALL STATUS: SOME PERFORMANCE TESTS FAILED
    echo Please review the output above for performance issues.
)

echo.
echo Performance test components:
echo - RAG System: Query response time, embedding generation, concurrent queries
echo - Database: PostgreSQL connections, CRUD operations, Qdrant vector ops
echo - Supervisor Framework: Task routing, chain-of-thought, parallel execution
echo - Memory Usage: Resource consumption, cleanup, leak detection
echo - Concurrent Load: Multi-user simulation, connection pooling
echo.
echo Performance reports:
echo - RAG Performance: test_results\performance\rag_performance_results.xml
echo - Database Performance: test_results\performance\database_performance_results.xml
echo - Supervisor Performance: test_results\performance\supervisor_performance_results.xml
echo - System Metrics: test_results\performance\system_metrics.json
echo.
echo Performance Benchmarks:
echo - RAG Query Response: ^< 3.0s ^(target: ^< 1.0s^)
echo - Database Operations: ^< 1.0s per operation
echo - Task Routing: ^< 0.5s per routing decision
echo - Memory Usage: ^< 50MB increase per 50 operations
echo - Concurrent Load: Support 10+ simultaneous users
echo.
echo Run time: %date% %time%
echo ========================================

:: Keep window open if run directly
if "%1"=="auto" goto :end
pause

:end
exit /b %OVERALL_EXIT_CODE%