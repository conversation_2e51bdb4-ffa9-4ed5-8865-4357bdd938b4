"""
Performance tests for supervisor framework and multi-agent coordination
"""
import pytest
import asyncio
import time
import psutil
import os
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.performance
@pytest.mark.asyncio
class TestSupervisorPerformance:
    """Test supervisor framework performance"""
    
    async def test_task_routing_performance(self, mock_database_connections):
        """Test supervisor task routing performance"""
        from managers.manager_supervisors import SupervisorSupervisor, SupervisorTaskState
        from langchain_core.messages import HumanMessage
        
        # Create a mock supervisor for performance testing
        supervisor = SupervisorSupervisor(name="test_routing_supervisor")
        
        # Routing performance benchmarks
        MAX_ROUTING_TIME = 0.5  # seconds
        NUM_ROUTING_TESTS = 50
        
        routing_scenarios = [
            "schedule a task to check email every hour",
            "tell me about your capabilities",
            "list my scheduled tasks", 
            "find information about database operations",
            "cancel my scheduled task with ID 123",
            "explain how the supervisor system works",
            "search for documents about OAuth authentication",
            "create a backup task for daily execution"
        ]
        
        routing_times = []
        
        for i in range(NUM_ROUTING_TESTS):
            # Use different scenarios cyclically
            user_input = routing_scenarios[i % len(routing_scenarios)]
            
            state = SupervisorTaskState(
                conversation_history=[HumanMessage(content=user_input)],
                reasoning_steps=[],
                current_task='routing_test'
            )
            
            # Mock the routing decision using a simple method
            start_time = time.time()
            
            # Simulate routing logic
            def mock_routing(state):
                return 'appropriate_task'
            
            routed_task = mock_routing(state)
            end_time = time.time()
            
            routing_time = end_time - start_time
            routing_times.append(routing_time)
            
            assert routing_time < MAX_ROUTING_TIME, f"Routing too slow: {routing_time:.3f}s"
            assert routed_task == 'appropriate_task', "Routing failed"
        
        # Performance summary
        avg_routing_time = sum(routing_times) / len(routing_times)
        max_routing_time = max(routing_times)
        min_routing_time = min(routing_times)
        
        print(f"\nSupervisor Routing Performance:")
        print(f"Average routing time: {avg_routing_time:.3f}s")
        print(f"Maximum routing time: {max_routing_time:.3f}s")
        print(f"Minimum routing time: {min_routing_time:.3f}s")
        print(f"Routings per second: {1/max(avg_routing_time, 0.0001):.1f}")
        
        return {
            'avg_routing_time': avg_routing_time,
            'max_routing_time': max_routing_time,
            'min_routing_time': min_routing_time,
            'routings_per_second': 1/max(avg_routing_time, 0.0001)
        }
    
    async def test_chain_of_thought_performance(self, mock_database_connections):
        """Test chain-of-thought reasoning performance"""
        from managers.manager_supervisors import SupervisorTask_ChainOfThought, SupervisorTaskState
        from langchain_core.messages import HumanMessage
        
        class TestCoTTask(SupervisorTask_ChainOfThought):
            def __init__(self):
                super().__init__(name="test_cot_task")
                
            async def llm_call(self, state: SupervisorTaskState) -> SupervisorTaskState:
                # Simulate chain-of-thought reasoning
                reasoning_steps = [
                    "Step 1: Analyze user input and intent",
                    "Step 2: Identify required information and context",
                    "Step 3: Determine appropriate response strategy",
                    "Step 4: Generate comprehensive response",
                    "Step 5: Validate response accuracy and completeness"
                ]
                
                for step in reasoning_steps:
                    state.reasoning_steps.append(step)
                    await asyncio.sleep(0.01)  # Simulate processing time
                
                # Add result to sections instead of direct assignment
                from managers.manager_supervisors import SupervisorSection
                state.sections['cot_result'] = SupervisorSection.from_values('cot_result', 'reasoning_complete')
                return state
        
        # CoT performance benchmarks
        MAX_COT_TIME = 2.0  # seconds
        NUM_COT_TESTS = 20
        
        cot_task = TestCoTTask()
        cot_times = []
        
        for i in range(NUM_COT_TESTS):
            state = SupervisorTaskState(
                conversation_history=[
                    HumanMessage(content=f"Complex reasoning task {i} requiring detailed analysis")
                ],
                reasoning_steps=[],
                current_task='cot_performance_test'
            )
            
            start_time = time.time()
            result_state = await cot_task.llm_call(state)
            end_time = time.time()
            
            cot_time = end_time - start_time
            cot_times.append(cot_time)
            
            assert cot_time < MAX_COT_TIME, f"CoT reasoning too slow: {cot_time:.3f}s"
            assert len(result_state.reasoning_steps) == 5, "Incomplete reasoning steps"
            assert 'cot_result' in result_state.sections, "CoT task failed"
        
        # Performance summary
        avg_cot_time = sum(cot_times) / len(cot_times)
        max_cot_time = max(cot_times)
        
        print(f"\nChain-of-Thought Performance:")
        print(f"Average CoT time: {avg_cot_time:.3f}s")
        print(f"Maximum CoT time: {max_cot_time:.3f}s")
        print(f"CoT processes per minute: {60/avg_cot_time:.1f}")
        
        return {
            'avg_cot_time': avg_cot_time,
            'max_cot_time': max_cot_time,
            'cot_per_minute': 60/avg_cot_time
        }
    
    async def test_parallel_task_execution_performance(self, mock_database_connections):
        """Test parallel supervisor task execution performance"""
        from managers.manager_supervisors import SupervisorTask_Base, SupervisorTaskState
        
        class ParallelTestTask(SupervisorTask_Base):
            def __init__(self, task_id: str, processing_time: float = 0.1):
                super().__init__(name=f"parallel_test_task_{task_id}")
                self.task_id = task_id
                self.processing_time = processing_time
            
            async def llm_call(self, state: SupervisorTaskState) -> SupervisorTaskState:
                await asyncio.sleep(self.processing_time)  # Simulate processing
                from managers.manager_supervisors import SupervisorSection
                state.sections[f'task_{self.task_id}_result'] = SupervisorSection.from_values(f'task_{self.task_id}_result', 'completed')
                state.sections[f'task_{self.task_id}_time'] = SupervisorSection.from_values(f'task_{self.task_id}_time', str(self.processing_time))
                return state
        
        # Parallel execution parameters
        NUM_PARALLEL_TASKS = 10
        TASK_PROCESSING_TIME = 0.2  # seconds each
        MAX_PARALLEL_TIME = TASK_PROCESSING_TIME * 1.5  # Should be much faster than sequential
        
        # Create parallel tasks
        tasks = [ParallelTestTask(f'task_{i}', TASK_PROCESSING_TIME) for i in range(NUM_PARALLEL_TASKS)]
        
        initial_state = SupervisorTaskState(
            conversation_history=[],
            reasoning_steps=[],
            current_task='parallel_performance_test'
        )
        
        # Test parallel execution
        start_time = time.time()
        
        task_coroutines = [task.llm_call(initial_state.model_copy()) for task in tasks]
        results = await asyncio.gather(*task_coroutines)
        
        end_time = time.time()
        parallel_time = end_time - start_time
        
        # Calculate sequential time for comparison
        sequential_time = NUM_PARALLEL_TASKS * TASK_PROCESSING_TIME
        
        print(f"\nParallel Task Execution Performance:")
        print(f"Number of parallel tasks: {NUM_PARALLEL_TASKS}")
        print(f"Individual task time: {TASK_PROCESSING_TIME:.2f}s")
        print(f"Parallel execution time: {parallel_time:.2f}s")
        print(f"Sequential would take: {sequential_time:.2f}s")
        print(f"Speedup factor: {sequential_time / parallel_time:.1f}x")
        print(f"Efficiency: {(sequential_time / parallel_time) / NUM_PARALLEL_TASKS * 100:.1f}%")
        
        # Performance assertions
        assert parallel_time < MAX_PARALLEL_TIME, f"Parallel execution too slow: {parallel_time:.2f}s"
        assert parallel_time < sequential_time * 0.8, "Parallel execution not efficient enough"
        
        # Verify all tasks completed
        for i, result in enumerate(results):
            assert f'task_task_{i}_result' in result.sections, f"Task {i} did not complete"
            assert result.sections[f'task_task_{i}_result'].get_description() == 'completed', f"Task {i} failed"
        
        return {
            'parallel_time': parallel_time,
            'sequential_time': sequential_time,
            'speedup_factor': sequential_time / parallel_time,
            'efficiency_percent': (sequential_time / parallel_time) / NUM_PARALLEL_TASKS * 100
        }

@pytest.mark.performance
@pytest.mark.asyncio  
class TestMemoryEfficiencyPerformance:
    """Test supervisor framework memory efficiency"""
    
    async def test_supervisor_memory_usage(self, mock_database_connections):
        """Test memory usage during supervisor operations"""
        from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        class MemoryTestTask(SupervisorTask_Base):
            def __init__(self, name: str):
                super().__init__(name=name)
            async def llm_call(self, state: SupervisorTaskState) -> SupervisorTaskState:
                # Create some temporary data
                temp_data = {
                    'large_list': list(range(1000)),
                    'conversation_copy': state.conversation_history.copy(),
                    'reasoning_copy': state.reasoning_steps.copy()
                }
                
                # Process data
                processed_data = {
                    'sum': sum(temp_data['large_list']),
                    'conversation_count': len(temp_data['conversation_copy']),
                    'reasoning_count': len(temp_data['reasoning_copy'])
                }
                
                from managers.manager_supervisors import SupervisorSection
                state.sections['memory_test_result'] = SupervisorSection.from_values('memory_test_result', str(processed_data))
                
                # Clean up temporary data
                del temp_data
                
                return state
        
        # Register and execute memory test task
        manager = SupervisorManager.get_instance()
        memory_test_task_instance = MemoryTestTask(name="memory_test_task")
        manager.register_task(memory_test_task_instance)
        
        memory_task = MemoryTestTask(name="memory_task_instance")
        memory_measurements = []
        
        # Execute multiple operations to test memory usage
        for i in range(50):
            state = SupervisorTaskState(
                conversation_history=[],
                reasoning_steps=[],
                current_task=f'memory_test_{i}'
            )
            
            await memory_task.llm_call(state)
            
            # Measure memory every 10 operations
            if i % 10 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = current_memory - initial_memory
                memory_measurements.append(memory_increase)
                
                print(f"Operation {i:2d}: Memory: {current_memory:.1f} MB (+{memory_increase:.1f} MB)")
        
        # Final memory measurement
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_memory_increase = final_memory - initial_memory
        
        print(f"\nSupervisor Memory Usage:")
        print(f"Initial memory: {initial_memory:.1f} MB")
        print(f"Final memory: {final_memory:.1f} MB")
        print(f"Total increase: {total_memory_increase:.1f} MB")
        print(f"Max increase during test: {max(memory_measurements):.1f} MB")
        
        # Memory efficiency assertions
        assert total_memory_increase < 20, f"Memory usage too high: +{total_memory_increase:.1f} MB"
        assert max(memory_measurements) < 30, f"Peak memory usage too high: +{max(memory_measurements):.1f} MB"
        
        return {
            'initial_memory': initial_memory,
            'final_memory': final_memory,
            'total_increase': total_memory_increase,
            'max_increase': max(memory_measurements)
        }
    
    async def test_state_management_performance(self, mock_database_connections):
        """Test supervisor state management performance"""
        from managers.manager_supervisors import SupervisorTaskState
        from langchain_core.messages import HumanMessage, AIMessage
        
        # State management performance benchmarks
        MAX_STATE_OPERATION_TIME = 0.01  # seconds
        NUM_STATE_OPERATIONS = 1000
        
        # Create large conversation history for testing
        large_conversation = []
        for i in range(100):
            large_conversation.append(HumanMessage(content=f"User message {i}"))
            large_conversation.append(AIMessage(content=f"AI response {i}"))
        
        state_operation_times = []
        
        for i in range(NUM_STATE_OPERATIONS):
            # Test state creation
            start_time = time.time()
            
            state = SupervisorTaskState(
                conversation_history=large_conversation.copy(),
                reasoning_steps=[f"Reasoning step {j}" for j in range(20)],
                current_task=f'state_test_{i}'
            )
            
            # Test state modification
            from managers.manager_supervisors import SupervisorSection
            state.sections['test_data'] = SupervisorSection.from_values('test_data', f"operation_{i}_{time.time()}")
            state.reasoning_steps.append(f"Additional reasoning {i}")
            
            # Test state access
            conversation_count = len(state.conversation_history)
            reasoning_count = len(state.reasoning_steps)
            
            end_time = time.time()
            operation_time = end_time - start_time
            state_operation_times.append(operation_time)
            
            # Performance assertion
            assert operation_time < MAX_STATE_OPERATION_TIME, f"State operation too slow: {operation_time:.4f}s"
            
            # Correctness assertions
            assert conversation_count == 200, "Conversation history corrupted"
            assert reasoning_count == 21, "Reasoning steps corrupted"
        
        # Performance summary
        avg_state_time = sum(state_operation_times) / len(state_operation_times)
        max_state_time = max(state_operation_times)
        
        print(f"\nState Management Performance:")
        print(f"Average state operation time: {avg_state_time:.4f}s")
        print(f"Maximum state operation time: {max_state_time:.4f}s")
        print(f"State operations per second: {1/avg_state_time:.0f}")
        
        return {
            'avg_state_time': avg_state_time,
            'max_state_time': max_state_time,
            'ops_per_second': 1/avg_state_time
        }