from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from managers.manager_users import ZairaUserManager

class SupervisorTask_Teams(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)
        if user.my_task.calling_bot.name == "Teams":
            await user.my_task.send_response(input_message)
            LogFire.log("OUTPUT", "Teams:", input_message)

async def create_out_task_teams() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_Teams(name="teams_out", prompt_id="Output_Sender_Teams"))
