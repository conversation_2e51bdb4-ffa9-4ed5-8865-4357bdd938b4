# from imports import *

# from langchain_core.tools import tool
# import logging
# import smtplib
# import base64
# from typing import Optional
# from datetime import datetime, timedelta
# import asyncio
# import re
# import pytz

# from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState, SupervisorTask_Base
# from endpoints.oauth._verifier_ import OAuth2Verifier
# from managers.manager_users import ZairaUserManager
# from userprofiles.LongRunningZairaTask import LongRunningZairaTask

# import json
# from googleapiclient.discovery import build
# from google.oauth2.credentials import Credentials


# def create_event_dict(title: str, date: str, time: str, duration: int = 1) -> dict:
#     """
#     Helper function to create a properly formatted event_details dictionary.
    
#     Args:
#         title: Event title/summary
#         date: Date in format DD/MM/YYYY or YYYY-MM-DD
#         time: Time in format HH:MM or HH:MMam/pm
#         duration: Duration in hours (default: 1)
    
#     Returns:
#         dict: Properly formatted event_details dictionary
#     """
#     try:
#         # Parse date - handle both DD/MM/YYYY and YYYY-MM-DD formats
#         if '/' in date:
#             if len(date.split('/')[2]) == 4:  # DD/MM/YYYY
#                 day, month, year = date.split('/')
#                 formatted_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"
#             else:  # YYYY/MM/DD
#                 formatted_date = date.replace('/', '-')
#         else:
#             formatted_date = date  # Already in YYYY-MM-DD format
        
#         # Parse time - handle am/pm and 24h formats
#         time_str = time.lower().strip()
#         is_pm = 'pm' in time_str
#         is_am = 'am' in time_str
        
#         # Remove am/pm indicators
#         time_str = time_str.replace('am', '').replace('pm', '').strip()
        
#         # Handle decimal format (11.30 -> 11:30)
#         time_str = time_str.replace('.', ':')
        
#         # Add minutes if only hour provided
#         if ':' not in time_str:
#             time_str += ':00'
        
#         # Parse hour and minute
#         parts = time_str.split(':')
#         hour = int(parts[0])
#         minute = int(parts[1]) if len(parts) > 1 else 0
        
#         # Convert to 24h format
#         if is_pm and hour < 12:
#             hour += 12
#         elif is_am and hour == 12:
#             hour = 0
        
#         # Create start datetime
#         start_datetime = f"{formatted_date}T{hour:02d}:{minute:02d}:00"
        
#         # Calculate end datetime
#         start_dt = datetime.fromisoformat(start_datetime)
#         end_dt = start_dt + timedelta(hours=duration)
#         end_datetime = end_dt.strftime("%Y-%m-%dT%H:%M:%S")
        
#         # Return properly formatted event_details
#         return {
#             'summary': title,
#             'start': {
#                 'dateTime': start_datetime,
#                 'timeZone': 'Europe/Amsterdam'
#             },
#             'end': {
#                 'dateTime': end_datetime,
#                 'timeZone': 'Europe/Amsterdam'
#             }
#         }
#     except Exception as e:
#         raise ValueError(f"Error creating event dict: {e}")


# @tool
# def create_event_details(title: str, date: str, time: str, duration: int = 1) -> dict:
#     """Create a properly formatted event_details dictionary for calendar events.
    
#     Args:
#         title: Event title/summary
#         date: Date in format DD/MM/YYYY or YYYY-MM-DD  
#         time: Time in format HH:MM or HH:MMam/pm
#         duration: Duration in hours (default: 1)
    
#     Returns:
#         dict: Properly formatted event_details dictionary ready for calendar_tool
#     """
#     return create_event_dict(title, date, time, duration)


# @tool
# def get_the_date(query:str, state: SupervisorTaskState):
#     """You are an English to Dutch translator"""
#     task = SupervisorManager.get_task("check_language")
#     result = task.model.invoke(f"Verify the message: {query}"
#                                "If extract the date and return it in the format 'YYYY-MM-DD'. If not, return 'NO_DATE_FOUND'")
#     if isinstance(result, etc.helper_functions.get_any_message_as_type()):
#         result = result.content
#     return result


# @tool
# async def calendar_tool(action: str, state: SupervisorTaskState = None, **kwargs):
#     """Performs calendar operations based on the specified action."""
#     LogFire.log("TASK", "calendar_tool", f"calendar_tool called with action: {action}, kwargs: {kwargs}")
    
#     if state is None:
#         raise ValueError("State parameter is required but was None")
    
#     task = SupervisorManager.get_task("agenda_task")
#     user_guid = state.user_guid
    
#     if not user_guid:
#         raise ValueError("user_guid is required but was None or empty")
        
#     user = await ZairaUserManager.find_user(user_guid)
    
#     try:
#         if action == "list_calendars":
#             LogFire.log("TASK", "calendar_tool", "Executing list_calendars")
#             result = await list_calendar_list(kwargs.get('max_capacity', 50))
#             LogFire.log("TASK", "calendar_tool", f"list_calendars result: {result}")
#             return result
#         elif action == "list_events":
#             LogFire.log("TASK", "calendar_tool", "Executing list_events")
#             result = await list_calandar_events(kwargs.get('calendar_id'), kwargs.get('max_capacity', 20))
#             LogFire.log("TASK", "calendar_tool", f"list_events result: {result}")
#             return result
#         elif action == "create_calendar":
#             LogFire.log("TASK", "calendar_tool", f"Executing create_calendar with summary: {kwargs.get('summary')}")
#             result = await create_calendar(kwargs.get('summary'))
#             LogFire.log("TASK", "calendar_tool", f"create_calendar result: {result}")
#             return result
#         elif action == "create_event":
#             LogFire.log("TASK", "calendar_tool", "Executing create_event")
#             calendar_id = kwargs.get('calendar_id', 'primary')  # Default to primary calendar
#             event_details = kwargs.get('event_details')
#             if not event_details:
#                 return """Error: event_details parameter is required for create_event action.

# You must provide event_details as a dictionary with this structure:
# event_details = {
#     'summary': 'Event Title',
#     'start': {
#         'dateTime': 'YYYY-MM-DDTHH:MM:SS',
#         'timeZone': 'Europe/Amsterdam'
#     },
#     'end': {
#         'dateTime': 'YYYY-MM-DDTHH:MM:SS',
#         'timeZone': 'Europe/Amsterdam'
#     }
# }

# Example call: calendar_tool(action='create_event', state=state, event_details=event_details)"""
            
#             # Validate event_details structure
#             if not isinstance(event_details, dict):
#                 return f"Error: event_details must be a dictionary, got {type(event_details)}"
            
#             required_fields = ['summary', 'start', 'end']
#             for field in required_fields:
#                 if field not in event_details:
#                     return f"Error: Missing required field '{field}' in event_details"
            
#             # Validate start/end structure
#             for time_field in ['start', 'end']:
#                 if not isinstance(event_details[time_field], dict):
#                     return f"Error: {time_field} must be a dictionary"
#                 if 'dateTime' not in event_details[time_field]:
#                     return f"Error: Missing 'dateTime' in {time_field}"
            
#             result = await insert_calender_event(calendar_id, event_details)
#             LogFire.log("TASK", "calendar_tool", f"create_event result: {result}")
#             return result
#         else:
#             error_msg = f"Error: Unknown action '{action}'. Supported actions: list_calendars, list_events, create_calendar, create_event"
#             LogFire.log("ERROR", "calendar_tool", error_msg)
#             return error_msg
#     except Exception as e:
#         error_msg = f"Error in calendar_tool: {str(e)}"
#         LogFire.log("ERROR", "calendar_tool", error_msg)
#         return error_msg
    
# async def get_calendar_service():
#     # Get the OAuth tokens from the verifier
#     tokens = await OAuth2Verifier.get_full_token("gcalendar")
#     if not tokens:
#         raise Exception("No calendar OAuth tokens found")
    
#     # Create Google credentials from the tokens
#     credentials = Credentials(
#         token=tokens["access_token"],
#         refresh_token=tokens["refresh_token"],
#         token_uri="https://oauth2.googleapis.com/token",
#         client_id=OAuth2Verifier.get_instance().apps["gcalendar"].client_id,
#         client_secret=OAuth2Verifier.get_instance().apps["gcalendar"].client_secret,
#         scopes=OAuth2Verifier.get_instance().apps["gcalendar"].scopes
#     )

#     # Build and return the calendar service
#     calendar_service = build('calendar', 'v3', credentials=credentials)
#     return calendar_service

# async def create_calendar(calendar_name):
#     """
#     Creates a new calendar.

#     Parameters:
#     - calendar_name (str): The name of the new calendar.

#     Returns:
#     - dict: A dictionary containing the ID of the new calendar.
#     """
#     LogFire.log("TASK", "create_calendar", f"create_calendar called with calendar_name: {calendar_name}")
    
#     try:
#         # Get calendar service
#         LogFire.log("TASK", "create_calendar", "Getting calendar service...")
#         calendar_service = await get_calendar_service()
#         LogFire.log("TASK", "create_calendar", "Calendar service obtained successfully")

#         calendar_body = {
#             'summary': calendar_name,
#             'timeZone': 'Europe/Amsterdam'
#         }
        
#         LogFire.log("TASK", "create_calendar", f"Creating calendar with body: {calendar_body}")
#         created_calendar = calendar_service.calendars().insert(body=calendar_body).execute()
#         LogFire.log("TASK", "create_calendar", f"Calendar created successfully: {created_calendar}")
#         return created_calendar
#     except Exception as e:
#         LogFire.log("ERROR", "create_calendar", f"Error in create_calendar: {str(e)}")
#         raise
   
# async def list_calendar_list(max_capacity=200):
#     """
#     Lists calendar lists until the total number of items reaches max_capacity.

#     Parameters:
#     - max_capacity (int or str, optional): The maximum number of calendar lists to retrieve. Defaults to 200.
#       If a string is provided, it will be converted to an integer.

#     Returns:
#     - list: A list of dictionaries containing cleaned calendar list information with 'id', 'name', and 'description'.
#     """
#     if isinstance(max_capacity, str):
#         max_capacity = int(max_capacity)

#     all_calendars = []
#     all_calendars_cleaned = []
#     next_page_token = None
#     capacity_tracker = 0

#     # Get calendar service
#     calendar_service = await get_calendar_service()

#     while True:
#         calendar_list = calendar_service.calendarList().list(
#             maxResults=min(200, max_capacity - capacity_tracker),
#             pageToken=next_page_token
#         ).execute()
#         calendars = calendar_list.get('items', [])
#         all_calendars.extend(calendars)
#         capacity_tracker += len(calendars)
#         if capacity_tracker >= max_capacity:
#             break
#         next_page_token = calendar_list.get('nextPageToken')
#         if not next_page_token:
#             break

#     for calendar in all_calendars:
#         all_calendars_cleaned.append({
#             'id': calendar['id'],
#             'name': calendar['summary'],
#             'description': calendar.get('description', '')
#         })

#     return all_calendars_cleaned

# async def list_calandar_events(calendar_id, max_capacity=20):
#     """
#     Lists events from a specific calendar until the total number of items reaches max_capacity.

#     Parameters:
#     - calendar_id (str): The ID of the calendar to list events from.
#     - max_capacity (int or str, optional): The maximum number of events to retrieve. Defaults to 20.
#       If a string is provided, it will be converted to an integer.

#     Returns:
#     - list: a list of events from the specified calendar.
#     """

#     if isinstance(max_capacity, str):
#         max_capacity = int(max_capacity)

#     all_events = []
#     next_page_token = None
#     capacity_tracker = 0

#     # Get calendar service
#     calendar_service = await get_calendar_service()

#     while True:
#         events = calendar_service.events().list(
#             calendarId=calendar_id,
#             maxResults=min(250, max_capacity - capacity_tracker),
#             pageToken=next_page_token
#         ).execute()
#         events_list = events.get('items', [])
#         all_events.extend(events_list)  # Fixed: should be events_list not events
#         capacity_tracker += len(events_list)  # Fixed: should be events_list not events
#         if capacity_tracker >= max_capacity:
#             break
#         next_page_token = events.get('nextPageToken')
#         if not next_page_token:
#             break

#     return all_events

# async def insert_calender_event(calendar_id, event_details):
#     """
#     Inserts an event into the specified calendar.

#     Parameters:
#     - calendar_id: the ID of the calendar where the event will be inserted.
#     - event_details: Dictionary containing the event details.
#     Returns:
#     - the created event.
#     """
#     # Get calendar service
#     calendar_service = await get_calendar_service()

#     event = calendar_service.events().insert(calendarId=calendar_id, body=event_details).execute()
#     return event



# async def create_supervisor_agenda_planner() -> SupervisorSupervisor:
#     class TaskCreator:
#         agenda_task: SupervisorTask_SingleAgent = None
#         datechecker_task: SupervisorTask_SingleAgent = None
        

#         async def create_tasks(self):
#             # Create datechecker task first
#             self.datechecker_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(
#                 name="datechecker_task", 
#                 tools=[get_the_date],
#                 prompt="""You are a helpful agent equipped with an advanced datechecker function that understands Dutch date and time formats.
#                 Your job is to extract the date and time from the user's input and return it in the format 'YYYY-MM-DD'."""

#                 ))
           
#             # Register the agenda task with updated prompt to handle the new datechecker response format
#             self.agenda_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(
#                 name="agenda_expert", 
#                 tools=[calendar_tool, create_event_details], 
#                 prompt="""You are a calendar expert that creates and manages Google Calendar events. Follow these instructions exactly:

#                 CRITICAL FOR EVENT CREATION:
#                 Use the create_event_details tool to build proper event_details, then pass to calendar_tool.
                
#                 Step-by-step process for event creation:
#                 1. Parse user input to extract: title, date, time
#                 2. Use create_event_details(title, date, time, duration) to build event_details
#                 3. Pass the result to calendar_tool(action='create_event', state=state, event_details=event_details)
                
#                 WORKING EXAMPLE for "create event show time Date 19/06/2025 at 11:30pm":
                
#                 1. First call create_event_details tool:
#                    create_event_details(title="show time", date="19/06/2025", time="11:30pm", duration=1)
                
#                 2. Take the returned dictionary and pass it to calendar_tool:
#                    calendar_tool(action="create_event", state=state, event_details=<result_from_step_1>)
                
#                 CRITICAL: You MUST call create_event_details first, then use its result as event_details parameter.
                
#                 The create_event_details tool handles:
#                 - Date format conversion (DD/MM/YYYY → YYYY-MM-DD)
#                 - Time format conversion (11:30pm → 23:30)
#                 - Proper event_details dictionary structure
#                 - Timezone setting (Europe/Amsterdam)
#                 - End time calculation based on duration

#                 Other Operations:
#                 1. List calendars: calendar_tool(action='list_calendars', state=state)
#                 2. List events: calendar_tool(action='list_events', state=state, calendar_id='primary')
#                 3. Create calendar: calendar_tool(action='create_calendar', state=state, summary='Calendar Name')
                
#                 ALWAYS use create_event_details tool for event creation - never manually build event_details!""",
#             ))

#         async def create_supervisor(self) -> SupervisorSupervisor:
#             return SupervisorManager.register_supervisor(SupervisorSupervisor(
#                 name="agenda_supervisor",
#                 prompt="""You are an agenda and calendar supervisor that actively manages calendars and events. Always delegate to the agenda_expert for any calendar operations.

#                 For ANY calendar-related request, route to the agenda_expert which will:
#                 1. Use calendar_tool for the actual operations
#                 2. Handle proper error checking and responses
                
#                 Specifically for calendar creation requests like "create a calendar named X":
#                 - Route to agenda_expert to use: calendar_tool(action='create_calendar', state=state, summary='X')
                
#                 For event viewing requests:
#                 - Route to agenda_expert to use: calendar_tool(action='list_events', state=state, calendar_id='primary')
                
#                 For calendar listing requests:
#                 - Route to agenda_expert to use: calendar_tool(action='list_calendars', state=state)
                
#                 IMPORTANT:
#                 - Always route calendar operations to agenda_expert
#                 - Never handle calendar operations directly in the supervisor
#                 - The agenda_expert has the calendar_tool and knows how to use it properly"""
#             )) \
#             \
#             .add_task(self.agenda_task, priority=2)\
#             .compile()

#     creator = TaskCreator()
#     await creator.create_tasks()
#     return await creator.create_supervisor()

