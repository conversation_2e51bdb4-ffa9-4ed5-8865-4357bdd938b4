import asyncio
from inputs.crawler import Crawler

async def cmain():
    # Initialize the crawler
    crawler = await Crawler.setup_async()
    
    # Test with a single URL
    await crawler.crawl("https://example.com", is_sitemap=False)
    
    # Test with a sitemap
    # await crawler.crawl("https://example.com/sitemap.xml", is_sitemap=True, max_concurrent=5)
    
    # Clean up
    await crawler.close_crawler()

if __name__ == "__main__":
    asyncio.run(cmain())