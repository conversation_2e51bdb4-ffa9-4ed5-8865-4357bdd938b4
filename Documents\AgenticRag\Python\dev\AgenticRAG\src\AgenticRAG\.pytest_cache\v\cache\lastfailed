{"tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_setup_initialization": true, "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_setup_idempotent": true, "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_infer_column_types": true, "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_extract_table_key_info": true, "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_extract_table_key_info_invalid": true, "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_encode_image_to_base64": true, "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_encode_image_to_base64_nonexistent": true, "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_cleanup_assets": true, "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_get_asset_path": true, "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_save_image_asset": true, "tests/unit/test_multimodal_manager.py::TestMultimodalManagerIntegration::test_extract_multimodal_elements_integration": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_multimodal_chunking_workflow": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_end_to_end_storage_workflow": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_multimodal_search_workflow": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_meltano_multimodal_integration": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_asset_management_workflow": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_error_handling_workflow": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_vision_model_integration": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_table_summarization_workflow": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalPerformance::test_chunking_performance": true}