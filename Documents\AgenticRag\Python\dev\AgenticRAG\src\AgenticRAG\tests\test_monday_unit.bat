@echo off
echo ========================================
echo MONDAY UNIT TESTING - AgenticRAG
echo ========================================
echo Running comprehensive unit test suite...
echo.

:: Set up environment
set PYTHONPATH=%CD%\src
set CLAUDE_CODE=1
set ANTHROPIC_USER_ID=test-claude

:: Install/upgrade testing dependencies
echo Installing testing dependencies...
..\..\..\.venv\Scripts\python.exe -m pip install pytest pytest-asyncio pytest-mock pytest-cov --quiet

:: Create test results directory
if not exist "test_results" mkdir test_results
if not exist "test_results\coverage" mkdir test_results\coverage

:: Run unit tests with coverage
echo.
echo Running unit tests with coverage reporting...
echo ----------------------------------------
..\..\..\.venv\Scripts\python.exe -m pytest tests\unit\ ^
    --cov=src ^
    --cov-report=html:test_results\coverage\html ^
    --cov-report=term-missing ^
    --cov-report=xml:test_results\coverage\coverage.xml ^
    --junit-xml=test_results\unit_test_results.xml ^
    -v ^
    --tb=short ^
    --strict-markers

set TEST_EXIT_CODE=%ERRORLEVEL%

:: Generate test summary
echo.
echo ========================================
echo UNIT TEST SUMMARY
echo ========================================

if %TEST_EXIT_CODE% == 0 (
    echo STATUS: PASSED
    echo All unit tests completed successfully!
    echo.
    echo Coverage report available at: test_results\coverage\html\index.html
    echo JUnit XML report: test_results\unit_test_results.xml
) else (
    echo STATUS: FAILED
    echo Some unit tests failed. Please review the output above.
    echo Exit code: %TEST_EXIT_CODE%
)

echo.
echo Test components covered:
echo - Database operations (PostgreSQL, Qdrant)
echo - Supervisor framework (LangGraph)
echo - RAG system (retrieval, embeddings, OCR)
echo - OAuth authentication and token management
echo.
echo Run time: %date% %time%
echo ========================================

:: Keep window open if run directly
if "%1"=="auto" goto :end
pause

:end
exit /b %TEST_EXIT_CODE%