from imports import *

from aiohttp import web
from os import listdir, path, remove, getcwd

from endpoints.oauth._verifier_ import OAuth2App

class OAuth2GDrive(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_oauth("input", ["https://www.googleapis.com/auth/drive","https://www.googleapis.com/auth/drive.readonly"], GOOGLE_BOT_CLIENT_ID, GOOGLE_BOT_CLIENT_SECRET, \
                                        "https://accounts.google.com/o/oauth2/v2/auth", "https://oauth2.googleapis.com/token") \
                                        .create_input("input", "str:Eerste Google Drive folder om uit te lezen.") \
                                        .set_meltano({"GDRIVE_ACCESS_TOKEN": "access_token",
                                                      "GDRIVE_REFRESH_TOKEN": "refresh_token",
                                                      "GDRIVE_FILE_ID": "str1"})

    async def on_success_return(self, request: web.Request) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return(request)
        ret_html += "Bestanden worden opgehaald. Vraag overige bestanden op via Zaira."
        return ret_html

    async def on_success_execute(self, request: web.Request) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        from managers.manager_meltano import MeltanoManager
        from os import getcwd
        ret_html = await super().on_success_execute(request)
        intermediate = etc.helper_functions.get_value_from_env("GDRIVE_FILE_ID").rsplit("/", 1)
        if len(intermediate) > 1:
            intermediate = intermediate[1]
        intermediate = intermediate.rsplit("?", 1)[0]
        etc.helper_functions.save_to_env({"GDRIVE_FILE_ID": intermediate, "GDRIVE_OUTPUT_PATH": "/meltano/output" if Globals.is_docker() else getcwd() + "/src/meltano/output"})
        await MeltanoManager.RunUtility("gdrive")
        if Globals.is_docker():
            path = '/meltano/output'
        else:
            path = getcwd() + '/src/meltano/output'
        ret_html += f"Map succesvol uitgelezen. {len(listdir(path))} bestanden geconverteerd."
        await MeltanoManager.ConvertFilesToVectorStore(path, None)
        return ret_html
    
    async def on_success_execute_fail(self, request: web.Request) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute(request)
        if Globals.is_docker():
            path = '/meltano/output'
        else:
            path = getcwd() + '/src/meltano/output'
        files = listdir(path)
        for filename in files:
            file_path = path.join(path, filename)
            if path.isfile(file_path):
                    remove(file_path)
        ret_html += "Map uitlezen is fout gegaan. Er zijn geen bestanden opgeslagen in Zaira."
        return ret_html
