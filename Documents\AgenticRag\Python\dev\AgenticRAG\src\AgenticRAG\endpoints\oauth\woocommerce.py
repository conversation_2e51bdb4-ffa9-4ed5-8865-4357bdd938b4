from imports import *

from aiohttp import web

from endpoints.oauth._verifier_ import OAuth2App

class OAuth2Woocommerce(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_input("input", ["str:Consumer Key", "str:Consumer Secret", "str:WooCommerce URL", "str:Data verzamelen vanaf (YYYY-MM-DD)"]) \
                                    .set_meltano({
                                        "TAP_WOOCOMMERCE_CONSUMER_KEY": "access_token",
                                        "TAP_WOOCOMMERCE_CONSUMER_SECRET": "refresh_token",
                                        "TAP_WOOCOMMERCE_SITE_URL": "token_type",
                                        "TAP_WOOCOMMERCE_START_DATE": "str1"
                                    }) \
                                    .set_commands(["extract_woocommerce"])

    async def on_success_return(self, request: web.Request) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return(request)
        ret_html += "Woocommerce koppeling wordt gestart."
        return ret_html

    async def on_success_execute(self, request: web.Request) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        ret_html = await super().on_success_execute(request)
        # Extract WooCommerce data and store in vector database
        print("Starting WooCommerce data extraction")
        try:
            from tasks.inputs.woocommerce_extractor import WooCommerceExtractor
            from endpoints.oauth._verifier_ import OAuth2Verifier

            # Get WooCommerce credentials from stored tokens
            consumer_key = await OAuth2Verifier.get_token("woocommerce", "access_token")
            consumer_secret = await OAuth2Verifier.get_token("woocommerce", "refresh_token")
            site_url = await OAuth2Verifier.get_token("woocommerce", "token_type")
            start_date = await OAuth2Verifier.get_token("woocommerce", "str1")

            from datetime import datetime
            start_date = datetime.strptime(start_date, "%Y-%m-%d")

            print(f"WooCommerce credentials check:")
            print(f"- Consumer Key: {'✓' if consumer_key else '✗'}")
            print(f"- Consumer Secret: {'✓' if consumer_secret else '✗'}")
            print(f"- Site URL: {site_url if site_url else '✗'}")
            print(f"- Start Date: {start_date.date().isoformat() if start_date else '✗'}")

            if consumer_key and consumer_secret and site_url:
                # Initialize the WooCommerce extractor
                extractor = await WooCommerceExtractor.setup_async(
                    consumer_key=consumer_key,
                    consumer_secret=consumer_secret,
                    site_url=site_url,
                    start_date=start_date
                )

                # Extract and process WooCommerce data
                extraction_success = await extractor.extract_and_store_data()

                if extraction_success:
                    print(" WooCommerce data extraction completed successfully")
                else:
                    print(" WooCommerce data extraction failed")
                    print("Check the logs above for detailed error information")
                    # Don't set ret_val = False here as this might be due to Meltano setup issues
                    # The OAuth process itself was successful

                print("Finished WooCommerce data extraction")
            else:
                print("Missing WooCommerce credentials")
                print("Please ensure all required fields are filled:")
                print("- Consumer Key, Consumer Secret, WooCommerce URL, Start Date")
                ret_val = False
        except Exception as e:
            print(f" Error during WooCommerce extraction: {e}")
            import traceback
            traceback.print_exc()
            # Don't set ret_val = False for extraction errors, only for OAuth/credential errors
            print("OAuth process completed, but extraction encountered issues")
        ret_html += ""
        return ret_html
    
    async def on_success_execute_fail(self, request: web.Request) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute(request)
        ret_html += ""
        return ret_html
