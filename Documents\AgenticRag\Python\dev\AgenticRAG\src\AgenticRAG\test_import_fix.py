"""
Simple test to verify the import fix is working
"""

print("🧪 Testing imports...")

try:
    from inputs.user_query import rag_data
    print("✅ rag_data import successful")
except ImportError as e:
    print(f"❌ rag_data import failed: {e}")

try:
    from userprofiles.ZairaUser import ZairaUser
    print("✅ ZairaUser import successful")
except ImportError as e:
    print(f"❌ ZairaUser import failed: {e}")

try:
    from userprofiles.ZairaMessage import ZairaMessage
    print("✅ ZairaMessage import successful")
except ImportError as e:
    print(f"❌ ZairaMessage import failed: {e}")

try:
    from managers.manager_context_preprocessor import preprocess_for_supervisor
    print("✅ Context preprocessor import successful")
except ImportError as e:
    print(f"❌ Context preprocessor import failed: {e}")

print("🎉 Import test completed!")