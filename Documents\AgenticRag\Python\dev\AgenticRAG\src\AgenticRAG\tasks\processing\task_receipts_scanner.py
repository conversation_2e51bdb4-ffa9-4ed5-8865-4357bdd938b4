from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from managers.manager_supervisors import <PERSON><PERSON><PERSON><PERSON><PERSON>, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState
from langchain_core.tools import tool
from langchain_core.messages import AnyMessage
import os
import cv2
import imutils
import pytesseract
from imutils.perspective import four_point_transform
import tempfile
import base64
from typing import Optional

@tool
def scan_receipt_from_path(image_path: str, state: SupervisorTaskState) -> str:
    """
    Scans a receipt from a file path and extracts text using OCR.

    Args:
        image_path: Path to the receipt image file
        state: The current task state

    Returns:
        Extracted text from the receipt
    """
    try:
        # Check if image with given path exists
        if not os.path.exists(image_path):
            return f"Error: The image at path {image_path} does not exist."

        # Process the receipt image
        receipt_text = process_receipt_image(image_path)
        return receipt_text
    except Exception as e:
        return f"Error processing receipt: {str(e)}"

@tool
def scan_receipt_from_base64(image_data: str, state: SupervisorTaskState) -> str:
    """
    Scans a receipt from base64-encoded image data and extracts text using OCR.

    Args:
        image_data: Base64-encoded image data
        state: The current task state

    Returns:
        Extracted text from the receipt
    """
    try:
        # Decode base64 data
        if "base64," in image_data:
            # Handle data URLs (e.g., "data:image/jpeg;base64,...")
            image_data = image_data.split("base64,")[1]

        image_bytes = base64.b64decode(image_data)

        # Save to temporary file
        with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp_file:
            temp_file_path = temp_file.name
            temp_file.write(image_bytes)

        # Process the receipt image
        receipt_text = process_receipt_image(temp_file_path)

        # Clean up temporary file
        os.unlink(temp_file_path)

        return receipt_text
    except Exception as e:
        return f"Error processing receipt: {str(e)}"

def process_receipt_image(image_path: str) -> str:
    """
    Process a receipt image and extract text using OCR.

    Args:
        image_path: Path to the receipt image file

    Returns:
        Extracted text from the receipt
    """
    # Load the image, resize and compute ratio
    img_orig = cv2.imread(image_path)
    image = img_orig.copy()
    image = imutils.resize(image, width=500)
    ratio = img_orig.shape[1] / float(image.shape[1])

    # Convert the image to grayscale, blur it slightly, and then apply edge detection
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    blurred = cv2.GaussianBlur(gray, (5, 5), 0)
    edged = cv2.Canny(blurred, 75, 200)

    # Find contours in the edge map and sort them by size in descending order
    cnts = cv2.findContours(edged.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
    cnts = imutils.grab_contours(cnts)
    cnts = sorted(cnts, key=cv2.contourArea, reverse=True)

    # Initialize a contour that corresponds to the receipt outline
    receipt_cnt = None

    # Loop over the contours
    for c in cnts:
        # Approximate the contour
        peri = cv2.arcLength(c, True)
        approx = cv2.approxPolyDP(c, 0.02 * peri, True)

        # If our approximated contour has four points, then we can
        # assume we have found the outline of the receipt
        if len(approx) == 4:
            receipt_cnt = approx
            break

    # If the receipt contour is empty then our script could not find the outline
    if receipt_cnt is None:
        # If we can't find a receipt outline, just use the original image
        receipt = img_orig
    else:
        # Apply a four-point perspective transform to the original image to
        # obtain a top-down bird's-eye view of the receipt
        receipt = four_point_transform(img_orig, receipt_cnt.reshape(4, 2) * ratio)

    # Apply OCR to the receipt image
    options = "--psm 6"  # Assume a single uniform block of text
    text = pytesseract.image_to_string(
        cv2.cvtColor(receipt, cv2.COLOR_BGR2RGB), config=options
    )

    return text

@tool
def analyze_receipt_text(receipt_text: str, state: SupervisorTaskState) -> str:
    """
    Analyzes the extracted text from a receipt and identifies key information.

    Args:
        receipt_text: The text extracted from a receipt
        state: The current task state

    Returns:
        Structured information extracted from the receipt
    """
    task = SupervisorManager.get_task("receipts_scanner_task")

    prompt = f"""
    Analyze the following receipt text and extract key information in a structured format:

    {receipt_text}

    Please extract and format the following information:
    1. Store/Merchant Name
    2. Date of Purchase
    3. Total Amount
    4. List of Items Purchased with Prices
    5. Payment Method (if available)
    6. Tax Information (if available)
    7. Any Discounts or Special Offers

    Format the response in a clear, structured way.
    """

    result = task.model.invoke(prompt)
    if isinstance(result, etc.helper_functions.get_any_message_as_type()):
        result = result.content

    return result

async def create_supervisor_receipts_expert() -> SupervisorSupervisor:
    class TaskCreator:
        receipts_scanner_task: SupervisorTask_SingleAgent = None
        receipts_analyzer_task: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.receipts_scanner_task = SupervisorManager.register_task(
                SupervisorTask_SingleAgent(
                    name="receipts_scanner_scanner_agent",
                    tools=[scan_receipt_from_path, scan_receipt_from_base64 ],
                    prompt_id="Task_Receipts_Scanner_Agent_Scanner"
                )
            )

        
            self.receipts_analyzer_task = SupervisorManager.register_task(
                SupervisorTask_SingleAgent(
                    name="receipts_scanner_analyzer_agent",
                    tools=[analyze_receipt_text],
                    prompt_id="Task_Receipts_Scanner_Agent_Analyzer",
                )
            )

        

        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(
                SupervisorSupervisor(
                    name="receipts_scanner_supervisor",
                    prompt_id="Task_Receipts_Scanner_Supervisor"
                )
            ) \
            .add_task(self.receipts_scanner_task) \
            .add_task(self.receipts_analyzer_task) \
            .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()