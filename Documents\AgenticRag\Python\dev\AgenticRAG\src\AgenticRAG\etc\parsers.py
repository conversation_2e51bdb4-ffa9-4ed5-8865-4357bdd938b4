# bring in deps
#from llama_index.readers.file.base import DEFAULT_FILE_EXTRACTOR
from llama_index.readers.file.unstructured import UnstructuredReader

# Multimodal unstructured reader configuration
# Note: UnstructuredReader doesn't support the advanced parameters like keep_elements, strategy, etc.
# Those parameters are used directly with unstructured.partition.auto.partition in manager_multimodal.py
multimodal_reader = UnstructuredReader()

# Basic unstructured reader for fallback
basic_reader = UnstructuredReader()

# use SimpleDirectoryReader to parse our file
file_extractor = {
    # ".doc": doc_parser,
    # ".docx": doc_parser,
    # ".pdf": pdf_parser,
    ".pdf": multimodal_reader,
    ".docx": multimodal_reader,
    ".pptx": multimodal_reader,
    ".doc": basic_reader,
    ".txt": basic_reader,
    #".txt": DEFAULT_FILE_EXTRACTOR,  # fallback or use UnstructuredReader() here too
}
