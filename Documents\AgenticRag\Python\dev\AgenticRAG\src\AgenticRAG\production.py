from imports import *

import asyncio
from main import mainFunc

async def main():
    ZairaSettings.IsDebugMode = False
    Globals.set_debug(ZairaSettings.IsDebugMode)
    await mainFunc()
    
    # Run multiple async tasks concurrently
    #task1 = asyncio.create_task(mainFunc(is_debug=False))
    
    # Wait for both tasks to complete
    #await task1

# Run the async program
if __name__ == "__main__":
    asyncio.run(main())
