from imports import *

from managers.manager_supervisors import <PERSON><PERSON><PERSON><PERSON><PERSON>, SupervisorSupervisor, SupervisorTask_SingleAgent
from tasks.outputs.output_processing.task_out_language_verifier import create_out_processing_language_verifier

# Define tools for agents

async def create_supervisor_output_processing() -> SupervisorSupervisor:
    class TaskCreator:
        language_verifier: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.language_verifier = await create_out_processing_language_verifier()

        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(SupervisorSupervisor(name="output_processing_supervisor", prompt_id="Output_Supervisor_Processing")) \
                 .add_task(self.language_verifier) \
                .compile()
                #.add_task(self.language_verifier, priority=1) \
                
    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()
