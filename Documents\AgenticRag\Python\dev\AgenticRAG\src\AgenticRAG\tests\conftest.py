"""
Pytest configuration and fixtures for AgenticRAG testing
"""
import sys
import os
import asyncio
import pytest
from unittest.mock import <PERSON><PERSON>, AsyncMock, patch

# Add project root to Python path - tests are in src/AgenticRAG/tests, need to go up one level to src/AgenticRAG
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()

@pytest.fixture
def mock_claude_environment():
    """Mock Claude Code environment detection"""
    with patch.dict(os.environ, {'CLAUDE_CODE': '1', 'ANTHROPIC_USER_ID': 'test-claude'}):
        yield

@pytest.fixture
def mock_database_connections():
    """Mock database connections to avoid external dependencies"""
    with patch('managers.manager_postgreSQL.PostgreSQLManager') as mock_pg, \
         patch('managers.manager_qdrant.QDrantManager') as mock_qdrant:
        
        # Mock PostgreSQL manager
        mock_pg_instance = AsyncMock()
        mock_pg.get_instance.return_value = mock_pg_instance
        mock_pg_instance.get_connection.return_value = AsyncMock()
        
        # Mock Qdrant manager
        mock_qdrant_instance = AsyncMock()
        mock_qdrant.get_instance.return_value = mock_qdrant_instance
        mock_qdrant_instance.search_similar.return_value = []
        
        yield {
            'postgresql': mock_pg_instance,
            'qdrant': mock_qdrant_instance
        }

@pytest.fixture
def mock_external_services():
    """Mock external services to avoid network calls"""
    # Mock the OAuth verifier since that's what the tests actually use
    with patch('endpoints.oauth._verifier_.OAuth2Verifier') as mock_oauth, \
         patch('managers.manager_postgreSQL.PostgreSQLManager') as mock_db:
        
        # Mock OAuth verifier instance
        mock_oauth_instance = AsyncMock()
        mock_oauth.get_instance.return_value = mock_oauth_instance
        
        # Mock database instance
        mock_db_instance = AsyncMock()
        mock_db.get_instance.return_value = mock_db_instance
        
        yield {
            'oauth': mock_oauth_instance,
            'database': mock_db_instance
        }

@pytest.fixture
def sample_user_data():
    """Sample user data for testing"""
    return {
        'user_guid': 'test-user-123',
        'platform': 'discord',
        'permission_level': 1,
        'created_at': '2024-01-01T00:00:00Z'
    }

@pytest.fixture
def sample_scheduled_task_data():
    """Sample scheduled task data for testing"""
    return {
        'task_id': 'test-task-123',
        'user_guid': 'test-user-123',
        'schedule_prompt': 'check email every 30 minutes',
        'task_type': 'imap_idle',
        'is_active': True,
        'created_at': '2024-01-01T00:00:00Z'
    }

@pytest.fixture
def mock_rag_components():
    """Mock RAG system components"""
    with patch('managers.manager_retrieval.RetrievalManager') as mock_retrieval:
        mock_retrieval_instance = AsyncMock()
        mock_retrieval.get_instance.return_value = mock_retrieval_instance
        mock_retrieval_instance.retrieve_similar.return_value = [
            {'text': 'Sample document 1', 'score': 0.9},
            {'text': 'Sample document 2', 'score': 0.8}
        ]
        yield mock_retrieval_instance

# Test data cleanup
@pytest.fixture(autouse=True)
def cleanup_test_data():
    """Automatically clean up test data after each test"""
    yield
    # Add cleanup logic here if needed
    pass