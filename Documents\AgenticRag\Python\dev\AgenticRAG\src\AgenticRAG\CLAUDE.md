# CLAUDE.md

This file provides comprehensive guidance for Claude <PERSON> when working with the AskZaira codebase.

## Project Overview

**AskZaira** is a sophisticated multi-agent AI assistant system built on LangGraph supervisors with LlamaIndex RAG capabilities. The system implements hierarchical task coordination with multiple communication channels (<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, WhatsApp) and supports natural language scheduled task management with PostgreSQL persistence.

## Quick Start Commands

### Primary Development (Claude-Optimized)
```bash
# Main entry point - automatically detects Claude environment
../../.venv/Scripts/python.exe dev_run.py
```

### Testing
```bash
# Use pytest for comprehensive testing
# Unit tests
../../.venv/Scripts/pytest.exe tests/unit/ -v

# Integration tests
../../.venv/Scripts/pytest.exe tests/integration/ -v

# Performance tests
../../.venv/Scripts/pytest.exe tests/performance/ -v

# Health tests
../../.venv/Scripts/pytest.exe tests/health/ -v

# Manual testing scripts - run from project root with guide: tests/manual/test_scheduled_tasks_manual.md
../../.venv/Scripts/python.exe tests/manual/ -v
```

### Dependencies
```bash
# Claude should NOT execute package installation commands automatically
# Instead, instruct the user to run:
# poetry add --group dev <package-name>
# Example: poetry add --group dev pytest-asyncio
```

## Architecture Deep Dive

### Directory Structure (Multiple Python Files)
```
src/AgenticRAG/
|-- managers/        # Singleton managers
|-- tasks/           # LangGraph agent tasks with subdirectories
|-- endpoints/       # Communication channels with OAuth subdirectory
|-- userprofiles/    # User management files
|-- tests/           # Comprehensive test suite:
|   |-- unit/        # Component-specific tests
|   |-- integration/ # End-to-end workflow tests
|   |-- performance/ # Performance and load tests
|   |-- health/      # System health checks
|   `-- manual/      # Human-guided testing procedures and scripts
|-- etc/             # Utility files
|-- inputs/          # Input processing scripts
|-- ui/              # Web interface files
```

### Core Architectural Patterns

#### 1. Multi-Agent Supervisor Pattern (LangGraph)
**Implementation**: `managers/manager_supervisors.py`

**Key Classes:**
- `SupervisorTask_Base` - Base class for all LangChain supervisor tasks
- `SupervisorTask_SingleAgent` - Single tool-calling agent
- `SupervisorTask_Create_agent` - ReAct pattern agents
- `SupervisorTask_ChainOfThought` - Enhanced reasoning with step-by-step tracking
- `SupervisorSupervisor` - Hierarchical task coordination and routing
- `SupervisorSupervisor_ChainOfThought` - CoT-enhanced routing decisions

#### 2. Centralized Import Management
**File**: `imports.py`
**Purpose**: Single import file to optimize startup performance and manage dependencies. Should only contain imports that aren't slow during load
**Pattern**: ALL modules must use `from imports import *` at the top

#### 3. Singleton Resource Management
**All managers use singleton metaclass pattern:**
```python
# Correct usage pattern
manager = ZairaUserManager.get_instance()
qdrant = QDrantManager.get_instance()
```

#### 4. Async-First Architecture
- **All operations are async**
- **Concurrent execution**: Background tasks with `asyncio.create_task`
- **Thread safety**: `asyncio.Lock` for shared resources

### Database Architecture

#### PostgreSQL (Primary Database)
**Manager**: `managers/manager_postgreSQL.py`
**Features:**
- Auto-creating databases and tables with proper indexes
- Connection pooling with AsyncPG
- Scheduled task persistence with full state serialization
- User profile storage and metrics logging

#### Qdrant Vector Database
**Manager**: `managers/manager_qdrant.py`
**Features:**
- Hybrid search (dense + sparse embeddings)
- BM25 + semantic similarity search
- Metadata filtering and faceted search
- Automatic host resolution (Docker/local)

#### LlamaIndex RAG Pipeline
**Manager**: `managers/manager_retrieval.py`
**Features:**
- HuggingFace ONNX embeddings + FastEmbed sparse embeddings
- LangChain RecursiveCharacterTextSplitter
- Multimodal support

## Development Standards & Required Patterns

### 1. Character Encoding (CRITICAL)
**Forbidden characters in code:**
- emoji, Unicode symbols, special drawing characters

### 2. Exception Handling (CRITICAL)
**ALWAYS use the centralized exception handler:**
```python
# [CORRECT] ALWAYS do this:
try:
    result = await some_operation()
except Exception as e:
    from etc.helper_functions import exception_triggered
    exception_triggered(e, "Context description", user)
```

### 3. Import Pattern (REQUIRED)
```python
# [CORRECT] ALWAYS start files with this import:
from imports import *

# [CORRECT] For large libraries, import specific functions with "as":
from os import path as os_path
from os import environ as os_environ

# [WRONG] NEVER import entire large libraries:
import os  # Wrong!
import sys  # Wrong!
```

### 4. Class Design Standards (REQUIRED)
**CRITICAL**: All NEW classes must use Pydantic BaseModel for data validation and type safety.

```python
# [CORRECT] ALWAYS use Pydantic BaseModel for new classes:
from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime

class MyNewClass(BaseModel):
    # Use type hints and Field for validation
    count: int = Field(default=0, ge=0, description="Count must be non-negative")
    tags: List[str] = Field(default_factory=list, description="List of tags")
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="Optional metadata")
    created_at: datetime = Field(default_factory=datetime.now, description="Creation timestamp")
    
    class Config:
        # Enable validation on assignment
        validate_assignment = True
        # Allow arbitrary types if needed
        arbitrary_types_allowed = True
        # Use enum values
        use_enum_values = True

# [WRONG] NEVER create classes without Pydantic validation:
class BadClass:  # Wrong - no validation!
    def __init__(self, name, count):
        self.name = name
        self.count = count
```

### 5. Manager Access Pattern (MANDATORY SINGLETON)
**CRITICAL**: ALL manager classes MUST implement singleton pattern with get_instance() method.

```python
# [CORRECT] Mandatory singleton usage - ALL managers must use this pattern:
user_manager = ZairaUserManager.get_instance()
supervisor_manager = SupervisorManager.get_instance()

# [WRONG] NEVER use directly:
manager = ZairaUserManager()  # Wrong - bypasses singleton!
manager = ScheduledTaskPersistenceManager()  # Wrong - should use get_instance()!

# [REQUIRED] All new manager classes MUST implement this pattern:
class MyNewManager:
    _instance: Optional['MyNewManager'] = None
    _initialized: bool = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> "MyNewManager":
        """Get singleton instance of the manager"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        """Initialize the manager - called once during application startup"""
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        # Initialization logic here
        instance._initialized = True
```

**Singleton Enforcement Rules:**
1. **MANDATORY**: Every manager class must have a `get_instance()` class method
2. **FORBIDDEN**: Direct instantiation of manager classes (using `ManagerClass()`)
3. **REQUIRED**: Use `_instance` class variable to store singleton instance
4. **REQUIRED**: Use `_initialized` flag to prevent duplicate initialization
5. **RECOMMENDED**: Use metaclass pattern for thread-safe singleton implementation

### 5. Async Programming Standards
```python
# [CORRECT] All new functions must be async:
async def my_function():
    result = await some_async_operation()
    return result

# [CORRECT] Persistence handling for async tasks:
from etc.helper_functions import handle_asyncio_task_result_errors
from asyncio import create_task as asyncio_create_task
task = asyncio_create_task(long_running_operation())
handle_asyncio_task_result_errors(task)
```

### 6. Configuration Management
```python
# [PREFERRED] Use environment variables for production, config.py otherwise:
from etc.helper_functions import get_value_from_env
api_key = get_value_from_env('OPENAI_API_KEY', 'default_value', can_be_list=False)

# [WRONG] Never hardcode keys directly in business logic:
api_key = "sk-1234567890abcdef"  # Hardcoded in random files!
```

### 7. GUID Usage Standards (MANDATORY)
**CRITICAL**: ALWAYS use GUIDs instead of simple IDs for all identifiers in the codebase.

```python
# [CORRECT] Always use GUIDs for identifiers:
from uuid import uuid4

class MyDataClass(BaseModel):
    user_guid: str = Field(..., description="User GUID identifier")
    task_guid: str = Field(..., description="Task GUID identifier") 
    session_guid: str = Field(..., description="Session GUID identifier")
    document_guid: str = Field(..., description="Document GUID identifier")
    
    def __init__(self, **kwargs):
        # Auto-generate GUID if not provided
        if 'task_guid' not in kwargs:
            kwargs['task_guid'] = str(uuid4())
        super().__init__(**kwargs)

# [WRONG] NEVER use simple integer or string IDs:
class BadClass(BaseModel):
    user_id: int = Field(...)  # Wrong! Use user_guid instead
    task_id: str = Field(...)  # Wrong! Use task_guid instead

# [CORRECT] Database schema should use GUID columns:
CREATE TABLE users (
    user_guid UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP DEFAULT NOW()
);

# [WRONG] Never use integer auto-increment IDs. Ignore this line for the primary key:
CREATE TABLE users (
    platform_id INT,  -- Wrong! Use platform_guid UUID instead
    created_at TIMESTAMP DEFAULT NOW()
);

# [CORRECT] Function parameters and variables:
async def process_user_data(user_guid: str, task_guid: str):
    user = await find_user_by_guid(user_guid)
    task = await find_task_by_guid(task_guid)
    return await process(user, task)

# [WRONG] Never use simple id variables:
async def process_user_data(user_id: int, task_id: str):  # Wrong naming!
    pass
```

**GUID Enforcement Rules:**
1. **MANDATORY**: All variable names must use `*_guid` suffix, never `*_id`
2. **MANDATORY**: All function parameters must use `*_guid` naming
3. **MANDATORY**: All Pydantic field names must use `*_guid` suffix
4. **MANDATORY**: Use `str(uuid4())` to generate new GUIDs
5. **FORBIDDEN**: Integer auto-increment IDs in database schemas
6. **FORBIDDEN**: Variable names ending in `_id` unless referring to external systems

### 8. Test Creation Requirements (MANDATORY)
**CRITICAL**: Every new class MUST have comprehensive tests created:

```python
# When creating a new class like this:
class MyNewFeature(BaseModel):
    name: str = Field(..., description="Feature name")
    enabled: bool = Field(default=True, description="Feature enabled status")
    
    async def process_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        # Implementation here
        return processed_data

# YOU MUST create corresponding test file: tests/unit/test_my_new_feature.py
```

**Test Creation Standards:**
1. **Unit Tests (REQUIRED)**: Create `tests/unit/test_[class_name].py` for every new class
2. **Integration Tests (REQUIRED)**: Create `tests/integration/test_[feature_name]_workflow.py` for complex features
3. **Performance Tests (RECOMMENDED)**: Create `tests/performance/test_[class_name]_performance.py` for performance-critical classes
4. **Test Coverage Requirements**:
   - Test all public methods and properties
   - Test Pydantic validation (valid and invalid inputs)
   - Test error handling and edge cases
   - Test async operations if applicable
   - Test database operations if applicable

**Test File Template:**
```python
# tests/unit/test_my_new_feature.py
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from my_module import MyNewFeature
import pytest
from pydantic import ValidationError

class TestMyNewFeature:
    def test_creation_valid_data(self):
        """Test successful creation with valid data"""
        feature = MyNewFeature(name="test_feature", enabled=True)
        assert feature.name == "test_feature"
        assert feature.enabled is True
    
    def test_creation_invalid_data(self):
        """Test validation errors with invalid data"""
        with pytest.raises(ValidationError):
            MyNewFeature(name="", enabled="invalid")  # Should fail validation
    
    def test_default_values(self):
        """Test default values are applied correctly"""
        feature = MyNewFeature(name="test_feature")
        assert feature.enabled is True  # Default value
    
    async def test_async_methods(self):
        """Test async methods work correctly"""
        feature = MyNewFeature(name="test_feature")
        test_data = {"key": "value"}
        result = await feature.process_data(test_data)
        assert isinstance(result, dict)
```

**MANDATORY WORKFLOW**: 
1. **Create new class** with Pydantic validation
2. **Immediately create corresponding test file** in appropriate test directory
3. **Run tests** to ensure they pass before continuing
4. **Add integration tests** if the class interacts with other components
5. **Update this documentation** if new testing patterns are established

### 9. Tool Development Standards (MANDATORY)
**CRITICAL**: ALWAYS use BaseTool classes instead of `@tool` decorator for LangChain tool development.

```python
# [CORRECT] ALWAYS use BaseTool for creating tools:
from langchain_core.tools import BaseTool
from typing import Optional
from pydantic import BaseModel, Field

class MyCustomTool(BaseTool):
    """Tool for performing custom operations"""
    name: str = "my_custom_tool"
    description: str = "Performs custom operations with validation"
    
    def _run(self, input_param: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, input_param: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Performs the actual async operation"""
        try:
            # Implementation logic here
            result = await some_async_operation(input_param)
            return f"Operation completed: {result}"
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "my_custom_tool", state.user_guid if state else None)
            return "Error performing operation"

# Create tool instance
my_custom_tool = MyCustomTool()

# [WRONG] NEVER use @tool decorator:
from langchain_core.tools import tool

@tool  # Wrong! Use BaseTool instead
async def bad_tool(input_param: str):
    """This pattern is deprecated and should not be used"""
    return "Bad pattern"
```

**Tool Development Enforcement Rules:**
1. **MANDATORY**: All new tools MUST extend BaseTool class
2. **MANDATORY**: Implement both `_run()` and `_arun()` methods
3. **MANDATORY**: `_run()` method MUST raise NotImplementedError with "Use async version" message
4. **MANDATORY**: All actual logic goes in `_arun()` method
5. **MANDATORY**: Use centralized exception handling in `_arun()` method
6. **MANDATORY**: Create tool instances (e.g., `my_tool = MyTool()`) for registration
7. **FORBIDDEN**: Using `@tool` decorator for any new tool development
8. **REQUIRED**: Include proper type hints for all parameters
9. **REQUIRED**: Provide descriptive `name` and `description` attributes
10. **REQUIRED**: Handle SupervisorTaskState parameter when applicable

**Tool Registration Pattern:**
```python
# [CORRECT] Register BaseTool instances with supervisor tasks:
class MySupervisorTask(SupervisorTask_SingleAgent):
    async def llm_call(self, state: SupervisorTaskState):
        return await super().llm_call(state)

# Create tool instances
tool1 = MyFirstTool()
tool2 = MySecondTool()
tool3 = MyThirdTool()

async def create_my_task() -> SupervisorTask_SingleAgent:
    return SupervisorManager.get_instance().register_task(
        MySupervisorTask(
            name="my_task",
            tools=[tool1, tool2, tool3],  # Use tool instances
            prompt_id="My_Task_Prompt"
        )
    )

# [WRONG] Never register @tool decorated functions:
async def create_bad_task():
    return SupervisorManager.get_instance().register_task(
        MySupervisorTask(
            name="bad_task",
            tools=[bad_tool_function],  # Wrong! Use BaseTool instances
            prompt_id="Bad_Task_Prompt"
        )
    )
```

**Migration from @tool to BaseTool:**
When converting existing `@tool` decorated functions to BaseTool classes:

1. **Extract the function logic** into the `_arun()` method
2. **Create proper class structure** with name and description
3. **Add `_run()` method** that raises NotImplementedError
4. **Update tool registration** to use class instances
5. **Update tests** to call `tool_instance._arun()` instead of function calls
6. **Maintain the same function signature** in `_arun()` method

## Essential Helper Functions (Must Use)

**Files**: `etc/helper_functions.py`, `etc/parsers.py`, `etc/setup.py`

## File Organization Standards

### Tasks (Multiple files in `/tasks/`)
```
tasks/
|-- inputs/          # Input processing tasks (such as retrieval, email, Google Drive, scheduled management)
|-- processing/      # Core processing tasks (such as analysis, language detection, email writing)
|-- outputs/         # Output handling tasks (such as Discord, email, Teams, WhatsApp, HTTP responses)
`-- scheduled/       # Scheduled task implementations (such as IMAP IDLE, periodic reports)
```

### Managers (Multiple files in `/managers/`)
- Use singleton pattern for shared resources
- Handle connection pooling and resource lifecycle
- Provide async interfaces to external services

### Endpoints (Multiple files in `/endpoints/`)
- One file per communication channel
- OAuth subdirectory for authentication services
- External OAuth service for containerized deployment

### User Profiles (Multiple files in `/userprofiles/`)
- User lifecycle and permission management
- Task scheduling and execution state

### Input Processing (Multiple files in `/inputs/`)
- Direct execution scripts for specialized data ingestion

### Testing (Multiple test categories in `/tests/`)
```
tests/
|-- unit/            # Component-specific tests
|-- integration/     # End-to-end workflow tests
|-- performance/     # Performance and load tests
|-- health/          # System health checks
`-- manual/          # Human-guided testing procedures
```

**Testing Requirements:**
- **All test files MUST be in `/tests/` directory**
- **All test files MUST be prefixed with `test_`**
- **ALWAYS run tests from the project root directory (src/AgenticRAG/) to avoid import issues**

## Claude Code Environment

### Automatic Environment Detection
The system automatically detects Claude Code and sets:
```python
from os import environ as os_environ
os_environ['CLAUDE_CODE'] = '1'
os_environ['ANTHROPIC_USER_ID'] = 'claude-dev'
```

### Files Claude Should Ignore (Server Deployment Only)
**NEVER modify or reference these files - they are for production server deployment:**
- `requirements_oauth_external.txt` - OAuth service dependencies
- `Dockerfile_oauth_external` - OAuth service container configuration

### Interactive Usage
When prompted with "Please ask your company-specific question:", respond with:
- `"tell me about your tasks"`
- `"what can you help me with"`
- `"list scheduled tasks"`
- `"schedule a task to check email every 30 minutes"`
- `"explain your capabilities"`
- `"show me recent activity"`

## Common Development Workflows

### Adding New Tasks For Supervisors
1. **Create task class** extending appropriate base:
   ```python
   class MyTask(SupervisorTask_Base):
       async def llm_call(self, state: SupervisorTaskState) -> SupervisorTaskState:
           # Implementation here
           return state
   ```
2. **Register with manager**:
   ```python
   SupervisorManager.get_instance().register_task("my_task", MyTask)
   ```
3. **Add to supervisor**:
   ```python
   # In task_top_level_supervisor.py
   supervisor.add_task("my_task", "Description of when to use this task")
4. **Add prompt mapping** in `managers/manager_prompts.py`
5. **Add prompt to dashboard** in `endpoints/oauth/_verifier_.py`
   ```

**SECURITY BEST PRACTICES**:
- **PREFERRED**: Use environment variables via `get_value_from_env()` helper function for production
- **ACCEPTABLE**: Store API keys `.env` or in `config.py`
- **NEVER**: Hardcode API keys directly in business logic files

### Security Best Practices
1. **Use only ASCII characters in code**:
   ```python
   # [WRONG] Unicode characters cause encoding errors:
   status = "Complete [unicode-checkmark]"  # Crashes on Windows!
   
   # [CORRECT] ASCII-only code:
   status = "Complete [OK]"
   ```

2. **Never log sensitive data**:
   ```python
   # [WRONG] Never log API keys, tokens, or user data
   logger.info(f"API key: {api_key}")  # Security risk!
   
   # [CORRECT] Log safely
   LogFire.log(event_code, "Public data", "Secure and user data", user)
   ```

2. **Use OAuth 2.0 patterns** in `/endpoints/oauth/` for external integrations
3. **Implement user isolation** for scheduled tasks and data access
4. **Use secure token verification** via `_verifier_.py` patterns
5. **Environment variable protection**:
   ```python
   # [CORRECT] Safe environment access
   from etc.helper_functions import get_value_from_env
   secret = get_value_from_env('SECRET_KEY', None)
   if not secret:
       raise ValueError("Required SECRET_KEY environment variable not set")
   ```

### Performance Considerations
- **Startup Optimization**: Centralized imports reduce dependency loading
- **Connection Pooling**: PostgreSQL and Qdrant use connection pools
- **Async Execution**: All I/O operations are non-blocking
- **Resource Management**: Singleton pattern prevents resource duplication

## Debugging & Troubleshooting

### Common Issues & Solutions
1. **Docker Networking Issues**:
   - Service host resolution differs between Docker/local
   - Check automatic host detection in managers
   - Verify container connectivity for external services

### Logging & Monitoring
- **LogFire Integration**: Application logging and observability
- **CSV Metrics**: Performance data in `logs/` directory
- **Exception Tracking**: Centralized via `exception_triggered()`

## Recommended Development Approach for Claude

### Getting Started
1. **Run the application**: `../../.venv/Scripts/python.exe main.py`
2. **Try basic interactions**: "tell me about your tasks", "what can you help me with"

### Code Stability and Longevity Requirements
**CRITICAL**: After making any code changes, you MUST verify code longevity and stability:

1. **Verify backward compatibility**: Ensure existing functionality continues to work
2. **Check exception handling**: Confirm centralized exception patterns remain intact
3. **Validate async patterns**: Ensure async/await usage follows established conventions
4. **Import validation**: Ensure centralized import patterns are maintained

**Before completing any task involving code changes:**
- Run appropriate test suites from project root
- Verify no regressions in existing functionality
- Confirm all architectural patterns remain consistent

### Making Changes
1. **Follow established patterns**: Use singleton, async, and exception patterns consistently
2. **Use Pydantic for all new classes**: Every new class MUST extend BaseModel with proper validation
3. **Create comprehensive tests**: Write unit tests immediately after creating new classes
4. **Test-driven development**: Create tests before implementing new features when possible
5. **Use helper functions**: Leverage utilities in `etc/helper_functions`
6. **Maintain documentation**: Update CLAUDE.md with significant changes

**New Class Creation Checklist:**
- [ ] Extends Pydantic BaseModel with proper Field validation (for data classes)
- [ ] Implements singleton pattern with get_instance() method (for manager classes)
- [ ] Unit test file created in tests/unit/
- [ ] Integration test file created if applicable
- [ ] All relevant tests pass before proceeding
- [ ] Documentation updated if new patterns introduced

**Manager Class Specific Requirements:**
- [ ] Implements `_instance: Optional['ClassName'] = None`
- [ ] Implements `_initialized: bool = False`
- [ ] Has `get_instance()` class method
- [ ] Has `setup()` async method for initialization
- [ ] `setup()` is being called from `etc.setup.py`
- [ ] Uses proper singleton metaclass for thread safety
- [ ] NEVER allows direct instantiation

### Testing Your Changes
1. **Run unit tests**: Test individual components in isolation - ALWAYS from project root
2. **Run integration tests**: Verify complete workflows work correctly - ALWAYS from project root
3. **Run performance tests**: Check system performance and load handling - ALWAYS from project root
4. **Run health tests**: Verify system health and resource monitoring - ALWAYS from project root
5. **Follow manual procedures**: Use the comprehensive manual testing guide and scripts
6. **Test all environments**: Verify functionality in Docker, Debug=False, Debug=True, and Claude
7. **Confirm code longevity and stability**: Verify that changes don't break existing functionality

**Code Stability Verification Process:**
- **After making changes**: Run all test suites to ensure no regressions
- **Check backward compatibility**: Verify existing functionality still works as expected
- **Validate error handling**: Ensure exception handling patterns remain consistent
- **Review async patterns**: Confirm async/await usage follows established patterns
- **Test scheduled tasks**: Verify scheduled task functionality remains intact
- **Database integrity**: Check that database operations and persistence work correctly
- **Pydantic validation**: Verify all new classes have proper Pydantic validation
- **Test coverage**: Ensure all new classes have comprehensive unit and integration tests
- **Singleton pattern compliance**: Verify all manager classes use get_instance() method
- **Manager access patterns**: Ensure no direct instantiation of manager classes
- **GUID compliance**: Verify all identifiers use GUID format, never simple IDs

**CRITICAL**: When running test files, ALWAYS execute them from the project root directory (src/AgenticRAG/) to ensure proper imports work. Never run tests from within the test directories themselves.

This comprehensive guide provides Claude Code with everything needed for effective development with the AgenticRAG codebase.
