from imports import *

from os import getcwd, path as os_path, makedirs
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_meltano import MeltanoManager
from managers.manager_users import ZairaUserManager
from datetime import datetime, timedelta
import imaplib
import email

class SupervisorTask_IMAPIdleActivate(SupervisorTask_Base):
    """
    IMAP email checking task that connects to IMAP server and processes new emails.
    This task performs a single email check and can be used by scheduled tasks.
    """
    
    last_check_timestamps: dict[str, datetime] = {}

    async def llm_call(self, state: SupervisorTaskState):
        """Execute IMAP email check - connects to server and processes new emails"""
        user = await ZairaUserManager.find_user(state.user_guid)
        
        # Get IMAP configuration from OAuth tokens
        try:
            IMAP_SERVER = await OAuth2Verifier.get_token("imap", "access_token")
            EMAIL_ACCOUNT = await OAuth2Verifier.get_token("imap", "refresh_token") 
            IMAP_PORT = await OAuth2Verifier.get_token("imap", "expires_in")
            PASSWORD = await OAuth2Verifier.get_token("imap", "token_type")
            
            if not all([IMAP_SERVER, EMAIL_ACCOUNT, IMAP_PORT, PASSWORD]):
                LogFire.log("ERROR", "Incomplete IMAP configuration found")
                return "IMAP configuration incomplete. Please reconfigure IMAP OAuth."
                
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get IMAP configuration: {str(e)}")
            return "Failed to retrieve IMAP configuration."
        
        # Set up output path
        if Globals.is_docker():
            path = '/meltano/output'
        else:
            path = getcwd() + '/src/meltano/output'
        
        makedirs(path, exist_ok=True)
        
        # Determine SSL usage from port (993 is standard SSL)
        USE_SSL = int(IMAP_PORT) == 993
        
        try:
            # Connect based on SSL flag
            if USE_SSL:
                mail = imaplib.IMAP4_SSL(IMAP_SERVER, int(IMAP_PORT))
            else:
                mail = imaplib.IMAP4(IMAP_SERVER, int(IMAP_PORT))
            
            # Login to account
            try:
                mail.login(EMAIL_ACCOUNT, PASSWORD)
            except Exception:
                # Some mail providers don't allow the extension as the username
                mail.login(EMAIL_ACCOUNT.rsplit(".", 1)[0], PASSWORD)
            
            # Select the mailbox
            mail.select("INBOX")
            
            # Get last check timestamp for this user
            last_check = self.last_check_timestamps.get(state.user_guid)
            
            if last_check:
                # Search for emails since last check
                search_date = last_check.strftime("%d-%b-%Y")
                search_criteria = f'SINCE "{search_date}"'
                LogFire.log("IMAP_IDLE", f"Searching for emails since {search_date}")
            else:
                # First time: get emails from last 24 hours
                yesterday = (datetime.now() - timedelta(days=1)).strftime("%d-%b-%Y")
                search_criteria = f'SINCE "{yesterday}"'
                LogFire.log("IMAP_IDLE", f"First check: searching for emails since {yesterday}")
            
            # Search for new emails
            status, message_ids = mail.search(None, search_criteria)
            
            new_email_count = 0
            if status == 'OK' and message_ids[0]:
                email_ids = message_ids[0].split()
                
                # Process each new email
                for i, email_id in enumerate(email_ids):
                    try:
                        status, msg_data = mail.fetch(email_id, '(RFC822)')
                        if status == 'OK':
                            raw_email = msg_data[0][1]
                            
                            # Parse email to get metadata
                            email_message = email.message_from_bytes(raw_email)
                            subject = email_message.get('Subject', 'No Subject')
                            sender = email_message.get('From', 'Unknown Sender')
                            date_str = email_message.get('Date', '')
                            
                            # Save as .eml file with meaningful name
                            email_id_str = email_id.decode() if isinstance(email_id, bytes) else str(email_id)
                            # Clean subject for filename
                            clean_subject = "".join(c for c in subject if c.isalnum() or c in (' ', '-', '_')).rstrip()[:50]
                            filename = os_path.join(path, f"new_email_{email_id_str}_{clean_subject}.eml")
                            
                            with open(filename, "wb") as f:
                                f.write(raw_email)
                            
                            new_email_count += 1
                            LogFire.log("IMAP_IDLE", f"Saved new email: {filename}")
                            
                    except Exception as e:
                        LogFire.log("ERROR", f"Failed to process email {email_id}: {str(e)}")
            
            # Logout
            mail.logout()
            
            # Update last check timestamp
            self.last_check_timestamps[state.user_guid] = datetime.now()
            
            # Convert new emails to vector store if any were found
            if new_email_count > 0:
                LogFire.log("IMAP_IDLE", f"Converting {new_email_count} new emails to vector store")
                await MeltanoManager.ConvertFilesToVectorStore(path, user)
                return f"Successfully processed {new_email_count} new emails and added them to the knowledge base."
            else:
                LogFire.log("IMAP_IDLE", "No new emails found")
                return "No new emails found since last check."
                
        except Exception as e:
            LogFire.log("ERROR", f"IMAP IDLE check failed: {str(e)}")
            return f"IMAP check failed: {str(e)}"
    
    def get_last_check_time(self, user_guid: str) -> datetime:
        """Get the last check timestamp for a user"""
        return self.last_check_timestamps.get(user_guid)
    
    def set_last_check_time(self, user_guid: str, timestamp: datetime):
        """Set the last check timestamp for a user"""
        self.last_check_timestamps[user_guid] = timestamp
    
    def clear_last_check_time(self, user_guid: str):
        """Clear the last check timestamp for a user"""
        if user_guid in self.last_check_timestamps:
            del self.last_check_timestamps[user_guid]

async def create_task_imap_idle_activate() -> SupervisorTask_Base:
    """Create and register the IMAP email checking task"""
    return SupervisorManager.register_task(
        SupervisorTask_IMAPIdleActivate(
            name="imap_email_check_task", 
            prompt_id="Task_IMAP_Email_Check"
        )
    )