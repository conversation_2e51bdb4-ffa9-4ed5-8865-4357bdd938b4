from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from managers.manager_supervisors import Supervisor<PERSON>anager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState
from langchain_core.tools import tool
from langchain_core.messages import AnyMessage

@tool
def generate_joke(query:str, state: SupervisorTaskState):
    """Creates a joke with the given original_input"""
    task = SupervisorManager.get_task("joke_task")
    result = task.model.invoke(f"Write a short joke about: {query}")
    if isinstance(result, etc.helper_functions.get_any_message_as_type()):
        result = result.content
    return result

@tool
def improve_joke(query:str, state: SupervisorTaskState):
    """Improves a joke after being generated"""
    task = SupervisorManager.get_task("joke_task")
    result = task.model.invoke(f"Make this joke funnier by adding wordplay: {query}")
    if isinstance(result, etc.helper_functions.get_any_message_as_type()):
        result = result.content
    return result

@tool
def polish_joke(query:str, state: SupervisorTaskState):
    """Third LLM call for final polish"""
    task = SupervisorManager.get_task("joke_task")
    result = task.model.invoke(f"Add a surprising short twist to this joke: {query}")
    if isinstance(result, etc.helper_functions.get_any_message_as_type()):
        result = result.content
    return result

async def create_supervisor_joker() -> SupervisorSupervisor:
    class TaskCreator:
        joke_task: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.joke_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(name="joke_task", tools=[generate_joke, improve_joke, polish_joke], prompt=
                "Your tools will help you to generate a joke. Use your first tool has generate it, your second tool to improve the joke and the third tool to polish it."))
        
        async def create_supervisor(self) -> SupervisorSupervisor:
                #"You are an entertainment joke machine. When compiling a joke, ensure it's a small joke."
                #"The joke should clearly communicate the necessary information while maintaining "
                #"an incredibly casual tone.")) \
            return SupervisorManager.register_supervisor(SupervisorSupervisor(name="joker_supervisor", prompt=
                                                                              "DO NOT USE.")) \
                .add_task(self.joke_task) \
                .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()
