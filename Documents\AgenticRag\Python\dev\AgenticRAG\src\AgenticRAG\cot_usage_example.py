#!/usr/bin/env python3
"""
Usage example for Chain of Thought integration in the supervisor system.
This example shows how to use the CoT-enhanced supervisors.
"""

# Example usage of CoT-enhanced supervisors

"""
## 1. Using CoT-Enhanced Top Level Supervisor

The top level supervisor now uses chain of thought reasoning for better task routing:

```python
# In task_top_level_supervisor.py, the supervisor is now created as:
SupervisorSupervisor_ChainOfThought(
    name="top_level_supervisor",
    prompt_id="Top_Supervisor_CoT_Prompt"
)
```

When processing user requests, it will now think through:
1. UNDERSTAND: What is the user's original request?
2. EVALUATE: What has been accomplished so far by previous tasks?
3. IDENTIFY: What still needs to be done to fully address the request?
4. PRIORITIZE: Which task would be most valuable to call next?
5. DECIDE: Should I call a specific task or END the process?

## 2. Using CoT-Enhanced Retrieval Supervisor

The retrieval supervisor uses systematic reasoning for search strategies:

```python
# In task_retrieval.py, the supervisor is now created as:
SupervisorSupervisor_ChainOfThought(
    name="search_supervisor", 
    prompt_id="Supervisor_Retrieval_CoT"
)
```

When processing search requests, it will think through:
1. ANALYZE QUERY: What information is the user seeking?
2. SOURCE PRIORITY: Should I check company data (RAG) first or external sources?
3. RAG EVALUATION: After RAG search, does it fully answer the question?
4. GAP ANALYSIS: What information is missing or incomplete?
5. NEXT ACTION: Do I need web search or is the answer complete?

## 3. Creating Custom CoT Tasks

You can create your own CoT-enhanced tasks:

```python
from managers.manager_supervisors import SupervisorTask_ChainOfThought

# Create a custom CoT task
custom_cot_task = SupervisorTask_ChainOfThought(
    name="my_cot_task",
    prompt="You are a helpful assistant that analyzes documents.",
    cot_prompt_suffix="Think step by step: 1. What type of document is this? 2. What are the key points? 3. What should I focus on?"
)
```

## 4. Creating Custom CoT Supervisors

You can create your own CoT-enhanced supervisors:

```python
from managers.manager_supervisors import SupervisorSupervisor_ChainOfThought

# Create a custom CoT supervisor
custom_supervisor = SupervisorSupervisor_ChainOfThought(
    name="my_cot_supervisor",
    prompt_id="My_Custom_CoT_Prompt"  # Define this in manager_prompts.py
)

# Optionally disable CoT for specific instances
custom_supervisor.enable_cot_routing = False  # Falls back to regular routing
```

## 5. Accessing Reasoning Steps

The reasoning steps are tracked in the SupervisorTaskState:

```python
# After task execution, you can access reasoning steps
result = await supervisor.call_supervisor("user query", user)
reasoning_trace = result.get("reasoning_steps", [])

for step in reasoning_trace:
    print(f"Reasoning: {step}")
```

## 6. Benefits of CoT Integration

1. **Transparency**: See exactly how the AI makes decisions
2. **Better Accuracy**: Step-by-step reasoning reduces errors
3. **Debugging**: Track decision-making process for troubleshooting
4. **User Trust**: Users can understand the AI's thought process
5. **Consistency**: Systematic approach to complex decisions

## 7. Configuration Options

Chain of thought can be configured per supervisor:

```python
# Enable/disable CoT for individual supervisors
supervisor.enable_cot_routing = True   # Use CoT prompts
supervisor.enable_cot_routing = False  # Use regular prompts

# Custom CoT prompts in manager_prompts.py
"My_Custom_CoT_Prompt": "Step-by-step reasoning for my specific use case..."
```

## 8. Performance Considerations

- CoT prompts are longer, which may increase token usage
- Reasoning steps add slight processing overhead
- Benefits typically outweigh costs for complex decision-making tasks
- Consider disabling CoT for simple, high-frequency tasks

## 9. Best Practices

1. Use descriptive reasoning step labels (ANALYSIS, CONTEXT, etc.)
2. Keep reasoning steps focused and actionable
3. Test with and without CoT to measure impact
4. Monitor reasoning quality in logs
5. Customize CoT prompts for specific domains
"""

def main():
    print("Chain of Thought Usage Example")
    print("=" * 40)
    print("See the docstring in this file for detailed usage examples.")
    print("Key files modified for CoT integration:")
    print("- managers/manager_prompts.py (new CoT prompts)")
    print("- managers/manager_supervisors.py (CoT classes)")
    print("- tasks/task_top_level_supervisor.py (uses CoT)")
    print("- tasks/inputs/task_retrieval.py (uses CoT)")

if __name__ == "__main__":
    main()