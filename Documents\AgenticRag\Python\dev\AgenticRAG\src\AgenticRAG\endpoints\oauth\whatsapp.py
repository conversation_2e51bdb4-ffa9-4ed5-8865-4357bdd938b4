from imports import *

from aiohttp import web

from endpoints.oauth._verifier_ import OAuth2App

class OAuth2Whatsapp(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_input("comm", [
            "str:WhatsApp Phone Number ID",
            "str:WhatsApp Business Account ID", 
            "str:WhatsApp Verify Token",
            "str:WhatsApp Access Token",
            "str:Meta App ID",
            "str:Meta App Secret"
        ])

    async def on_success_return(self, request: web.Request) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return(request)
        ret_html += "WhatsApp configuratie opgeslagen! De bot is nu klaar voor gebruik."
        return ret_html

    async def on_success_execute(self, request: web.Request) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        ret_html = await super().on_success_execute(request)
        return ret_html
    
    async def on_success_execute_fail(self, request: web.Request) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute_fail(request)
        ret_html += "Er is een fout opgetreden bij het configureren van WhatsApp."
        return ret_html