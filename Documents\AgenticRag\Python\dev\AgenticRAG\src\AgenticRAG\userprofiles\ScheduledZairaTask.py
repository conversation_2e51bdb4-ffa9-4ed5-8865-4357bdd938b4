from imports import *

import asyncio
import re
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from uuid import uuid4
from pydantic import Field, BaseModel
from enum import Enum
from threading import Lock
from time import time as time_time

from userprofiles.LongRunningZairaTask import <PERSON><PERSON><PERSON><PERSON><PERSON>airaT<PERSON>
from userprofiles.ZairaUser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from endpoints.mybot_generic import MyBot_Generic

class ScheduleType(Enum):
    ONCE = "once"
    RECURRING = "recurring"

class ScheduledZairaTask(LongRunningZairaTask):
    schedule_prompt: str = Field(default="", description="The original scheduling prompt")
    target_prompt: str = Field(default="", description="The prompt to execute after delay")
    delay_seconds: float = Field(default=0.0, description="Delay in seconds before execution")
    schedule_type: ScheduleType = Field(default=ScheduleType.ONCE, description="Whether to run once or repeatedly")
    next_execution: Optional[datetime] = Field(None, description="Next scheduled execution time")
    is_active: bool = Field(default=True, description="Whether this scheduled task is active")
    
    def __init__(self, user: <PERSON><PERSON><PERSON><PERSON><PERSON>, calling_bot: MyBot_Generic, original_message, schedule_prompt: str):
        # Parse the schedule prompt first to get required values
        target_prompt, delay_seconds, schedule_type = ScheduledZairaTask._parse_schedule_prompt_static(schedule_prompt)
        next_execution = datetime.now() + timedelta(seconds=delay_seconds)
        
        task_id = uuid4()
        complete_message = f"Scheduled task: {schedule_prompt}"
        
        # Call parent constructor with correct parameters
        super().__init__(user, task_id, complete_message, calling_bot, original_message)
        
        # Then set our specific Pydantic fields directly
        object.__setattr__(self, 'schedule_prompt', schedule_prompt)
        object.__setattr__(self, 'target_prompt', target_prompt)
        object.__setattr__(self, 'delay_seconds', delay_seconds)
        object.__setattr__(self, 'schedule_type', schedule_type)
        object.__setattr__(self, 'next_execution', next_execution)
        object.__setattr__(self, 'is_active', True)
        
        # Auto-save to persistence on creation (only if event loop is running)
        try:
            from etc.helper_functions import handle_asyncio_task_result_errors
            task = asyncio.create_task(self._save_to_persistence())
            task.add_done_callback(handle_asyncio_task_result_errors)
        except (RuntimeError, Exception):
            # No event loop running (e.g., in tests), skip auto-save
            pass
    
    def _set_task_status(self, new_status: Dict[str, Any]):
        """Update task status with thread safety"""
        with self._lock:
            self.task_status.update(new_status)
    
    @staticmethod
    def _parse_schedule_prompt_static(prompt: str) -> tuple[str, float, ScheduleType]:
        """Static version of _parse_schedule_prompt for use during initialization"""
        return ScheduledZairaTask._parse_schedule_prompt_impl(prompt)
    
    def _parse_schedule_prompt(self, prompt: str) -> tuple[str, float, ScheduleType]:
        """Instance method for backward compatibility"""
        return ScheduledZairaTask._parse_schedule_prompt_impl(prompt)
    
    @staticmethod
    def _parse_schedule_prompt_impl(prompt: str) -> tuple[str, float, ScheduleType]:
        prompt_lower = prompt.lower().strip()
        
        # Pattern matching for different schedule types
        patterns = [
            # "trigger IMAP IDLE every 30 minutes"
            (r'trigger\s+(.+?)\s+every\s+(\d+)\s+(second|minute|hour|day)s?', ScheduledZairaTask._parse_recurring_trigger),
            # "send me a good morning message at 9am monday to friday"  
            (r'send\s+me\s+(.+?)\s+at\s+(\d{1,2})(am|pm)\s+(monday\s+to\s+friday|daily|weekdays?)', ScheduledZairaTask._parse_daily_message),
            # "email me a report every first of the month"
            (r'email\s+me\s+(.+?)\s+every\s+(first|last|\d{1,2}(?:st|nd|rd|th)?)\s+of\s+the\s+month', ScheduledZairaTask._parse_monthly_email),
            # Generic "do X every Y time_unit"
            (r'(.+?)\s+every\s+(\d+)\s+(second|minute|hour|day)s?', ScheduledZairaTask._parse_generic_recurring),
            # Generic "do X in Y time_unit"
            (r'(.+?)\s+in\s+(\d+)\s+(second|minute|hour|day)s?', ScheduledZairaTask._parse_generic_once),
        ]
        
        for pattern, parser in patterns:
            match = re.search(pattern, prompt_lower)
            if match:
                return parser(match, prompt)
        
        # Fallback: treat as immediate one-time task
        return prompt, 0, ScheduleType.ONCE
    
    @staticmethod
    def _parse_recurring_trigger(match, original_prompt: str) -> tuple[str, float, ScheduleType]:
        action = match.group(1)
        interval = int(match.group(2))
        unit = match.group(3)
        
        delay_seconds = ScheduledZairaTask._convert_to_seconds(interval, unit)
        target_prompt = f"trigger {action}"
        
        return target_prompt, delay_seconds, ScheduleType.RECURRING
    
    @staticmethod
    def _parse_daily_message(match, original_prompt: str) -> tuple[str, float, ScheduleType]:
        message = match.group(1)  
        hour = int(match.group(2))
        am_pm = match.group(3)
        frequency = match.group(4)
        
        # Convert to 24-hour format
        if am_pm == 'pm' and hour != 12:
            hour += 12
        elif am_pm == 'am' and hour == 12:
            hour = 0
            
        # Calculate delay until next occurrence
        now = datetime.now()
        target_time = now.replace(hour=hour, minute=0, second=0, microsecond=0)
        
        # If time has passed today, schedule for tomorrow
        if target_time <= now:
            target_time += timedelta(days=1)
            
        # For weekdays only, adjust to next weekday if needed
        if 'monday to friday' in frequency or 'weekday' in frequency:
            while target_time.weekday() >= 5:  # Saturday=5, Sunday=6
                target_time += timedelta(days=1)
        
        delay_seconds = (target_time - now).total_seconds()
        target_prompt = f"send message: {message}"
        
        return target_prompt, delay_seconds, ScheduleType.RECURRING
    
    @staticmethod
    def _parse_monthly_email(match, original_prompt: str) -> tuple[str, float, ScheduleType]:
        content = match.group(1)
        day_spec = match.group(2)
        
        # Calculate next monthly occurrence
        now = datetime.now()
        if day_spec == 'first':
            target_day = 1
        elif day_spec == 'last':
            # Get last day of current month
            next_month = now.replace(day=28) + timedelta(days=4)
            target_day = (next_month - timedelta(days=next_month.day)).day
        else:
            target_day = int(re.sub(r'[^\d]', '', day_spec))
            
        # Create target datetime for this month or next
        try:
            target_time = now.replace(day=target_day, hour=9, minute=0, second=0, microsecond=0)
            if target_time <= now:
                # Move to next month
                if now.month == 12:
                    target_time = target_time.replace(year=now.year + 1, month=1)
                else:
                    target_time = target_time.replace(month=now.month + 1)
        except ValueError:
            # Day doesn't exist in current month, try next month
            if now.month == 12:
                target_time = datetime(now.year + 1, 1, target_day, 9, 0, 0)
            else:
                target_time = datetime(now.year, now.month + 1, target_day, 9, 0, 0)
        
        delay_seconds = (target_time - now).total_seconds()
        target_prompt = f"email report: {content}"
        
        return target_prompt, delay_seconds, ScheduleType.RECURRING
    
    @staticmethod
    def _parse_generic_recurring(match, original_prompt: str) -> tuple[str, float, ScheduleType]:
        action = match.group(1).strip()
        interval = int(match.group(2))
        unit = match.group(3)
        
        delay_seconds = ScheduledZairaTask._convert_to_seconds(interval, unit)
        return action, delay_seconds, ScheduleType.RECURRING
    
    @staticmethod
    def _parse_generic_once(match, original_prompt: str) -> tuple[str, float, ScheduleType]:
        action = match.group(1).strip()
        interval = int(match.group(2))
        unit = match.group(3)
        
        delay_seconds = ScheduledZairaTask._convert_to_seconds(interval, unit)
        return action, delay_seconds, ScheduleType.ONCE
    
    @staticmethod
    def _convert_to_seconds(interval: int, unit: str) -> float:
        multipliers = {
            'second': 1,
            'minute': 60,
            'hour': 3600,
            'day': 86400
        }
        return float(interval * multipliers.get(unit, 1))
    
    async def run_scheduled_task(self):
        while self.is_active:
            # Wait for the scheduled time
            if self.next_execution:
                now = datetime.now()
                if self.next_execution > now:
                    delay = (self.next_execution - now).total_seconds()
                    if delay > 0:
                        await asyncio.sleep(delay)
            else:
                await asyncio.sleep(self.delay_seconds)
            
            if not self.is_active:
                break
                
            # Execute the target prompt by calling parent class
            try:
                # Create a new LongRunningZairaTask for the actual execution
                execution_task = LongRunningZairaTask(
                    user=self.user,
                    task_id=uuid4(),
                    complete_message=self.target_prompt,
                    calling_bot=self.calling_bot,
                    original_message=self.original_physical_message
                )
                
                # Run the task
                await execution_task.run_task()
                await execution_task.await_status_complete(wait_on_complete=True)
                
                LogFire.log("SCHEDULED_TASK", f"Executed scheduled task: {self.target_prompt}")
                
            except Exception as e:
                LogFire.log("ERROR", f"Scheduled task execution failed: {str(e)}")
                await self.calling_bot.send_reply(f"Scheduled task failed: {str(e)}", self, self.original_physical_message, False)
            
            # Handle recurring vs one-time tasks
            if self.schedule_type == ScheduleType.ONCE:
                self.is_active = False
                break
            else:
                # Schedule next execution
                self.next_execution = datetime.now() + timedelta(seconds=self.delay_seconds)
                self._set_task_status({
                    'status': 'scheduled',
                    'message': f'Next execution at {self.next_execution.strftime("%Y-%m-%d %H:%M:%S")}',
                    'next_execution': self.next_execution.isoformat()
                })
                
                # Update persistence with new schedule
                from etc.helper_functions import handle_asyncio_task_result_errors
                task = asyncio.create_task(self._update_persistence())
                task.add_done_callback(handle_asyncio_task_result_errors)
    
    async def run_task(self):
        # Override parent's run_task to start the scheduling loop
        self._set_task_status({
            'status': 'scheduled',
            'message': f'Task scheduled for {self.next_execution.strftime("%Y-%m-%d %H:%M:%S")}',
            'next_execution': self.next_execution.isoformat()
        })
        
        # Start the scheduling loop
        await self.run_scheduled_task()
        
        # Mark as completed when done
        self._set_task_status({
            'status': 'completed',
            'message': 'Scheduled task completed',
            'completed_at': datetime.now().timestamp()
        })
    
    def cancel_schedule(self, reason: str = "User requested cancellation"):
        """Cancel the scheduled task"""
        self.is_active = False
        self._set_task_status({
            'status': 'cancelled',
            'message': f'Scheduled task cancelled: {reason}',
            'cancelled_at': datetime.now().timestamp(),
            'cancellation_reason': reason
        })
        
        # Save cancellation to persistence
        from etc.helper_functions import handle_asyncio_task_result_errors
        task = asyncio.create_task(self._save_cancellation_to_persistence(reason))
        task.add_done_callback(handle_asyncio_task_result_errors)
    
    def get_schedule_info(self) -> Dict[str, Any]:
        """Get information about this scheduled task"""
        return {
            'schedule_prompt': self.schedule_prompt,
            'target_prompt': self.target_prompt,
            'delay_seconds': self.delay_seconds,
            'schedule_type': self.schedule_type.value,
            'next_execution': self.next_execution.isoformat() if self.next_execution else None,
            'is_active': self.is_active,
            'task_id': str(self.task_id)
        }
    
    async def _save_to_persistence(self):
        """Save this task to persistence layer"""
        try:
            from managers.manager_scheduled_tasks import get_persistence_manager
            persistence_manager = await get_persistence_manager()
            await persistence_manager.save_task(self)
        except Exception as e:
            LogFire.log("ERROR", f"Failed to save scheduled task to persistence: {str(e)}")
    
    async def _save_cancellation_to_persistence(self, reason: str):
        """Save task cancellation to persistence layer"""
        try:
            from managers.manager_scheduled_tasks import get_persistence_manager
            persistence_manager = await get_persistence_manager()
            await persistence_manager.cancel_task(str(self.task_id), reason)
        except Exception as e:
            LogFire.log("ERROR", f"Failed to save task cancellation to persistence: {str(e)}")
    
    async def _update_persistence(self):
        """Update this task in persistence (called when schedule changes)"""
        await self._save_to_persistence()