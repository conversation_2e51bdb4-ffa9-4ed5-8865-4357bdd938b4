<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Server Setup - AskZaira</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background: #000000;
            color: #f8fafc;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
        }

        /* Animated background elements */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: radial-gradient(circle at 20% 80%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                        radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.1) 0%, transparent 50%);
            animation: pulse 4s ease-in-out infinite alternate;
        }
		
		a:visited {
			color: #0A84FF;
		}

        @keyframes pulse {
            0% { opacity: 0.3; }
            100% { opacity: 0.6; }
        }

        /* Floating particles animation */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            overflow: hidden;
            z-index: 0;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(59, 130, 246, 0.3);
            border-radius: 50%;
            animation: float 15s infinite linear;
        }

        @keyframes float {
            0% {
                transform: translateY(100vh) translateX(0px);
                opacity: 0;
            }
            10% {
                opacity: 1;
            }
            90% {
                opacity: 1;
            }
            100% {
                transform: translateY(-100px) translateX(100px);
                opacity: 0;
            }
        }

        .container {
            width: 100%;
            max-width: 720px;
            margin: 0 auto;
            padding: 2rem;
            position: relative;
            z-index: 1;
        }

        .logo-section {
            text-align: center;
            margin-bottom: 3rem;
        }

        .logo {
            width: 96px;
            height: 96px;
            margin: 0 auto 1rem;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: 700;
            position: relative;
            overflow: hidden;
        }

        .logo::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 50%;
            border-radius: 16px 16px 0 0;
        }

        .brand-name {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .tagline {
            color: #94a3b8;
            font-size: 1rem;
        }

        .card {
            background: rgba(30, 41, 59, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(59, 130, 246, 0.2);
            border-radius: 24px;
            padding: 3rem;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.4);
            position: relative;
            overflow: hidden;
        }

        .card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 40%;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0.05) 0%, transparent 100%);
            border-radius: 24px 24px 0 0;
        }

        .card-header {
            text-align: center;
            margin-bottom: 2.5rem;
            position: relative;
            z-index: 1;
        }

        .card-title {
            font-size: 1.875rem;
            font-weight: 700;
            margin-bottom: 0.75rem;
            background: linear-gradient(135deg, #f8fafc, #e2e8f0);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .card-subtitle {
            color: #94a3b8;
            font-size: 1.1rem;
            line-height: 1.6;
        }

        .form-container {
            position: relative;
            z-index: 1;
        }

        .form-group {
            margin-bottom: 2rem;
        }

        .form-label {
            display: block;
            font-size: 1rem;
            font-weight: 600;
            color: #e2e8f0;
            margin-bottom: 0.75rem;
        }

        .form-input {
            width: 100%;
            background: rgba(30, 41, 59, 0.8);
            border: 1px solid rgba(71, 85, 105, 0.4);
            border-radius: 12px;
            padding: 1rem 1.25rem;
            font-size: 1rem;
            color: #f8fafc;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            outline: none;
            border-color: rgba(59, 130, 246, 0.6);
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            background: rgba(30, 41, 59, 0.9);
        }

        .form-input::placeholder {
            color: #64748b;
        }

        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1.5rem;
        }

		.back-button {
			width: 45%;
			background: transparent;
			border: 2px solid #3b82f6;
			border-radius: 16px;
			padding: 1.25rem 2rem;
			font-size: 1.125rem;
			font-weight: 600;
			color: #3b82f6;
			cursor: pointer;
			transition: all 0.3s ease;
			box-shadow: 0 8px 32px rgba(59, 130, 246, 0.1);
            position: relative;
            overflow: hidden;
			margin-top: 1rem;
		}

        .back-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

		.back-button:hover {
			background: rgba(59, 130, 246, 0.1);
			box-shadow: 0 12px 40px rgba(59, 130, 246, 0.2);
		}

		.back-button:active {
            transform: translateY(0);
		}

        .back-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .submit-button {
			float: right;
            width: 50%;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            border: none;
            border-radius: 16px;
            padding: 1.25rem 2rem;
            font-size: 1.125rem;
            font-weight: 600;
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 32px rgba(59, 130, 246, 0.3);
            position: relative;
            overflow: hidden;
            margin-top: 1rem;
        }

        .submit-button::before {
			float: right;
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 40px rgba(59, 130, 246, 0.4);
        }

        .submit-button:hover::before {
            left: 100%;
        }

        .submit-button:active {
            transform: translateY(0);
        }

        .submit-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .loading {
            display: none;
            width: 20px;
            height: 20px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 0.5rem;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .button-loading .loading {
            display: inline-block;
        }

        .button-loading {
            pointer-events: none;
        }

        .security-note {
            margin-top: 2rem;
            padding: 1rem;
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.2);
            border-radius: 12px;
            text-align: center;
            font-size: 0.875rem;
            color: #86efac;
        }

        .security-icon {
            margin-right: 0.5rem;
            font-size: 1rem;
        }

        .progress-indicator {
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 2rem;
        }

        .progress-step {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(71, 85, 105, 0.4);
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }

        .progress-step.active {
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            box-shadow: 0 4px 16px rgba(59, 130, 246, 0.4);
        }

        .progress-step.completed {
            background: rgba(34, 197, 94, 0.8);
        }

        .progress-line {
            width: 40px;
            height: 2px;
            background: rgba(71, 85, 105, 0.4);
            margin: 0 0.25rem;
            transition: all 0.3s ease;
        }

        .progress-line.completed {
            background: rgba(34, 197, 94, 0.6);
        }

        .form-help {
            font-size: 0.875rem;
            color: #64748b;
            margin-top: 0.5rem;
            line-height: 1.4;
        }

        .form-error {
            font-size: 0.875rem;
            color: #fca5a5;
            margin-top: 0.5rem;
            display: none;
        }

        .form-input.error {
            border-color: rgba(239, 68, 68, 0.6);
            box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
        }

        @media (max-width: 640px) {
            .container {
                padding: 1rem;
            }
            
            .card {
                padding: 2rem 1.5rem;
            }
            
            .form-row {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .brand-name {
                font-size: 1.75rem;
            }

            .card-title {
                font-size: 1.5rem;
            }
        }

        /* Input focus animations */
        .form-input:focus + .form-label::after {
            content: '';
            position: absolute;
            bottom: -2px;
            left: 0;
            width: 100%;
            height: 2px;
            background: linear-gradient(135deg, #3b82f6, #6366f1);
            animation: expandWidth 0.3s ease;
        }

        @keyframes expandWidth {
            0% { width: 0; }
            100% { width: 100%; }
        }
		
		.spinner-container {
			display: flex;
			justify-content: center;
			align-items: center;
		}
		
		/* Spinner styles */
		.spinner {
			border: 8px solid #ddd;
			border-top: 8px solid #3498db;
			border-radius: 50%;
			width: 60px;
			height: 60px;
			animation: spin 1s linear infinite;
		}

		/* Spin animation */
		@keyframes spin {
			0%   { transform: rotate(0deg); }
			100% { transform: rotate(360deg); }
		}
    </style>
</head>
