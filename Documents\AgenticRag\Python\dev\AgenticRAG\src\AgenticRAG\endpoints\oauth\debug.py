from imports import *

from aiohttp import web

from endpoints.oauth._verifier_ import OAuth2App

class OAuth2Debug(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_input("auto", ["str:Deel bedrijfsdata? (ja/nee)", "str:Be<PERSON>tig keuze? (ja/nee)"], True)

    async def on_success_return(self, request: web.Request) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return(request)
        ret_html += "Debug waarde wordt aangepast."
        return ret_html

    async def on_success_execute(self, request: web.Request) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        from endpoints.oauth._verifier_ import OAuth2Verifier
        ret_html = await super().on_success_execute(request)
        debug_modus = await OAuth2Verifier.get_token("debug")
        company_data = await OAuth2Verifier.get_token("debug", "refresh_token")
        Globals.set_debug_values("j" in debug_modus and "j" in company_data)
        # Set variable based on company_data==True
        if Globals.is_debug_values():
            ret_html += "Debug staat AAN! Laat dat enkel aanstaan voor zolang als nodig is!"
        else:
            ret_html += "Debug staat uit. Bedrijfsdata staat weer veilig."
        return ret_html
    
    async def on_success_execute_fail(self, request: web.Request) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute(request)
        ret_html += ""
        return ret_html
