from imports import *

import bisect
from threading import Lock
import uuid
import operator

from typing import (
    Callable,
    Optional,
    Sequence,
    Union,
    Annotated,
    cast,
    Any,
    Tuple,
    Dict,
    Optional,
)
from typing_extensions import TypedDict

from langgraph.types import Command
from langchain_core.language_models.chat_models import BaseChatModel
from langchain_core.language_models.base import BaseLanguageModel
from langgraph.graph import StateGraph, START, END
from langgraph.graph.message import add_messages
from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage, AnyMessage
from langchain_core.tools import BaseTool
from langgraph.graph.state import CompiledStateGraph
from langchain_core.messages import HumanMessage
from pydantic import BaseModel, Field, ConfigDict
from langgraph.checkpoint.memory import MemorySaver
from langgraph.prebuilt import create_react_agent
from managers.manager_users import ZairaUserManager
from managers.manager_prompts import PromptManager

class SupervisorSection(BaseModel):
    name: str = Field(
        None, description="Name for this section of the report.",
    )
    description: str = Field(
        None, description="Brief overview of the main topics and concepts to be covered in this section.",
    )
    description_list: Annotated[list[str], None] = Field(
        default_factory=list, description="Brief overview of the main topics and concepts to be covered in this section.",
    )

    @classmethod
    def default(cls):
        """Constructor with no arguments."""
        return cls()

    @classmethod
    def from_values(cls, name: str, description):
        """Constructor with arguments."""
        if isinstance(description, str):
            return cls(name=name, description=description, description_list=[])
        if isinstance(description, list):
            return cls(name=name, description="", description_list=description)
        
    def get_description(self):
        if not self.description or self.description == "":
            return self.description_list
        return self.description
    
    def __str__(self):
        description = self.get_description()
        if isinstance(description, str):
            return description
        else:
            return ", ".join(description)

class SupervisorRouteState(BaseModel):
    step: str = Field(
        None, description="The next step in the routing process"
    )



class SupervisorTaskInput(BaseModel):
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.ZairaUser import ZairaUser

    original_input: str = Field(None, description="Query that was entered initially by the user.")
    additional_input: dict = Field(None, description="Additional data provided by Zaira.")
    user_guid: str = Field(None, description="User ID")

class SupervisorTaskOutput(BaseModel):
    messages: Annotated[list[AnyMessage], add_messages] = Field(
        None, description="Add to this when returning from a tool or agent"
    )

class SupervisorTaskState(SupervisorTaskInput, SupervisorTaskOutput):
    # next: str = Field(
    #     None, description="What node to execute next. LLM does not use this variable.",
    # )
    sections: dict[str, SupervisorSection] = Field(
        default_factory=dict, description="Sections of processed data.",
    ) # List of report sections. Unsure if this should be its own class
    completed_sections: Annotated[list, None] = Field(
        default_factory=list, description="Completed sections of the report. LLM does not use this variable.",
    ) # All workers write to this key in parallel. Unsure if this should be its own class
    completed_tasks: Annotated[list[str], None] = Field(
        default_factory=list, 
        description="Tasks that have already been executed.",
    ) # All workers write to this key in parallel. Unsure if this should be its own class
    call_trace: Annotated[list[str], None] = Field(
        default_factory=list, description="The task or supervisor that was last called. Currently used in debug only."
    )
    reasoning_steps: Annotated[list[str], None] = Field(
        default_factory=list, description="Chain of thought reasoning steps for transparent decision making."
    )
    conversation_history: Annotated[list[AnyMessage], None] = Field(
        default_factory=list, description="Full conversation history including human messages and supervisor outputs for context preservation across supervisor transitions."
    )

class SupervisorTaskConfig(TypedDict):
    complexity: int = 1



class SupervisorTask_Base:
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.ZairaUser import ZairaUser
    task_id: str = ""
    name: str = ""
    _prompt: str = ""
    prompt_id: str = ""
    model: BaseLanguageModel = None
    always_call_FIRST: bool = False
    always_call_LAST: bool = False

    langgraph: StateGraph = None
    compiled_langgraph: CompiledStateGraph = None
    thread_config: dict = {}

    def __init__(self, name: str, prompt: str = "", prompt_id: str = "", model: BaseLanguageModel = None):
        if model is None:
            model = SupervisorManager.get_instance().default_model
        self.task_id = str(uuid.uuid4())  # Auto-generated unique ID
        self.name = name
        self._prompt = prompt
        self.prompt_id = prompt_id
        self.model = model
        self.langgraph = StateGraph(SupervisorTaskState)
        self.thread_config = {"thread_id": uuid.uuid4()}

    def __repr__(self):
        return f"Task({self.task_id}, {self.name})"
    
    def get_tools(self):
        return []

    def get_prompt(self, prefix_zaira_personality: bool = False):
        ret_val = PromptManager.get_prompt(self.prompt_id) if self.prompt_id else self._prompt
        if prefix_zaira_personality:
            ret_val = PromptManager.get_prompt("AskZaira_Prompt") + ret_val
        return ret_val
        
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        return f"{self.name} NOT_IMPLEMENTED! Do not use the Base class directly."
    
    async def llm_call_wrapper(self, state: SupervisorTaskState):
        if Globals.is_debug_values():
            user = await ZairaUserManager.find_user(state.user_guid)
            LogFire.log("TASK", "Router is processing '", state.messages[-1].content.strip() + "'", user, f"Task {self.name}")
        result = await self.llm_call(state)
        if result is None:
            result = ""
        if isinstance(result, Command):
            return result
        # If string or similar was returned, wrap it neatly with the langgraph Command class
        ret_val = {}
        ret_val.update({"call_trace": state.call_trace + [f"{self.name}: llm_call"]})
        if result != "":
            ret_val.update({"messages": result})
        ret_val.update({"completed_tasks": [self.name]})
        return Command(update=ret_val)
    
    def compile_default(self) -> "SupervisorTask_Base":
        self.langgraph.add_node("llm_call", self.llm_call_wrapper)
        self.langgraph.add_edge(START, "llm_call")
        self.langgraph.add_edge("llm_call", END)

        self.compiled_langgraph = self.langgraph.compile()
        return self
    
    async def llm_call_internal(self, state: SupervisorTaskState):
        #state.call_trace = []
        #state.completed_sections = []
        #state.completed_tasks = []
        result = await self.compiled_langgraph.ainvoke(input=state, stream_mode="updates", config=self.thread_config)
        return Command(update=result[-1])
    
    async def call_task(self, query: str = "", user: "ZairaUser" = None, **kwargs):
        """Function to be called from outwith the Supervisors"""
        langgraph = StateGraph(SupervisorTaskState)
        langgraph.add_node("llm_call_internal", self.llm_call_internal)
        langgraph.add_edge(START, "llm_call_internal")
        langgraph.add_edge("llm_call_internal", END)

        user_guid = str(user.GUID) if user else ""
        state = SupervisorTaskState(original_input=query, user_guid=user_guid, messages=[HumanMessage(query)], call_trace=[f"{self.name}: call_task"])
        for key, value in kwargs.items():
            state.sections[key] = SupervisorSection(key, value)
        compiled_langgraph = langgraph.compile()
        result = await compiled_langgraph.ainvoke(state)
        response = result["messages"][-1]
        if isinstance(response, etc.helper_functions.get_any_message_as_type()):
            response = response.content
        
        return {"result": response, "call_trace": result["call_trace"]}
    
    async def call_task(self, state: SupervisorTaskState):
        """Function to be called from outwith the Supervisors"""
        langgraph = StateGraph(SupervisorTaskState)
        langgraph.add_node("llm_call_internal", self.llm_call_internal)
        langgraph.add_edge(START, "llm_call_internal")
        langgraph.add_edge("llm_call_internal", END)
        compiled_langgraph = langgraph.compile()
        result = await compiled_langgraph.ainvoke(state)
        response = result["messages"][-1]
        if isinstance(response, etc.helper_functions.get_any_message_as_type()):
            response = response.content
        
        return {"result": response, "call_trace": result["call_trace"]}


class SupervisorTask_SingleAgent(SupervisorTask_Base):
    _tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]]
    enforce_HITL_before_tool_call: bool = False
    callback_response: str = ""

    model_config = ConfigDict(arbitrary_types_allowed=True)  # Enable arbitrary types

    def __init__(self, name: str, prompt: str = "", prompt_id: str = "", tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]] = [], enforce_HITL_before_tool_call = False, model: BaseLanguageModel = None):
        super().__init__(name=name, prompt=prompt, prompt_id=prompt_id, model=model)
        self._tools = tools
        self.enforce_HITL_before_tool_call = enforce_HITL_before_tool_call
    
    def get_tools(self):
        return self._tools
        
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        used_model = self.model
        if self._tools:
            used_model = cast(BaseChatModel,cast(BaseChatModel, used_model).bind_tools(self._tools))
            tools_by_name = {tool.name: tool for tool in self._tools}
            tool_prompts = [
                f"{tool.name} prompt: {tool.description}."
                for tool in self._tools
            ]
            full_prompt = f"{self.get_prompt()}\n" + "\n".join(tool_prompts)
        else:
            full_prompt = f"{self.get_prompt()}\n"
            tools_by_name = []

        results = []
        call_trace = state.call_trace
        user = await ZairaUserManager.find_user(state.user_guid)
        async def call_my_tool(tool_call):
            tool = tools_by_name[tool_call["name"]]
            from userprofiles.LongRunningZairaTask import LongRunningZairaTask
            from managers.manager_users import ZairaUserManager

            self.callback_response = ""
            async def callback(task: LongRunningZairaTask, response: str):
                self.callback_response = response

            call_trace.append(self.name + ": tool " + tool.name)
            if "state" not in tool_call["args"] or not tool_call["args"]["state"]:
                LogFire.log("DEBUG", "tool_call", f"Adding state to tool {tool.name}. user_guid: {getattr(state, 'user_guid', 'NOT_FOUND')}")
                tool_call["args"]["state"] = state

            async def handle_tool_response():
                observation = await tool.ainvoke(input=tool_call["args"])
                results.append(ToolMessage(content=observation, tool_call_id=tool_call["id"]))
            
            if self.enforce_HITL_before_tool_call == True:
                await user.my_task.request_human_in_the_loop(f"About to call tool: {tool.name}({tool.description}).\n Proceed? (yes/no)", callback, True)
                if "yes" in self.callback_response.lower():
                    await handle_tool_response()
            else:
                await handle_tool_response()
        
        #if len(self.tools) == 1:
            # We somehow need to create the tool_args manually for this optimisation
            #await call_my_tool({"name": self.tools[0].name, "args": {"query": state.original_input, "state": state}})
        #else:
        user_info = {"name": user.username}
        chat_history = user.get_chat_history()
        messages = chat_history
        messages.insert(0, SystemMessage(content=f"You are an agent tasked with answering a question using the following tools: {tools_by_name}."
                                                " Your tools are sorted in order of relevance and importance. If a tool earlier in the list could potentially solve the issue, try that one first."
                                                " Given the following original_input,"
                                                " determine which tools need to be called to create the best result. Each tool can only be called once."
                                                " If your tool list is empty, respond to the best of your abilities with an answer to the original_input."
                                                " Consider the above as your logic. "
                                                f"My prompt: {self.get_prompt(True)}"
                                                " User info: " + " ".join(user_info) + ". "
                                                " Consider the following prompts as the individual task's logic: "
                                                f"\n{full_prompt}"))
        # Context is already available through chat history and system prompts
        result = await used_model.ainvoke(input=messages)
        if self._tools:
            for tool_call in result.tool_calls:
                await call_my_tool(tool_call)
        else:
            results = result
            call_trace.append(f"{self.name}: llm_call")
            
        return Command(update={"call_trace": call_trace, "messages": results, "completed_tasks": [self.name]})
    
    def compile_default(self) -> "SupervisorTask_Base":
        if self.enforce_HITL_before_tool_call == False:
            super().compile_default()
        else:
            self.langgraph.add_node("llm_call", self.llm_call_wrapper)
            self.langgraph.add_edge(START, "llm_call")
            self.langgraph.add_edge("llm_call", END)
            self.compiled_langgraph = self.langgraph.compile()
            return self



class SupervisorTask_Create_agent(SupervisorTask_Base):
    _tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]]
    enforce_HITL_before_tool_call: bool = False

    model_config = ConfigDict(arbitrary_types_allowed=True)  # Enable arbitrary types

    def __init__(self, name: str, prompt: str = "", prompt_id:str = "", tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]] = [], enforce_HITL_before_tool_call = False, model: BaseLanguageModel = None):
        super().__init__(name=name, prompt=prompt, prompt_id=prompt_id, model=model)
        self._tools = tools
        self.enforce_HITL_before_tool_call = enforce_HITL_before_tool_call
    
    def get_tools(self):
        return self._tools
        
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task using create_react_agent with SingleAgent state parsing"""
        try:
            user = await ZairaUserManager.find_user(state.user_guid)
            user_info = {"name": user.username}
            chat_history = user.get_chat_history()
            
            # Create the ReAct agent using LangGraph's create_react_agent
            agent_executor = create_react_agent(self.model, self._tools)
            
            # Use same state parsing mechanism as SupervisorTask_SingleAgent
            tools_by_name = {tool.name: tool for tool in self._tools} if self._tools else {}
            tool_prompts = [
                f"{tool.name} prompt: {tool.description}."
                for tool in self._tools
            ] if self._tools else []
            full_prompt = f"{self.get_prompt()}\n" + "\n".join(tool_prompts)
            
            # Build messages using SingleAgent pattern
            messages = chat_history.copy()
            messages.insert(0, SystemMessage(content=f"You are an agent tasked with answering a question using the following tools: {tools_by_name}."
                                                    " Your tools are sorted in order of relevance and importance. If a tool earlier in the list could potentially solve the issue, try that one first."
                                                    " Given the following original_input,"
                                                    " determine which tools need to be called to create the best result. Each tool can only be called once."
                                                    " If your tool list is empty, respond to the best of your abilities with an answer to the original_input."
                                                    " Consider the above as your logic. "
                                                    f"My prompt: {self.get_prompt(True)}"
                                                    " User info: " + str(user_info) + ". "
                                                    " Consider the following prompts as the individual task's logic: "
                                                    f"\n{full_prompt}"))
            
            # Add conversation history if available
            if hasattr(state, 'conversation_history') and state.conversation_history:
                messages.extend(state.conversation_history)
            
            # Context is already available through chat history and system prompts
            
            # Execute the user input with full conversation context
            result = await agent_executor.ainvoke({
                "messages": messages
            })
            
            # Extract the final message from the response
            if "messages" in result and result["messages"]:
                final_message = result["messages"][-1]
                if hasattr(final_message, "content"):
                    return Command(update={"call_trace": state.call_trace + [f"{self.name}: create_react_agent"], "messages": final_message, "completed_tasks": [self.name]})
                elif isinstance(final_message, dict) and "content" in final_message:
                    return Command(update={"call_trace": state.call_trace + [f"{self.name}: create_react_agent"], "messages": final_message, "completed_tasks": [self.name]})
            
            return Command(update={"call_trace": state.call_trace + [f"{self.name}: create_react_agent"], "messages": str(result), "completed_tasks": [self.name]})
            
        except Exception as e:
            LogFire.log("ERROR", f"Error in SupervisorTask_Create_agent.llm_call: {str(e)}")
            return Command(update={"call_trace": state.call_trace + [f"{self.name}: error"], "messages": f"Error processing request with create_react_agent: {str(e)}", "completed_tasks": [self.name]})
    
    def compile_default(self) -> "SupervisorTask_Base":
        self.langgraph.add_node("llm_call", self.llm_call_wrapper)
        self.langgraph.add_edge(START, "llm_call")
        self.langgraph.add_edge("llm_call", END)
        self.compiled_langgraph = self.langgraph.compile()
        return self


class SupervisorTask_ChainOfThought(SupervisorTask_Base):
    """
    Enhanced supervisor task that implements explicit chain of thought reasoning.
    This class captures and tracks reasoning steps for transparent decision making.
    """
    _cot_prompt_suffix: str = ""
    enable_reasoning_trace: bool = True
    
    def __init__(self, name: str, prompt: str = "", prompt_id: str = "", cot_prompt_suffix: str = "", model: BaseLanguageModel = None):
        super().__init__(name=name, prompt=prompt, prompt_id=prompt_id, model=model)
        self._cot_prompt_suffix = cot_prompt_suffix
    
    def get_cot_prompt(self, prefix_zaira_personality: bool = False):
        """Get the chain of thought enhanced prompt"""
        base_prompt = self.get_prompt(prefix_zaira_personality)
        
        if self._cot_prompt_suffix:
            return f"{base_prompt}\n\n{self._cot_prompt_suffix}"
        
        # Default CoT structure if no custom suffix provided
        cot_suffix = (
            "\n\nUse step-by-step reasoning:\n"
            "1. ANALYSIS: What is being asked?\n"
            "2. CONTEXT: What relevant information do I have?\n"
            "3. APPROACH: How should I handle this request?\n"
            "4. ACTION: What specific action should I take?\n"
            "5. VERIFICATION: Does this approach make sense?\n\n"
            "Think through each step systematically before responding."
        )
        return f"{base_prompt}{cot_suffix}"
    
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task with chain of thought reasoning"""
        user = await ZairaUserManager.find_user(state.user_guid)
        user_info = {"name": user.username}
        chat_history = user.get_chat_history()
        
        # Build messages with CoT prompt
        messages = chat_history.copy()
        messages.insert(0, SystemMessage(content=self.get_cot_prompt(True)))
        messages.append(HumanMessage(content=f"User info: {user_info}. Original request: {state.original_input}"))
        
        print(f"\n💭 [CoT TASK] {self.name} thinking step-by-step...")
        
        # Get LLM response with reasoning
        result = await self.model.ainvoke(input=messages)
        
        # Display the reasoning if it contains CoT structure
        if hasattr(result, 'content') and result.content:
            content = result.content
            print(f"🧠 [CoT RESPONSE] {self.name} reasoning:")
            print("─" * 50)
            print(content)
            print("─" * 50)
            
            # Look for numbered reasoning steps
            import re
            steps = re.findall(r'\d+\.\s*[A-Z]+:\s*([^\n]+)', content)
            reasoning_steps = [f"{self.name}: {step}" for step in steps]
            
            if reasoning_steps:
                print(f"📝 [CoT STEPS] Extracted {len(reasoning_steps)} reasoning steps:")
                for i, step in enumerate(reasoning_steps, 1):
                    print(f"   {i}. {step}")
        else:
            reasoning_steps = []
        
        return Command(update={
            "call_trace": state.call_trace + [f"{self.name}: llm_call_cot"], 
            "messages": result,
            "reasoning_steps": reasoning_steps,
            "completed_tasks": [self.name]
        })



class SupervisorSupervisor(SupervisorTask_Base):
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.ZairaUser import ZairaUser
    _tasks: list[Tuple[int, SupervisorTask_Base]] = []
    _task_map: Dict[str, Tuple[int, SupervisorTask_Base]] = {}
    _lock: Lock = None
    
    def __init__(self, name: str, prompt: str = "", prompt_id = "", tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]] = [], model: BaseLanguageModel = None):
        if model is None:
            model = SupervisorManager.get_instance().default_model
        super().__init__(name=name, prompt=prompt, prompt_id=prompt_id, model=model)

        self.name = name
        self._tasks: list[Tuple[int, SupervisorTask_Base]] = []
        self._task_map: Dict[str, Tuple[int, SupervisorTask_Base]] = {}
        self._lock = Lock()
        self.thread_config.update({"recursion_limit": 2500})
    
    def get_tools(self):
        return []

    class Route(BaseModel):
        step: str = Field(
            None, description="The next step in the routing process"
        )

    async def llm_call_router(self, state: SupervisorTaskState):
        """Route the input to the appropriate node"""
        user = await ZairaUserManager.find_user(state.user_guid)

        # Run the augmented LLM with structured output to serve as routing logic
        tasks: list[str] = []
        for _, task in self._tasks:
            if task.always_call_FIRST == False and task.always_call_LAST == False:
                tasks.append(F"{task.name}")
        # Build agent-specific prompts
        with self._lock:
            task_prompts = []
            for _, task in self._tasks:
                if task.always_call_FIRST == False and task.always_call_LAST == False:
                    task_prompts.append(f"Task {task.name}'s prompt: {task.get_prompt()}. Tools: {', '.join(tool.name for tool in task.get_tools())}. End of tools for task {task.name}")
            previous_tasks = []
            for task_name in state.completed_tasks:
                task = self.get_task(task_name)
                if task:
                    previous_tasks.append(task_name)

        supervisor_brain = "\n".join(task_prompts)
        user_info = {"name": user.username}
        chat_history = user.get_chat_history()
        messages = chat_history
        env_prompt = PromptManager.get_prompt("Global_Supervisor_Prompt") # Ends with what to do with the previous_tasks
        messages.insert(0, SystemMessage(env_prompt + ", ".join(previous_tasks) + ". End of previous_tasks." 
                         " Consider the above as your restraints." \
                        f" My prompt: {self.get_prompt(True)}\n" \
                         " User info: " + " ".join(user_info) + "." \
                         " Consider the following prompts as the individual task's logic: " \
                        f"\n{supervisor_brain}"))
        # Context is already available through chat history and system prompts
        decision: SupervisorRouteState = await self.model.with_structured_output(SupervisorRouteState).ainvoke(input=messages)

        # Handle transition to always_call tasks when ending conditional routing
        if decision.step == "END":
            # Check if we have always_call tasks to execute
            always_call_tasks = [task for _, task in self._tasks if task.always_call_LAST == True]
            if always_call_tasks:
                # Transition to first always_call task instead of END
                first_always_task = always_call_tasks[0]
                print(f"[DEBUG] {self.name} transitioning from END to always_call task: {first_always_task.name}")
                return Command(update={"call_trace": state.call_trace + [f"{self.name}: goto {first_always_task.name} (always_call)"], "completed_tasks": [self.name]}, goto=first_always_task.name)
            else:
                print(f"[DEBUG] {self.name} no always_call tasks found, going to END")
                return Command(update={"call_trace": state.call_trace + [f"{self.name}: goto END"], "completed_tasks": [self.name]}, goto="END")

        return Command(update={"call_trace": state.call_trace + [f"{self.name}: goto {decision.step}"], "completed_tasks": [self.name]}, goto=decision.step)
    
    async def llm_call_router_wrapper(self, state: SupervisorTaskState):
        if Globals.is_debug_values():
            user = await ZairaUserManager.find_user(state.user_guid)
            LogFire.log("TASK", "Router is processing '", state.messages[-1].content.strip() + "'", user, f"Supervisor {self.name}")
        result = await self.llm_call_router(state)
        if result is None:
            result = "END"
        if isinstance(result, Command):
            return result
        # If string or similar was returned, wrap it neatly with the langgraph Command class
        ret_val = {}
        ret_val.update({"call_trace": state.call_trace + [f"{self.name}: llm_call_router"]})
        ret_val.update({"completed_tasks": [self.name]})
        return Command(update=ret_val, goto=result)
        
    def compile(self) -> "SupervisorSupervisor":
        for _, task in self._tasks:
            if task.compiled_langgraph is None:
                task.compile_default()

        self.langgraph = StateGraph(state_schema=SupervisorTaskState, config_schema=SupervisorTaskConfig)
        
        # Separate conditional and always_call tasks (original logic)
        conditional_tasks = [task for _, task in self._tasks if task.always_call_LAST == False and task.always_call_FIRST == False]
        always_call_FIRST_tasks = [task for _, task in self._tasks if task.always_call_FIRST == True]
        always_call_LAST_tasks = [task for _, task in self._tasks if task.always_call_LAST == True]
        
        # Add the supervisor router node and task nodes
        self.langgraph.add_node("llm_call_router", self.llm_call_router_wrapper)
        for _, task in self._tasks:
            self.langgraph.add_node(task.name, task.llm_call_internal)
        
        def end_func(state):
            return Command(update="")
        self.langgraph.add_node("END", end_func)

        current_task = START
        if always_call_FIRST_tasks:
            # Chain always_call_FIRST tasks in sequence
            for task in always_call_FIRST_tasks:
                self.langgraph.add_edge(current_task, task.name)
                current_task = task.name
        
        # Handle conditional routing tasks
        if conditional_tasks:
            # Start with router for conditional tasks  
            self.langgraph.add_edge(current_task, "llm_call_router")
            # Conditional tasks return to router after completion
            for task in conditional_tasks:
                self.langgraph.add_edge(task.name, "llm_call_router")
        
        # Handle always_call_LAST tasks
        if always_call_LAST_tasks:
            first_always_task = always_call_LAST_tasks[0]
            
            if conditional_tasks:
                # Router can transition to first always_call task when it decides to END
                pass  # No direct edge needed - router uses goto
            else:
                # No conditional tasks, start directly with first always_call task
                self.langgraph.add_edge(current_task, first_always_task.name)
            
            # Chain always_call_LAST tasks in sequence
            for i in range(len(always_call_LAST_tasks) - 1):
                current_task_last = always_call_LAST_tasks[i]
                next_task = always_call_LAST_tasks[i + 1]
                self.langgraph.add_edge(current_task_last.name, next_task.name)
            
            # Last always_call task goes to END
            last_always_task = always_call_LAST_tasks[-1]
            self.langgraph.add_edge(last_always_task.name, END)
        
        # Always ensure END node routes to LangGraph END for router goto commands
        self.langgraph.add_edge("END", END)
        
        # Special case: no tasks at all, go directly to END
        if not conditional_tasks and not always_call_FIRST_tasks and not always_call_LAST_tasks:
            self.langgraph.add_edge(START, END)
        
        self.compiled_langgraph = self.langgraph.compile(name=self.name)
        
        return self
    
    async def llm_call_internal(self, state: SupervisorTaskState):
        output: list = []
        #state.call_trace = []
        #state.completed_sections = []
        #state.completed_tasks = []
        #state.call_trace.append(f"{self.name}: enter")
        async for stream_response in self.compiled_langgraph.astream(
            input=state, 
            config=self.thread_config,
            stream_mode="updates",
        ):
            # When Human-in-the-loop is called, we have no response just yet
            if stream_response is None:
                return Command()
            state.call_trace = stream_response["call_trace"]
            output.append(stream_response)
            if Globals.is_debug():
                print(f"----")
                debug_print = ""
                if "messages" in stream_response:
                    response = stream_response["messages"][-1]
                    if isinstance(response, etc.helper_functions.get_any_message_as_type()):
                        response = response.content
                    if len(stream_response["call_trace"]) > 0:
                        debug_print = stream_response["call_trace"][-1] + " --> " + response
                else:
                    debug_print = stream_response["call_trace"][-1]
                print(debug_print)
                print("----")
        
        #state.call_trace.append(f"{self.name}: exit")
        call_trace = state.call_trace
        
        # Display reasoning summary if this is a CoT supervisor
        if hasattr(self, 'enable_cot_routing') and self.enable_cot_routing and hasattr(state, 'reasoning_steps') and state.reasoning_steps:
            print(f"\n🎯 [CoT SUMMARY] {self.name} reasoning trail:")
            print("=" * 60)
            for i, step in enumerate(state.reasoning_steps, 1):
                print(f"{i:2d}. {step}")
            print("=" * 60)
        
        response = ""
        if len(output) > 0:
            if "messages" in output[-1]:
                return Command(update={"call_trace": call_trace, "messages": output[-1]["messages"][-1], "completed_tasks": [self.name]})
        return Command(update={"call_trace": call_trace, "completed_tasks": [self.name]})
    
    async def call_supervisor(self, query: str, user: "ZairaUser", **kwargs):
        """Function to be called from outwith the Supervisors"""
        langgraph = StateGraph(SupervisorTaskState)
        langgraph.add_node("llm_call_internal", self.llm_call_internal)
        langgraph.add_edge(START, "llm_call_internal")
        langgraph.add_edge("llm_call_internal", END)
        
        user_guid = str(user.GUID) if user else ""
        state = SupervisorTaskState(original_input=query, user_guid=user_guid, messages=[HumanMessage(query)], call_trace=[f"{self.name}: start"])
        for key, value in kwargs.items():
            state.sections[key] = SupervisorSection.from_values(key, value)
        compiled_langgraph = langgraph.compile()
        result = await compiled_langgraph.ainvoke(state)

        response = result["messages"][-1]
        if isinstance(response, etc.helper_functions.get_any_message_as_type()):
            response = response.content
        
        return {"result": response, "call_trace": result["call_trace"]}
    
    async def call_supervisor_with_state(self, state: SupervisorTaskState):
        """Function to be called from outwith the Supervisors"""
        langgraph = StateGraph(SupervisorTaskState)
        langgraph.add_node("llm_call_internal", self.llm_call_internal)
        langgraph.add_edge(START, "llm_call_internal")
        langgraph.add_edge("llm_call_internal", END)
        compiled_langgraph = langgraph.compile()
        result = await compiled_langgraph.ainvoke(state)

        response = result["messages"][-1]
        if isinstance(response, etc.helper_functions.get_any_message_as_type()):
            response = response.content
        
        return {"result": response, "call_trace": result["call_trace"]}
    
    def add_task(self, task: SupervisorTask_Base, priority: int = -1) -> "SupervisorSupervisor":
        if priority == -1:
            priority = 100 + len(self._tasks) # If no priority is specified, add it to the end of the list while also being behind any tasks that DO have a specific priority
        with self._lock:
            if task.task_id in self._task_map:
                self.remove_task(task.task_id)
            entry = (priority, task)
            bisect.insort(self._tasks, entry)
            self._task_map[task.task_id] = entry
        return self

    def remove_task(self, task_id: str) -> Optional[SupervisorTask_Base]:
        with self._lock:
            entry = self._task_map.pop(task_id, None)
            if entry:
                index = bisect.bisect_left(self._tasks, entry)
                if index < len(self._tasks) and self._tasks[index][1].task_id == task_id:
                    self._tasks.pop(index)
                return entry[1]
            return None

    def update_task_priority(self, task_id: str, new_priority: int):
        with self._lock:
            task = self.remove_task(task_id)
            if task:
                self.add_task(task, new_priority)

    def get_tasks(self) -> list[SupervisorTask_Base]:
        with self._lock:
            return [entry[1] for entry in self._tasks]

    def get_tasks_with_priorities(self) -> list[Tuple[int, SupervisorTask_Base]]:
        with self._lock:
            return list(self._tasks)

    def has_task(self, task_id: str) -> bool:
        return task_id in self._task_map
    
    def get_task(self, task_name: str) -> Optional[SupervisorTask_Base]:
        """Returns the task object by its name."""
        for _, task in self._tasks:
            if task.name == task_name:
                return task
        return None

    def find_task(self, task_id: str) -> Optional[SupervisorTask_Base]:
        """Returns the task object by its task_id."""
        entry = self._task_map.get(task_id)
        return entry[1] if entry else None

    def get_task_priority(self, task_id: str) -> Optional[int]:
        entry = self._task_map.get(task_id)
        return entry[0] if entry else None

    def __repr__(self):
        return f"Supervisor({self.name}, tasks={[t[1].task_id for t in self._tasks]})"


class SupervisorSupervisor_ChainOfThought(SupervisorSupervisor):
    """
    Enhanced supervisor that implements chain of thought reasoning for task routing decisions.
    """
    enable_cot_routing: bool = True
    
    def __init__(self, name: str, prompt: str = "", prompt_id: str = "", tools: Sequence[Union[Dict[str, Any], type, Callable, BaseTool]] = [], model: BaseLanguageModel = None):
        super().__init__(name=name, prompt=prompt, prompt_id=prompt_id, tools=tools, model=model)
    
    def compile(self) -> "SupervisorSupervisor":
        """Override compile to route all tasks through CoT reasoning"""
        for _, task in self._tasks:
            if task.compiled_langgraph is None:
                task.compile_default()

        self.langgraph = StateGraph(state_schema=SupervisorTaskState, config_schema=SupervisorTaskConfig)
        
        # All tasks are routed through CoT reasoning - no automatic execution
        all_tasks = [task for _, task in self._tasks]
        
        # Add the supervisor router node and task nodes
        self.langgraph.add_node("llm_call_router", self.llm_call_router_wrapper)
        for _, task in self._tasks:
            self.langgraph.add_node(task.name, task.llm_call_internal)
        
        def end_func(state):
            return Command(update="")
        self.langgraph.add_node("END", end_func)

        # All execution starts with CoT router - no bypassing for always_call_FIRST
        self.langgraph.add_edge(START, "llm_call_router")
        
        # All tasks return to router after completion for continued CoT reasoning
        for task in all_tasks:
            self.langgraph.add_edge(task.name, "llm_call_router")
        
        # Router handles END transitions via goto commands in CoT logic
        self.langgraph.add_edge("END", END)
        
        self.compiled_langgraph = self.langgraph.compile(name=self.name)
        
        return self
    
    async def llm_call_router(self, state: SupervisorTaskState):
        """Route the input to the appropriate node using chain of thought reasoning"""
        user = await ZairaUserManager.find_user(state.user_guid)

        # Check if this is the first routing call in the chain
        is_first_call = len(state.completed_tasks) == 0
        
        # Separate tasks by priority
        always_call_FIRST_tasks = [task for _, task in self._tasks if task.always_call_FIRST == True]
        always_call_LAST_tasks = [task for _, task in self._tasks if task.always_call_LAST == True]
        conditional_tasks = [task for _, task in self._tasks if task.always_call_FIRST == False and task.always_call_LAST == False]
        
        # Handle always_call_FIRST tasks - execute in sequence before any conditional tasks
        unexecuted_always_call_FIRST = [task for task in always_call_FIRST_tasks 
                                       if task.name not in state.completed_tasks]
        
        if unexecuted_always_call_FIRST:
            first_task = unexecuted_always_call_FIRST[0]
            print(f"[DEBUG] {self.name} (CoT) executing next always_call_FIRST task: {first_task.name}")
            reasoning_steps = []
            if self.enable_cot_routing:
                reasoning_steps = [f"{self.name}: CoT routing to {first_task.name} (always_call_FIRST)"]
            return Command(update={
                "call_trace": state.call_trace + [f"{self.name}: goto {first_task.name} (always_call_FIRST)"], 
                "reasoning_steps": reasoning_steps,
                "completed_tasks": [self.name]
            }, goto=first_task.name)

        # Build task information for CoT reasoning - exclude priority tasks based on context
        tasks: list[str] = []
        task_prompts = []
        
        with self._lock:
            for _, task in self._tasks:
                # Include always_call_FIRST tasks only if they haven't been executed yet
                if task.always_call_FIRST:
                    if task.name not in state.completed_tasks:
                        tasks.append(task.name)
                        task_prompts.append(f"Task {task.name}'s prompt: {task.get_prompt()}. Tools: {', '.join(tool.name for tool in task.get_tools())}. [FIRST PRIORITY - execute if not done] End of tools for task {task.name}")
                # Include conditional tasks normally
                elif not task.always_call_LAST:
                    tasks.append(task.name)
                    task_prompts.append(f"Task {task.name}'s prompt: {task.get_prompt()}. Tools: {', '.join(tool.name for tool in task.get_tools())}. End of tools for task {task.name}")
                # Include always_call_LAST tasks only when considering END
                # (They will be handled in the END logic below)
                
            previous_tasks = []
            for task_name in state.completed_tasks:
                task = self.get_task(task_name)
                if task:
                    previous_tasks.append(task_name)

        supervisor_brain = "\n".join(task_prompts)
        user_info = {"name": user.username}
        chat_history = user.get_chat_history()
        messages = chat_history.copy()
        
        # Use CoT-enhanced prompt if available, otherwise fall back to regular prompt
        cot_prompt = PromptManager.get_prompt("Global_Supervisor_CoT_Prompt") if self.enable_cot_routing else PromptManager.get_prompt("Global_Supervisor_Prompt")
        
        messages.insert(0, SystemMessage(
         
            f" My prompt: {self.get_prompt(True)}\n" 
            " User info: " + " ".join(user_info) + "." 
            ", ".join(previous_tasks) + ". End of previous_tasks." 
            " Consider the above as your restraints." 
            
        ))
        # Context is already available through chat history and system prompts
        
        # Get structured decision with reasoning
        if self.enable_cot_routing:
            print(f"\n🧠 [CoT REASONING] {self.name} starting chain of thought...")
            
            # First get the raw reasoning response
            raw_response = await self.model.ainvoke(input=messages)
            if hasattr(raw_response, 'content'):
                print(f"🤔 [CoT THINKING] {self.name} reasoning process:")
                print("─" * 60)
                print(raw_response.content)
                print("─" * 60)
                
                # Add the reasoning response to messages for the structured output call
                messages_with_reasoning = messages.copy()
                messages_with_reasoning.insert(1, raw_response)
                messages_with_reasoning.insert(2, SystemMessage(content="Based on your reasoning above, what is your final decision? Respond with the exact task name or END."))
                
                decision: SupervisorRouteState = await self.model.with_structured_output(SupervisorRouteState).ainvoke(input=messages_with_reasoning)
            else:
                decision: SupervisorRouteState = await self.model.with_structured_output(SupervisorRouteState).ainvoke(input=messages)
        else:
            decision: SupervisorRouteState = await self.model.with_structured_output(SupervisorRouteState).ainvoke(input=messages)
        
        # Debug: Log the routing decision with detailed context
        print(f"[DEBUG] {self.name} routing decision: {decision.step}")
        print(f"[DEBUG] Previous tasks: {previous_tasks}")
        print(f"[DEBUG] All available tasks: {[t[1].name for t in self._tasks]}")
        print(f"[DEBUG] Conditional tasks (always_call=False): {[t[1].name for t in self._tasks if not t[1].always_call_LAST]}")
        print(f"[DEBUG] Always call tasks: {[t[1].name for t in self._tasks if t[1].always_call_LAST]}")
        print(f"[DEBUG] State completed_tasks: {getattr(state, 'completed_tasks', 'NO_COMPLETED_TASKS')}")
        print(f"[DEBUG] Original input: {getattr(state, 'original_input', 'NO_ORIGINAL_INPUT')}")
        
        # Validate the decision
        if decision.step != "END":
            available_task_names = [t[1].name for t in self._tasks]
            if decision.step not in available_task_names:
                print(f"[DEBUG] WARNING: {self.name} tried to route to invalid task '{decision.step}', forcing END")
                decision.step = "END"
            elif decision.step in previous_tasks:
                print(f"[DEBUG] WARNING: {self.name} tried to route to already completed task '{decision.step}', forcing END")
                decision.step = "END"
        
        # Handle transition to always_call_LAST tasks when ending conditional routing
        if decision.step == "END":
            # Check if we have always_call_LAST tasks that haven't been executed yet
            unexecuted_always_call_LAST = [task for _, task in self._tasks 
                                          if task.always_call_LAST == True and task.name not in state.completed_tasks]
            
            if unexecuted_always_call_LAST:
                # Execute always_call_LAST tasks in sequence when the main work is done
                first_always_task = unexecuted_always_call_LAST[0]
                print(f"[DEBUG] {self.name} (CoT) chain ending - executing next always_call_LAST task: {first_always_task.name}")
                reasoning_steps = []
                if self.enable_cot_routing:
                    reasoning_steps = [f"{self.name}: CoT routing to {first_always_task.name} (always_call_LAST)"]
                return Command(update={
                    "call_trace": state.call_trace + [f"{self.name}: goto {first_always_task.name} (always_call_LAST)"], 
                    "reasoning_steps": reasoning_steps,
                    "completed_tasks": [self.name]
                }, goto=first_always_task.name)
            
            print(f"[DEBUG] {self.name} (CoT) no more always_call_LAST tasks, going to END")
            reasoning_steps = []
            if self.enable_cot_routing:
                reasoning_steps = [f"{self.name}: CoT routing to END"]
            return Command(update={
                "call_trace": state.call_trace + [f"{self.name}: goto END"], 
                "reasoning_steps": reasoning_steps,
                "completed_tasks": [self.name]
            }, goto="END")

        # Extract reasoning if CoT was used
        reasoning_steps = []
        if self.enable_cot_routing:
            reasoning_steps = [f"{self.name}: CoT routing to {decision.step}"]

        return Command(update={
            "call_trace": state.call_trace + [f"{self.name}: goto {decision.step}"], 
            "reasoning_steps": reasoning_steps,
            "completed_tasks": [self.name]
        }, goto=decision.step)


class _SupervisorManagerMeta(type):
    _instance = None
    _lock: Lock = None

    def __call__(cls, *args, **kwargs):
        if cls._lock is None:
            cls._lock = Lock()

        with cls._lock:
            if cls._instance is None:
                cls._instance = super().__call__(*args, **kwargs)
        return cls._instance

class SupervisorManager(metaclass=_SupervisorManagerMeta):
    # Initialize model
    _instance = None
    _initialized = False

    default_model: BaseLanguageModel = None
    _supervisors: Dict[str, SupervisorSupervisor] = {}
    _all_tasks: Dict[str, SupervisorTask_Base] = {}
    _lock: Lock = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls) -> "SupervisorManager":
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        instance.default_model = ZairaSettings.llm
        instance._supervisors = {}
        instance._all_tasks = {}
        instance._lock = Lock()

        instance._initialized = True

    @classmethod
    def register_task(cls, task: SupervisorTask_Base) -> SupervisorTask_Base:
        instance = cls.get_instance()
        with instance._lock:
            instance._all_tasks[task.task_id] = task
        return task

    @classmethod
    def register_supervisor(cls, supervisor: SupervisorSupervisor) -> SupervisorSupervisor:
        instance = cls.get_instance()
        with instance._lock:
            instance._supervisors[supervisor.name] = supervisor
            #instance._all_tasks[supervisor.task_id] = supervisor # All supervisors can also be used as a task, not sure if we should add them to the list
        return supervisor

    @classmethod
    def get_supervisor(cls, name: str) -> Optional[SupervisorSupervisor]:
        instance = cls.get_instance()
        return instance._supervisors.get(name)

    @classmethod
    def add_task_to_supervisor(cls, task: SupervisorTask_Base, supervisor_name: str, priority: int):
        cls.register_task(task)
        supervisor = cls.get_supervisor(supervisor_name)
        if not supervisor:
            raise ValueError(f"Supervisor '{supervisor_name}' not found.")
        supervisor.add_task(task, priority)

    @classmethod
    def transfer_task(cls, task_id: str, from_supervisor: str, to_supervisor: str, new_priority: Optional[int] = None):
        src = cls.get_supervisor(from_supervisor)
        dst = cls.get_supervisor(to_supervisor)
        if not src or not dst:
            raise ValueError("One or both supervisors not found.")
        task = src.remove_task(task_id)
        if task:
            priority = new_priority if new_priority is not None else 0
            dst.add_task(task, priority)
        else:
            raise ValueError(f"Task {task_id} not found in {from_supervisor}.")

    @classmethod
    def find_task(cls, task_id: str) -> Optional[SupervisorTask_Base]:
        instance = cls.get_instance()
        return instance._all_tasks.get(task_id)
    
    @classmethod
    def get_task(cls, task_name: str) -> Optional[SupervisorTask_Base]:
        instance = cls.get_instance()
        with instance._lock:
            for task in instance._all_tasks.values():
                if task.name == task_name:
                    return task
        return None

    @classmethod
    def get_all_tasks(cls) -> list[SupervisorTask_Base]:
        instance = cls.get_instance()
        with instance._lock:
            return list(instance._all_tasks.values())

    @classmethod
    def __repr__(cls):
        instance = cls.get_instance()
        return f"Manager(supervisors={list(instance._supervisors.keys())}, all_tasks={list(instance._all_tasks.keys())})"
    
#from userprofiles.ZairaUser import ZairaUser
#SupervisorTaskState.model_rebuild()
