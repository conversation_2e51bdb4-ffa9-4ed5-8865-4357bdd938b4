#!/usr/bin/env python3
"""
Comprehensive WhatsApp Bot Test Runner

This script provides a unified interface for running all WhatsApp bot tests
with proper setup, teardown, and reporting.
"""

import asyncio
import argparse
import subprocess
import sys
import time
import json
import os
from pathlib import Path
from typing import Dict, List, Optional

class WhatsAppTestRunner:
    """Comprehensive test runner for WhatsApp bot"""
    
    def __init__(self):
        self.test_results: Dict[str, Dict] = {}
        self.mock_server_process: Optional[subprocess.Popen] = None
        self.start_time = time.time()
        
    def setup_environment(self):
        """Setup test environment"""
        print("🔧 Setting up test environment...")
        
        # Ensure we're in the right directory
        os.chdir(Path(__file__).parent)
        
        # Add src to Python path
        src_path = Path.cwd()
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
        
        # Verify imports work
        try:
            from imports import *
            print("✅ Imports successful")
        except ImportError as e:
            print(f"❌ Import error: {e}")
            return False
        
        # Check required dependencies
        required_packages = ['pytest', 'pytest-asyncio', 'httpx', 'aiohttp', 'psutil']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ Missing packages: {', '.join(missing_packages)}")
            print(f"Install with: pip install {' '.join(missing_packages)}")
            return False
        
        print("✅ All dependencies available")
        return True
    
    async def start_mock_server(self, port: int = 8080) -> bool:
        """Start mock WhatsApp API server"""
        print(f"🚀 Starting mock WhatsApp server on port {port}...")
        
        try:
            # Import and start mock server
            from tests.mock_whatsapp_server import MockWhatsAppServer
            
            server = MockWhatsAppServer(port)
            runner = await server.start()
            
            # Verify server is running
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.get(f"http://localhost:{port}/health")
                if response.status_code == 200:
                    print(f"✅ Mock server running on http://localhost:{port}")
                    return True
                else:
                    print(f"❌ Mock server health check failed: {response.status_code}")
                    return False
                    
        except Exception as e:
            print(f"❌ Failed to start mock server: {e}")
            return False
    
    def run_test_category(self, category: str, test_path: str, markers: List[str] = None) -> Dict:
        """Run a specific test category"""
        print(f"\n📋 Running {category} tests...")
        
        cmd = ["python", "-m", "pytest", test_path, "-v"]
        
        if markers:
            for marker in markers:
                cmd.extend(["-m", marker])
        
        # Add coverage for relevant tests
        if category in ["unit", "integration"]:
            cmd.extend([
                "--cov=endpoints.whatsapp_endpoint",
                "--cov-append"
            ])
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            success = result.returncode == 0
            
            test_result = {
                "success": success,
                "duration": duration,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
            
            if success:
                print(f"✅ {category} tests passed ({duration:.1f}s)")
            else:
                print(f"❌ {category} tests failed ({duration:.1f}s)")
                print(f"Error output: {result.stderr}")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            print(f"⏰ {category} tests timed out")
            return {
                "success": False,
                "duration": 300,
                "error": "Test timeout",
                "returncode": -1
            }
        except Exception as e:
            print(f"❌ Error running {category} tests: {e}")
            return {
                "success": False,
                "duration": 0,
                "error": str(e),
                "returncode": -1
            }
    
    def run_basic_validation(self) -> Dict:
        """Run basic validation tests"""
        print("\n🔍 Running basic validation...")
        
        try:
            # Test basic bot functionality
            result = subprocess.run(
                ["python", "test_whatsapp.py"],
                capture_output=True,
                text=True,
                timeout=60,
                input="n\n"  # Don't send actual messages
            )
            
            success = "All tests passed" in result.stdout or result.returncode == 0
            
            return {
                "success": success,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "returncode": result.returncode
            }
            
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "returncode": -1
            }
    
    def generate_report(self):
        """Generate comprehensive test report"""
        print("\n📊 Generating test report...")
        
        total_duration = time.time() - self.start_time
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get("success", False))
        
        report = {
            "summary": {
                "total_duration": total_duration,
                "total_test_categories": total_tests,
                "passed_categories": passed_tests,
                "success_rate": (passed_tests / total_tests * 100) if total_tests > 0 else 0,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            },
            "results": self.test_results
        }
        
        # Save detailed report
        report_file = f"whatsapp_test_report_{int(time.time())}.json"
        with open(report_file, 'w') as f:
            json.dump(report, f, indent=2)
        
        # Print summary
        print(f"\n{'='*60}")
        print(f"📋 WHATSAPP BOT TEST SUMMARY")
        print(f"{'='*60}")
        print(f"Total Duration: {total_duration:.1f}s")
        print(f"Test Categories: {passed_tests}/{total_tests} passed")
        print(f"Success Rate: {report['summary']['success_rate']:.1f}%")
        print(f"Detailed Report: {report_file}")
        
        # Print category results
        for category, result in self.test_results.items():
            status = "✅ PASS" if result.get("success", False) else "❌ FAIL"
            duration = result.get("duration", 0)
            print(f"{category:20} {status:8} ({duration:.1f}s)")
        
        if passed_tests == total_tests:
            print(f"\n🎉 All tests passed! WhatsApp bot is ready for production.")
        else:
            print(f"\n⚠️  Some tests failed. Review the detailed report for issues.")
        
        return report
    
    async def run_all_tests(self, categories: List[str] = None, skip_mock_server: bool = False):
        """Run all test categories"""
        print("🚀 Starting WhatsApp Bot Test Suite")
        print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # Setup environment
        if not self.setup_environment():
            print("❌ Environment setup failed")
            return False
        
        # Start mock server if needed
        if not skip_mock_server:
            server_started = await self.start_mock_server()
            if not server_started:
                print("⚠️  Mock server failed to start, some tests may fail")
        
        # Define test categories
        all_categories = {
            "validation": {
                "name": "Basic Validation",
                "runner": self.run_basic_validation
            },
            "unit": {
                "name": "Unit Tests",
                "path": "tests/unit/test_whatsapp_components.py",
                "markers": ["unit"]
            },
            "integration": {
                "name": "Integration Tests", 
                "path": "tests/integration/test_whatsapp_integration.py",
                "markers": ["integration"]
            },
            "e2e": {
                "name": "End-to-End Tests",
                "path": "tests/integration/test_whatsapp_e2e.py",
                "markers": ["e2e"]
            },
            "performance": {
                "name": "Performance Tests",
                "path": "tests/performance/test_whatsapp_performance.py",
                "markers": ["performance"]
            },
            "security": {
                "name": "Security Tests",
                "path": "tests/security/test_whatsapp_security.py",
                "markers": ["security"]
            }
        }
        
        # Filter categories if specified
        if categories:
            test_categories = {k: v for k, v in all_categories.items() if k in categories}
        else:
            test_categories = all_categories
        
        # Run each test category
        for category_key, category_info in test_categories.items():
            if "runner" in category_info:
                # Custom runner
                result = category_info["runner"]()
            else:
                # Standard pytest runner
                result = self.run_test_category(
                    category_info["name"],
                    category_info["path"],
                    category_info.get("markers", [])
                )
            
            self.test_results[category_key] = result
        
        # Generate final report
        report = self.generate_report()
        
        return report["summary"]["success_rate"] == 100.0

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description="WhatsApp Bot Test Runner")
    parser.add_argument(
        "--categories",
        nargs="+",
        choices=["validation", "unit", "integration", "e2e", "performance", "security"],
        help="Test categories to run (default: all)"
    )
    parser.add_argument(
        "--skip-mock-server",
        action="store_true",
        help="Skip starting mock server"
    )
    parser.add_argument(
        "--quick",
        action="store_true",
        help="Run only validation and unit tests"
    )
    
    args = parser.parse_args()
    
    # Set categories based on arguments
    if args.quick:
        categories = ["validation", "unit"]
    else:
        categories = args.categories
    
    # Run tests
    runner = WhatsAppTestRunner()
    
    try:
        success = asyncio.run(runner.run_all_tests(
            categories=categories,
            skip_mock_server=args.skip_mock_server
        ))
        
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️  Test run interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test run failed with error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
