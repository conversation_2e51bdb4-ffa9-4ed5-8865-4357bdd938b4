from imports import *

from aiohttp import web

from endpoints.oauth._verifier_ import OAuth2App

class OAuth2New(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_input("input", ["str:client_id", "str:client_secret", "str:auth_url", "str:token_url"])

    async def on_success_return(self) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return()
        ret_html += "Airtable wordt opgehaald."
        return ret_html

    async def on_success_execute(self) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        ret_html = await super().on_success_execute()
        from managers.manager_meltano import MeltanoManager
        await MeltanoManager.RunTap("woocommerce")
        await MeltanoManager.ConvertSQLToVectorStore()
        ret_html += "Airtables opgehaald!"
        return ret_html
    
    async def on_success_execute_fail(self) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute()
        ret_html += ""
        return ret_html
