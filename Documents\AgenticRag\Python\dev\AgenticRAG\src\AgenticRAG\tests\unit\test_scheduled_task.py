"""
Test script for ScheduledZairaTask implementation
"""
import sys
import os
from datetime import datetime, timedelta

# Add the project root to the path so we can import our modules
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

from userprofiles.ScheduledZairaTask import ScheduledZairaTask, ScheduleType

def test_schedule_parsing():
    """Test the schedule parsing functionality"""
    print("Testing schedule parsing...")
    
    # Create a mock task instance to test parsing
    class MockUser:
        pass
    
    class MockBot:
        name = "test_bot"
    
    test_cases = [
        ("trigger IMAP IDLE every 30 minutes", "trigger IMAP IDLE", 1800.0, ScheduleType.RECURRING),
        ("send me a good morning message at 9am monday to friday", "send message: a good morning message", None, ScheduleType.RECURRING),
        ("email me a report every first of the month", "email report: a report", None, ScheduleType.RECURRING),
        ("check status every 5 seconds", "check status", 5.0, ScheduleType.RECURRING),
        ("backup database in 2 hours", "backup database", 7200.0, ScheduleType.ONCE),
    ]
    
    for i, (prompt, expected_target, expected_delay, expected_type) in enumerate(test_cases):
        print(f"\nTest case {i+1}: '{prompt}'")
        try:
            # Create a temporary task instance
            mock_user = MockUser()
            mock_bot = MockBot()
            task = ScheduledZairaTask(mock_user, mock_bot, None, prompt)
            
            print(f"  Parsed target: '{task.target_prompt}'")
            print(f"  Expected target: '{expected_target}'")
            print(f"  Target match: {task.target_prompt == expected_target}")
            
            if expected_delay is not None:
                print(f"  Parsed delay: {task.delay_seconds} seconds")
                print(f"  Expected delay: {expected_delay} seconds")
                print(f"  Delay match: {task.delay_seconds == expected_delay}")
            
            print(f"  Parsed type: {task.schedule_type}")
            print(f"  Expected type: {expected_type}")
            print(f"  Type match: {task.schedule_type == expected_type}")
            
            # Test schedule info
            schedule_info = task.get_schedule_info()
            print(f"  Schedule info: {schedule_info}")
            
        except Exception as e:
            print(f"  ERROR: {e}")
            import traceback
            traceback.print_exc()

def test_time_calculations():
    """Test time calculation methods"""
    print("\n\nTesting time calculations...")
    
    class MockUser:
        pass
    
    class MockBot:
        name = "test_bot"
    
    mock_user = MockUser()
    mock_bot = MockBot()
    
    # Test conversion to seconds
    task = ScheduledZairaTask(mock_user, mock_bot, None, "test")
    
    test_conversions = [
        (5, "second", 5.0),
        (10, "minute", 600.0),
        (2, "hour", 7200.0),
        (1, "day", 86400.0),
    ]
    
    for interval, unit, expected in test_conversions:
        result = task._convert_to_seconds(interval, unit)
        print(f"  {interval} {unit}(s) = {result} seconds (expected: {expected})")
        print(f"  Match: {result == expected}")

if __name__ == "__main__":
    test_schedule_parsing()
    test_time_calculations()
    print("\n\nTest completed!")