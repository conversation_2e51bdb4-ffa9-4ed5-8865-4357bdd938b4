from imports import *

from langchain_core.tools import tool
import logging
import smtplib
import base64
from typing import Optional

from managers.manager_supervisors import Supervisor<PERSON><PERSON><PERSON>, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaTask import LongRunningZairaTask

# Define tools for agents

@tool
async def email_tool(content: str, subject: Optional[str], sender: Optional[str], recipient: Optional[str], state: SupervisorTaskState):
    """Creates a mail or email with the given content and subject coming from sender and being sent to recipient"""
    class EmailClient:
        """
        Handles mail sending operations.
        """
        sender: str = ""
        recipient: str = ""

        smtp_server: str = ""
        smtp_port: int = 0
        bearer_token: str = ""
        refresh_token:str = ""

        async def send_email(self, subject: str, content: str) -> bool:
            """
            Send an email to the specified recipient.

            :param recipient: Email address of the recipient.
            :param subject: Subject of the email.
            :param content: Content of the email.
            :return: True if email is sent successfully, False otherwise.
            """
            try:
                if self.smtp_server == "" or self.smtp_port == 0:
                    await user.my_task.send_response(f"{self.sender}'s email provider is nog niet geïmplementeerd. Contacteer <EMAIL> met het verzoek om deze toe te voegen.", False)
                if self.smtp_server == "smtp.gmail.com":
                    if not self.bearer_token:
                        logging.error("Failed to obtain a valid token. Email not sent.")
                        return False
                    from google.oauth2.credentials import Credentials
                    from googleapiclient.discovery import build
                    from email.message import EmailMessage

                    # Call the Gmail API
                    token_info = {
                        "client_id": OAuth2Verifier.get_instance().oauth_client_keys["google"][0],
                        "client_secret": OAuth2Verifier.get_instance().oauth_client_keys["google"][1],
                        "refresh_token": await OAuth2Verifier.get_instance().get_token(identifier="google", token_type="refresh_token"),
                        "token_uri": OAuth2Verifier.get_instance().oauth_auth_token_urls["google"][1],
                        "access_token": await OAuth2Verifier.get_instance().get_token(identifier="google"),
                        "expires_in": await OAuth2Verifier.get_instance().get_token(identifier="google", token_type="expires_in"),
                        "scopes": OAuth2Verifier.get_instance().apps["google"].scopes,
                    }
                    service = build("gmail", "v1", credentials=Credentials.from_authorized_user_info(token_info))
                    
                    def create_message():
                        message = EmailMessage()
                        message.set_content(content)
                        message['To'] = self.recipient
                        message['From'] = self.sender
                        message['Subject'] = subject

                        raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
                        return {'raw': raw_message}
                    message = create_message()
                    sent_message = service.users().messages().send(userId="me", body=message).execute()
                    print(f"Message ID: {sent_message['id']}")
                else:
                    if self.smtp_port == 465:
                        session = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
                    else:
                        session = smtplib.SMTP(self.smtp_server, self.smtp_port)
                        if "starttls" in session.ehlo():
                            session.starttls()
                    # Authenticate using username and password
                    session.login(self.sender, email_client.refresh_token)
                    # auth_string = (
                    #     b"user="
                    #     + bytes(self.sender, "ascii")
                    #     + b"\1auth=Bearer "
                    #     + self.bearer_token.encode()
                    #     + b"\1\1"
                    # )
                    # session.docmd("AUTH", "XOAUTH2 " + (base64.b64encode(auth_string)).decode("ascii"))
                    # Prepare and send the email
                    from email.mime.text import MIMEText
                    from email.mime.multipart import MIMEMultipart
                    
                    msg = MIMEMultipart()
                    msg['From'] = self.sender
                    msg['To'] = self.recipient
                    msg['Subject'] = subject
                    msg.attach(MIMEText(content, 'plain', 'utf-8'))
                    
                    session.sendmail(self.sender, self.recipient, msg.as_string())
                    session.quit()
                    await user.my_task.send_response(f"Mail is verzonden.", False)
                return True
            except Exception as e:
                print(f"Failed to send email: {e}")
                etc.helper_functions.exception_triggered(e)
                return False
    email_client = EmailClient()
    user = await ZairaUserManager.find_user(state.user_guid)

    if "@gmail.com" in email_client.sender:
        email_client.smtp_server = "smtp.gmail.com"
        email_client.smtp_port = 587
        email_client.bearer_token = await OAuth2Verifier.get_token("google")
        email_client.refresh_token = await OAuth2Verifier.get_token("google", "refresh_token")
    else:
        email_client.smtp_server = await OAuth2Verifier.get_token("smtp", "access_token")
        email_client.smtp_port = await OAuth2Verifier.get_token("smtp", "expires_in")
        email_client.bearer_token = await OAuth2Verifier.get_token("smtp", "refresh_token")
        email_client.refresh_token = await OAuth2Verifier.get_token("smtp", "token_type")
    
    if not sender or not '@' in sender:
        if user.email == "":
            user.email = email_client.bearer_token
            if user.email == "":
                async def set_user_email(task: LongRunningZairaTask, response: str):
                    user.email = response
                await user.my_task.request_human_in_the_loop("Je e-mail adres is nog niet bekend bij ons. Van waaruit moet de mail verzonden worden?", set_user_email, True)
        email_client.sender = user.email
    else:
        email_client.sender = sender
    if not recipient or not '@' in recipient:
        async def set_recipient(task: LongRunningZairaTask, response: str):
            if response and '@' in response:
                email_client.recipient = response
            else:
                # Invalid email, ask again
                await user.my_task.request_human_in_the_loop(f"'{response}' is geen geldig e-mailadres. Geef een geldig e-mailadres op (bijvoorbeeld: <EMAIL>):", set_recipient, True)
        await user.my_task.request_human_in_the_loop("Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden?", set_recipient, True)
    else:
        email_client.recipient = recipient
    email_template = f"""
    From: {email_client.sender}
    To: {email_client.recipient}
    Subject: {subject}

    {content}

    .Mail opgesteld en verstuurd met AskZaira.
    """
    
    async def set_and_send_mail(task:LongRunningZairaTask, response: str):
        if response:
            if response[0:2] == 'nee':
                pass
            else:
                await email_client.send_email(subject, response)
    
    async def send_mail(task: LongRunningZairaTask, response: str):
        if response and response[0] == 'j':
            await email_client.send_email(subject, content)
        else:
            await user.my_task.request_human_in_the_loop(f"Kopieer de mail en pas hem aan zodat ik hem kan versturen. Reageer met 'nee' als je wilt dat ik het versturen afbreek.\n\n{content}", set_and_send_mail, True)
    await user.my_task.request_human_in_the_loop(f"Ik heb de volgende mail opgesteld. Wil je dat ik deze verstuur? (j/n)\n\n{email_template}", send_mail, True)

    return email_template.strip()

async def create_supervisor_email_writer() -> SupervisorSupervisor:
    class TaskCreator:
        email_task: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.email_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(name="mail_expert", tools=[email_tool], prompt_id="Task_EmailWriter_Agent"))
        
        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(SupervisorSupervisor(name="email_supervisor", prompt_id="Task_EmailWriter_Supervisor")) \
                .add_task(self.email_task) \
                .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()
