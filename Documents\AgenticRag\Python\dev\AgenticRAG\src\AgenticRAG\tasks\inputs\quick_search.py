from imports import *

from os import path as os_path
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTask_SingleAgent, SupervisorTaskState
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_meltano import MeltanoManager
from managers.manager_users import ZairaUserManager
from inputs.user_query import rag_data, llm_data, complexity_data

class SupervisorTask_Quick_RAG(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)
        ragdata = await rag_data(Globals.get_query_engine_default(), input_message, user)
        if Globals.is_debug_values():
            LogFire.log("TASK", "", "RAGed: " + ragdata)
        return ragdata

class SupervisorTask_Quick_LLM(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)
        llmdata = await llm_data(input_message, user)
        if not llmdata:
            print("No relevant data found for your query.")
            return
        if Globals.is_debug_values():
            LogFire.log("TASK", "", "LLMed: " + llmdata)
        return llmdata

class SupervisorTask_Quick_Complexity(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)
        complexity_score = await complexity_data(input_message, user)
        if Globals.is_debug_values():
            LogFire.log("TASK", "", "Compl: " + str(complexity_score))
        return ""

async def create_task_quick_rag_search() -> SupervisorTask_Base:
    ret_val = SupervisorManager.register_task(SupervisorTask_Quick_RAG(name="quick_rag_task", prompt_id="Quick_RAG_Search"))
    #ret_val.always_call_FIRST = True
    return ret_val

async def create_task_quick_llm_search() -> SupervisorTask_Base:
    ret_val = SupervisorManager.register_task(SupervisorTask_Quick_LLM(name="quick_llm_task", prompt_id="Quick_LLM_Search"))
    
    #ret_val.always_call_FIRST = True
    return ret_val

async def create_task_quick_complexity_search() -> SupervisorTask_Base:
    ret_val = SupervisorManager.register_task(SupervisorTask_Quick_Complexity(name="quick_complexity_task", prompt="Do nothing"))
    
    #ret_val.always_call_FIRST = True
    return ret_val
