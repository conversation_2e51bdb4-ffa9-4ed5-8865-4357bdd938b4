from imports import *
from datetime import datetime, timezone
from pydantic import BaseModel
import logfire
import inspect
from typing import Literal, Optional, TYPE_CHECKING
from asyncio import get_running_loop as asyncio_get_running_loop
from asyncio import run as asyncio_run
from asyncio import sleep, Queue, create_task

from managers.manager_postgreSQL import PostgreSQLManager

# https://logfire-us.pydantic.dev/askzaira/agentic-rag

if TYPE_CHECKING:
    from asyncio import Task
    from userprofiles.ZairaUser import ZairaUser

class LogFire:
    _instance = None
    _initialized = False
    asyncio_Task_await_response: "Task" = None
    isLogging = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        if cls._initialized:
            return
        logfire.configure(token='pylf_v1_eu_PdlSrNHG8TsSLXGvf87cKtXxKtQ28dV3Tdr0GZVlvMTT')
        #logfire.instrument_pydantic()
        cls._initialized = True

    @classmethod    
    async def logfire_middleware(cls, request, handler):
        #with logfire.span(msg_template="Handling request {method} {path}", _span_name=f"{request.method} {request.path}"):
        response = await handler(request)
        return response
    
    @classmethod
    def get_caller_name(cls):
        # Stack[0] = get_caller_name
        # Stack[1] = function that called get_caller_name
        # Stack[2] = function that called that one (and so on)
        stack = inspect.stack()
        ret_val = ""
        if len(stack) > 3:
            ret_val += stack[3].function
            ret_val += " -> " + stack[2].function
        return ret_val

    @classmethod
    def log(cls, event_code: Literal["INIT", "RETRIEVE", "TOP", "TASK", "OUTPUT", "USER", "ERROR"], data_logfire: str, suffix_sql: str = "", user: Optional["ZairaUser"] = None, source: str = "", exception: str = "", severity: Literal["debug", "info", "warning", "error"] = "info"):
        # Skip logging if running in Claude environment
        from etc.helper_functions import is_claude_environment
        if is_claude_environment():
            return
            
        # Initialize class-level queue if it doesn't exist
        if not hasattr(cls, '_log_queue'):
            cls._log_queue = Queue()
            cls._log_worker_started = False
        
        async def log_worker():
            """Worker that processes log entries in FIFO order"""
            while True:
                try:
                    log_entry = await cls._log_queue.get()
                    if log_entry is None:  # Shutdown signal
                        break
                    
                    instance = cls.get_instance()
                    await instance.log_internal(*log_entry)
                    cls._log_queue.task_done()
                    
                except Exception as e:
                    print(f"Error during logging: {e}")
                    cls._log_queue.task_done()
        
        async def enqueue_log():
            """Enqueue the log entry and ensure worker is running"""
            try:
                mysource = cls.get_caller_name() if source == "" else source
                log_data = (
                    event_code,
                    f"{data_logfire} " + (suffix_sql if Globals.is_debug_values() else ""),
                    f"{data_logfire} {suffix_sql}",
                    user,
                    mysource,
                    exception,
                    severity
                )
                
                # Start worker if not already running
                if not cls._log_worker_started:
                    cls._log_worker_started = True
                    create_task(log_worker())
                
                await cls._log_queue.put(log_data)
                
            except Exception as e:
                print(f"Error enqueueing log: {e}")
        
        # Execute the enqueueing logic
        try:
            loop = asyncio_get_running_loop()
            loop.create_task(enqueue_log())
        except RuntimeError:
            # No event loop running, run synchronously
            asyncio_run(enqueue_log())

    # @classmethod
    # def log(cls, event_code: Literal["INIT", "RETRIEVE", "TOP", "TASK", "OUTPUT", "USER", "ERROR"], data_logfire: str, suffix_sql: str = "", user: Optional["ZairaUser"] = None, source: str = "", exception: str = "", severity: Literal["debug", "info", "warning", "error"] = "info"):
    #     async def runthreadsafe(mysource):
    #         try:
    #             instance = cls.get_instance()
    #             while instance.isLogging == True:
    #                 await sleep(1)
    #             instance.isLogging = True
    #             await cls.get_instance().log_internal(event_code, f"{data_logfire} " + (suffix_sql if Globals.is_debug_values() else ""), f"{data_logfire} {suffix_sql}", user, mysource, exception, severity)
    #             instance.isLogging = False
    #         except Exception as e:
    #             print("Error during logging. Welp!")
    #     try:
    #         mysource = cls.get_caller_name() if source == "" else source
    #         loop = asyncio_get_running_loop()
    #         loop.create_task(runthreadsafe(mysource))
    #     except RuntimeError:
    #         asyncio_run(runthreadsafe())
    #         return

    @classmethod
    async def log_internal(cls, event_code: str, data_logfire: str, data_sql: str, user: Optional["ZairaUser"], source: str, exception: str, severity: str):
        instance = cls.get_instance()
        timestamp = datetime.now(timezone.utc)

        company = etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME", "")
        data_sql = data_sql.replace('\n', '\t\t\t')
        params = [
            timestamp,
            user.GUID if user else None,
            user.sessionGUID if user else None,
            severity,
            source,
            event_code,
            data_sql,
            exception,
            "{" + f"""\"chat length#\":{len(user.chat_history[user.sessionGUID]) if user else "-1"}""" + "}"#, \"environment\": \"{company}\"""" + "}",
        ]

        # Also store log in the database
        await PostgreSQLManager.connect_to_database("vectordb")
        query = """INSERT INTO LogEntries (Timestamp, UserID, SessionID, Level, Source, EventCode, Value, Exception, MetaData) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)"""
        await PostgreSQLManager.execute_query("vectordb", query, params)
        await PostgreSQLManager.close_connection("vectordb")
        
        # Finally when all other places are logged, add to LogFire
        attributes = {
            "timestamp": params[0],
            "userGUID": str(params[1]),
            "sessieGUID": str(params[2]),
            "severity": params[3],
            "source": params[4],
            "event_code": params[5],
            "value": data_logfire,
            "exception": params[7],
            "metadata": params[8],
            "company": company
        }
        logfire.log(level=severity, msg_template="""[{company}][{event_code}], '{source}': {value}. {exception} Metadata: {metadata}. User {userGUID} on session {sessieGUID} at {timestamp}""", attributes=attributes)

