from imports import *

from aiohttp import web

from endpoints.oauth._verifier_ import OAuth2App

class OAuth2Discord(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_oauth("comm", ["bot", "applications.commands"], DISCORD_BOT_CLIENT_ID, DISCORD_BOT_CLIENT_SECRET, "https://discord.com/oauth2/authorize?permissions=274877975616", "https://discord.com/api/oauth2/token") \

    async def on_success_return(self, request: web.Request) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return(request)
        target_url = Globals.get_endpoint_address()
        ret_html += f"""Server wordt herstart. Geef me even 5 minuutjes en probeer vervolgens <a href='{target_url}'>hier</a> opnieuw in te loggen
                    <script>
                        // Redirect after 300 seconds
                        setTimeout(function() {{
                            window.location.href = "{target_url}";
                        }}, 300000);
                    </script>"""
        return ret_html

    async def on_success_execute(self, request: web.Request) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        ret_html = await super().on_success_execute(request)
        if Globals.is_docker():
            from asyncio import sleep
            await sleep(15)
            exit()
        return ret_html
    
    async def on_success_execute_fail(self, request: web.Request) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute(request)
        ret_html += ""
        return ret_html
