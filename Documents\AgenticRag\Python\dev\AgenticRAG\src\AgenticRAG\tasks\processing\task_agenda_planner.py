from imports import *

from langchain_core.tools import tool
import logging
import smtplib
import base64
from typing import Optional, Any
from datetime import datetime, timedelta
import asyncio
import re
import pytz
import os

from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTask_Create_agent, SupervisorTaskState, SupervisorTask_Base
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaTask import LongRunningZairaTask

import json
from googleapiclient.discovery import build
from google.oauth2.credentials import Credentials
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field


try:
    from langchain_google_community import CalendarToolkit
    from langchain_google_community.calendar.create_event import CalendarCreateEvent
    from langchain_google_community.calendar.utils import (
        build_resource_service,
        get_google_credentials,
    )
    CALENDAR_IMPORTS_AVAILABLE = True
except ImportError as e:
    logging.error(f"Google Calendar dependencies not available: {e}")
    CALENDAR_IMPORTS_AVAILABLE = False


def format_event_preview(event_details: dict) -> str:
    """Format event details for user preview"""
    try:
        # Parse datetime strings for better display
        start_time = event_details.get('start', {}).get('dateTime', 'Niet opgegeven')
        end_time = event_details.get('end', {}).get('dateTime', 'Niet opgegeven')
        
        # Format attendees
        attendees = event_details.get('attendees', [])
        attendee_emails = []
        if attendees:
            for attendee in attendees:
                if isinstance(attendee, dict):
                    attendee_emails.append(attendee.get('email', ''))
                else:
                    attendee_emails.append(str(attendee))
        
        attendee_list = ', '.join(filter(None, attendee_emails)) if attendee_emails else 'Geen'
        
        return f"""
Afspraak details:
Titel: {event_details.get('summary', 'Geen titel')}
Start: {start_time}
Eind: {end_time}
Locatie: {event_details.get('location', 'Geen')}
Deelnemers: {attendee_list}
        """.strip()
    except Exception as e:
        logging.error(f"Error formatting event preview: {e}")
        return f"Fout bij het formatteren van afspraak details: {str(e)}"


class CalendarCreateEventInput(BaseModel):
    """Input schema for calendar create event with confirmation"""
    event_details: dict = Field(description="Event details in Google Calendar API format")
    state: Optional[Any] = Field(default=None, description="Supervisor state for context")


class CalendarCreateEventWithConfirmation(BaseTool):
    """Custom calendar create event tool with human-in-the-loop confirmation"""
    name: str = "CalendarCreateEvent"
    description: str = "Create a new calendar event with user confirmation preview"
    args_schema: type = CalendarCreateEventInput
    
    def __init__(self, original_tool, **kwargs):
        super().__init__(**kwargs)
        self.original_tool = original_tool
    
   
    
    async def _arun(self, event_details: dict, state: Optional[Any] = SupervisorTaskState) -> str:
        """Create calendar event with user confirmation"""
        task = SupervisorManager.get_task("agenda_task")
        user_guid = state.user_id
        
        try:
            # Get current user and task from injected state
            if not state:
                return "Fout: Geen state beschikbaar voor bevestiging"
            
            if not hasattr(state, 'user_guid') or not state.user_guid:
                return "Fout: Geen gebruiker ID gevonden in state"
            
            from managers.manager_users import ZairaUserManager
            user = await ZairaUserManager.find_user(state.user_guid)
            if not user or not hasattr(user, 'my_task') or not user.my_task:
                return "Fout: Geen gebruikerstaak gevonden voor bevestiging"
            
            # Format event preview
            event_preview = format_event_preview(event_details)
            
            # Request human confirmation
            confirmation_message = f"Wil je deze afspraak aanmaken? (j/n)\n\n{event_preview}"
            
            # Define callback to capture user response
            self.user_response = None
            async def confirmation_callback(task, response):
                self.user_response = response
                return response
            
            # Request confirmation from user
            await user.my_task.request_human_in_the_loop(
                confirmation_message,
                confirmation_callback,
                True  # Wait for response
            )
            
            # Wait for user response
            while self.user_response is None:
                await asyncio.sleep(0.1)
            
            confirmed = self.user_response
            
            # Check if user confirmed
            if not confirmed or str(confirmed).lower() not in ['j', 'ja', 'yes', 'y']:
                return "Afspraak aanmaken geannuleerd door gebruiker"
            
            # User confirmed, create the event using original tool
            if hasattr(self.original_tool, '_arun'):
                result = await self.original_tool._arun(event_details)
            else:
                result = self.original_tool._run(event_details)
            
            return f"Afspraak succesvol aangemaakt na bevestiging!\n\nDetails:\n{result}"
            
        except Exception as e:
            logging.error(f"Error in calendar create with confirmation: {e}")
            return f"Fout bij aanmaken afspraak: {str(e)}"


async def create_direct_calendar_event(event_data: dict, api_resource=None) -> dict:
    """
    Direct CalendarCreateEvent integration function with error handling
    
    Args:
        event_data: Dict with keys: summary, start, end, description?, location?
        api_resource: Optional pre-built API resource
    
    Returns:
        Dict with success status and event details or error message
    """
    try:
        if not CALENDAR_IMPORTS_AVAILABLE:
            return {"success": False, "error": "Google Calendar dependencies not available"}
        
        # Use provided API resource or build new one
        if api_resource is None:
            # Get OAuth tokens using existing system
            try:
                bearer_token = await OAuth2Verifier.get_token("gcalendar")
                refresh_token = await OAuth2Verifier.get_token("gcalendar", "refresh_token")
            except Exception as e:
                logging.error(f"Error getting OAuth tokens: {str(e)}")
                return {"success": False, "error": f"Authentication failed: {str(e)}"}

            if not bearer_token:
                return {"success": False, "error": "No valid authentication token available"}

            # Get OAuth app configuration
            gcalendar_app = OAuth2Verifier.get_instance().apps["gcalendar"]
            
            # Set up Google Calendar credentials
            token_info = {
                "client_id": gcalendar_app.client_id,
                "client_secret": gcalendar_app.client_secret,
                "refresh_token": refresh_token,
                "token_uri": gcalendar_app.token_url,
                "access_token": bearer_token,
                "expires_in": await OAuth2Verifier.get_token("gcalendar", "expires_in"),
                "scopes": gcalendar_app.scopes
            }
            
            # Create credentials and build the Google API service
            credentials = Credentials.from_authorized_user_info(token_info)
            api_resource = build_resource_service(credentials=credentials)
        
        # Create the CalendarCreateEvent tool
        create_event_tool = CalendarCreateEvent.from_api_resource(api_resource)
        
        # Validate required fields
        required_fields = ["summary", "start", "end"]
        for field in required_fields:
            if field not in event_data:
                return {"success": False, "error": f"Missing required field: {field}"}
        
        # Execute event creation
        logging.info(f"Creating calendar event: {event_data.get('summary', 'Untitled')}")
        result = await create_event_tool.ainvoke(event_data)
        
        return {
            "success": True, 
            "event_id": result.get("id", "unknown"),
            "event_details": result,
            "message": f"Successfully created event: {event_data.get('summary', 'Untitled')}"
        }
        
    except Exception as e:
        logging.error(f"Error creating calendar event: {str(e)}")
        return {
            "success": False, 
            "error": f"Failed to create calendar event: {str(e)}"
        }


@tool
async def direct_calendar_create_tool(summary: str, start_datetime: str, end_datetime: str, description: str = "", location: str = "", state: Optional[Any] = None) -> str:
    """
    Create a calendar event directly using CalendarCreateEvent tool
    
    Args:
        summary: Event title/summary
        start_datetime: Start time in ISO format (e.g., "2024-01-15T14:00:00")
        end_datetime: End time in ISO format (e.g., "2024-01-15T15:30:00") 
        description: Optional event description
        location: Optional event location
        state: Supervisor state for context
    
    Returns:
        Success message with event details or error message
    """
    try:
        from datetime import datetime
        
        # Parse datetime strings
        start_dt = datetime.fromisoformat(start_datetime.replace("Z", "+00:00"))
        end_dt = datetime.fromisoformat(end_datetime.replace("Z", "+00:00"))
        
        event_data = {
            "summary": summary,
            "start": start_dt,
            "end": end_dt
        }
        
        if description:
            event_data["description"] = description
        if location:
            event_data["location"] = location
            
        result = await create_direct_calendar_event(event_data)
        
        if result["success"]:
            return f"✅ Calendar event created successfully!\n\nEvent: {summary}\nTime: {start_datetime} - {end_datetime}\nEvent ID: {result['event_id']}"
        else:
            return f"❌ Failed to create calendar event: {result['error']}"
            
    except Exception as e:
        logging.error(f"Error in direct_calendar_create_tool: {e}")
        return f"❌ Error creating calendar event: {str(e)}"


            
async def get_calendar_tools():
    """Get Google Calendar tools for the agenda planner"""
    try:
        if not CALENDAR_IMPORTS_AVAILABLE:
            return []
            
        # Get OAuth tokens using existing system
        try:
            bearer_token = await OAuth2Verifier.get_token("gcalendar")
            refresh_token = await OAuth2Verifier.get_token("gcalendar", "refresh_token")
        except Exception as e:
            logging.error(f"Error getting OAuth tokens: {str(e)}")
            return []

        if not bearer_token:
            return []

        # Get OAuth app configuration
        gcalendar_app = OAuth2Verifier.get_instance().apps["gcalendar"]
        
        # Set up Google Calendar credentials for the toolkit
        token_info = {
            "client_id": gcalendar_app.client_id,
            "client_secret": gcalendar_app.client_secret,
            "refresh_token": refresh_token,
            "token_uri": gcalendar_app.token_url,
            "access_token": bearer_token,
            "expires_in": await OAuth2Verifier.get_token("gcalendar", "expires_in"),
            "scopes": gcalendar_app.scopes
        }
        
        # Create credentials and build the Google API service
        credentials = Credentials.from_authorized_user_info(token_info)
        api_resource = build_resource_service(credentials=credentials)
        
        # Initialize the Google Calendar Toolkit
        toolkit = CalendarToolkit(api_resource=api_resource)
        
        # Get all tools from the toolkit
        calendar_tools = toolkit.get_tools()
        
        # Replace CalendarCreateEvent with our custom confirmation version
        modified_tools = []
        original_create_tool = None
        
        for tool in calendar_tools:
            if tool.name == "CalendarCreateEvent":
                original_create_tool = tool
                # Create our custom tool with confirmation
                custom_create_tool = CalendarCreateEventWithConfirmation(original_tool=tool)
                modified_tools.append(custom_create_tool)
            else:
                modified_tools.append(tool)
        
        return modified_tools
        
    except Exception as e:
        logging.error(f"Error setting up calendar tools: {str(e)}")
        return []




async def create_supervisor_agenda_planner() -> SupervisorSupervisor:
    class TaskCreator:
        agenda_task: SupervisorTask_Create_agent = None
      
        

        async def create_tasks(self):
            # Create datechecker task first

           
            # Register the agenda task with React agent functionality using the new SupervisorTask_Create_agent
            calendar_tools = await get_calendar_tools()
            self.agenda_task = SupervisorManager.register_task(SupervisorTask_Create_agent(
                name="agenda_expert", 
                tools=calendar_tools, 
                prompt="""You are an agenda and calendar expert with integrated Google Calendar Toolkit capabilities. 

                            Your task uses a ReAct agent with the Google Calendar Toolkit that provides:
                            - CalendarCreateEvent: Create new calendar events (with automatic user confirmation preview)
                            - CalendarSearchEvents: Search for events
                            - CalendarUpdateEvent: Update existing events
                            - GetCalendarsInfo: Get calendar information
                            - CalendarMoveEvent: Move events between calendars
                            - CalendarDeleteEvent: Delete events
                            - GetCurrentDatetime: Get current date and time
                            - CalculatorTool: Perform calculations

                            IMPORTANT: When creating events with CalendarCreateEvent, the system will automatically show a preview 
                            to the user and request confirmation before actually creating the event. The user will see details 
                            like title, start/end times, location, description, and attendees, and must approve before the 
                            event is created.

                            Process user requests naturally and leverage the React agent's reasoning capabilities with the full Google Calendar Toolkit.
                            Always be helpful and accurate with calendar operations.""",
            ))

        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(SupervisorSupervisor(
                name="agenda_supervisor",
                prompt="""You are an agenda and calendar supervisor that actively manages calendars and events. Always delegate to the agenda_expert for any calendar operations.

               """
            )) \
            \
            .add_task(self.agenda_task, priority=1)\
            .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()

