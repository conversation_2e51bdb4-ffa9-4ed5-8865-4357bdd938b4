from imports import *

import asyncio
from datetime import datetime
from typing import Optional, Dict, Any

from userprofiles.ScheduledZairaTask import ScheduledZairaTask
from userprofiles.ZairaUser import Zaira<PERSON>ser
from endpoints.mybot_generic import MyBot_Generic
from managers.manager_users import ZairaUserManager

class IMAPIdleScheduledTask:
    """
    Manages IMAP IDLE scheduled tasks that check for new emails every 30 minutes.
    This class provides a singleton-like interface to manage the recurring IMAP IDLE task.
    """
    _instance: Optional['IMAPIdleScheduledTask'] = None
    _scheduled_task: Optional[ScheduledZairaTask] = None
    _is_running: bool = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    async def start_imap_idle_scheduler(cls, user: <PERSON><PERSON><PERSON><PERSON>ser, calling_bot: MyBot_Generic, original_message = None) -> ScheduledZairaTask:
        """
        Start the IMAP IDLE scheduler that triggers every 30 minutes.
        
        Args:
            user: The <PERSON>airaUser who initiated the scheduler
            calling_bot: The bot instance that will handle responses
            original_message: The original message that triggered this scheduler
            
        Returns:
            ScheduledZairaTask: The created scheduled task instance
        """
        instance = cls()
        
        if instance._is_running and instance._scheduled_task and instance._scheduled_task.is_active:
            LogFire.log("IMAP_SCHEDULER", "IMAP IDLE scheduler is already running")
            await calling_bot.send_reply("IMAP IDLE scheduler is already active", None, original_message, False)
            return instance._scheduled_task
        
        # Create the scheduled task with a 30-minute recurring interval
        schedule_prompt = "trigger IMAP IDLE every 30 minutes"
        
        try:
            instance._scheduled_task = ScheduledZairaTask(
                user=user,
                calling_bot=calling_bot,
                original_message=original_message,
                schedule_prompt=schedule_prompt
            )
            
            # Override the target prompt to call our IMAP IDLE activation
            instance._scheduled_task.target_prompt = "activate IMAP IDLE check"
            
            instance._is_running = True
            
            LogFire.log("IMAP_SCHEDULER", f"Started IMAP IDLE scheduler for user {user.user_id}")
            await calling_bot.send_reply("IMAP IDLE scheduler started - will check for new emails every 30 minutes", None, original_message, False)
            
            # Start the scheduled task in the background
            asyncio.create_task(instance._run_scheduler())
            
            return instance._scheduled_task
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to start IMAP IDLE scheduler: {str(e)}")
            await calling_bot.send_reply(f"Failed to start IMAP IDLE scheduler: {str(e)}", None, original_message, False)
            raise
    
    async def _run_scheduler(self):
        """Internal method to run the scheduled task"""
        if self._scheduled_task:
            try:
                await self._scheduled_task.run_task()
            except Exception as e:
                LogFire.log("ERROR", f"IMAP IDLE scheduler encountered an error: {str(e)}")
                self._is_running = False
    
    @classmethod
    async def stop_imap_idle_scheduler(cls, calling_bot: MyBot_Generic = None, original_message = None):
        """
        Stop the currently running IMAP IDLE scheduler.
        
        Args:
            calling_bot: Optional bot instance for sending response
            original_message: Optional original message context
        """
        instance = cls()
        
        if not instance._is_running or not instance._scheduled_task:
            if calling_bot:
                await calling_bot.send_reply("No IMAP IDLE scheduler is currently running", None, original_message, False)
            return
        
        try:
            instance._scheduled_task.cancel_schedule()
            instance._is_running = False
            
            LogFire.log("IMAP_SCHEDULER", "IMAP IDLE scheduler stopped")
            
            if calling_bot:
                await calling_bot.send_reply("IMAP IDLE scheduler stopped", None, original_message, False)
                
        except Exception as e:
            LogFire.log("ERROR", f"Failed to stop IMAP IDLE scheduler: {str(e)}")
            if calling_bot:
                await calling_bot.send_reply(f"Failed to stop IMAP IDLE scheduler: {str(e)}", None, original_message, False)
    
    @classmethod
    def get_scheduler_status(cls) -> Dict[str, Any]:
        """
        Get the current status of the IMAP IDLE scheduler.
        
        Returns:
            Dict containing scheduler status information
        """
        instance = cls()
        
        if not instance._scheduled_task:
            return {
                'is_running': False,
                'status': 'not_initialized',
                'message': 'No IMAP IDLE scheduler has been created'
            }
        
        schedule_info = instance._scheduled_task.get_schedule_info()
        schedule_info.update({
            'is_running': instance._is_running,
            'scheduler_status': 'active' if instance._is_running else 'inactive'
        })
        
        return schedule_info
    
    @classmethod
    async def restart_imap_idle_scheduler(cls, user: ZairaUser, calling_bot: MyBot_Generic, original_message = None) -> ScheduledZairaTask:
        """
        Restart the IMAP IDLE scheduler (stop current and start new).
        
        Args:
            user: The ZairaUser who initiated the restart
            calling_bot: The bot instance that will handle responses  
            original_message: The original message that triggered this restart
            
        Returns:
            ScheduledZairaTask: The newly created scheduled task instance
        """
        # Stop existing scheduler if running
        await cls.stop_imap_idle_scheduler(calling_bot, original_message)
        
        # Wait a moment for cleanup
        await asyncio.sleep(1)
        
        # Start new scheduler
        return await cls.start_imap_idle_scheduler(user, calling_bot, original_message)

class CustomIMAPIdleTask(ScheduledZairaTask):
    """
    Custom implementation of ScheduledZairaTask specifically for IMAP IDLE functionality.
    This class overrides the execution logic to call the imap_idle_activate module.
    """
    
    def __init__(self, user: ZairaUser, calling_bot: MyBot_Generic, original_message):
        # Initialize with the IMAP IDLE specific schedule
        schedule_prompt = "trigger IMAP IDLE every 30 minutes"
        super().__init__(user, calling_bot, original_message, schedule_prompt)
    
    async def run_scheduled_task(self):
        """Override the scheduled task execution to call IMAP IDLE activation"""
        while self.is_active:
            # Wait for the scheduled time
            if self.next_execution:
                now = datetime.now()
                if self.next_execution > now:
                    delay = (self.next_execution - now).total_seconds()
                    if delay > 0:
                        await asyncio.sleep(delay)
            else:
                await asyncio.sleep(self.delay_seconds)
            
            if not self.is_active:
                break
                
            # Execute IMAP IDLE activation
            try:
                LogFire.log("IMAP_IDLE", "Executing scheduled IMAP IDLE check")
                
                # Call the IMAP email check task through the supervisor system
                try:
                    from managers.manager_supervisors import SupervisorManager
                    imap_check_task = SupervisorManager.get_task("imap_email_check_task")
                    if imap_check_task:
                        await imap_check_task.call_task(user_guid=self.user.user_id)
                        LogFire.log("IMAP_IDLE", "IMAP email check completed successfully")
                    else:
                        # Fallback to existing IMAP task if the new task doesn't exist yet
                        LogFire.log("IMAP_IDLE", "imap_email_check_task not found, using fallback IMAP task")
                        imap_task = SupervisorManager.get_task("imap_retrieval_task")
                        if imap_task:
                            await imap_task.call_task(user_guid=self.user.user_id)
                        else:
                            LogFire.log("ERROR", "No IMAP task available for execution")
                except Exception as task_error:
                    LogFire.log("ERROR", f"Failed to call IMAP task: {str(task_error)}")
                    # Final fallback: try direct import
                    try:
                        from tasks.inputs.imap_idle_activate import SupervisorTask_IMAPIdleActivate
                        task_instance = SupervisorTask_IMAPIdleActivate(name="temp_imap_task", prompt_id="temp")
                        from managers.manager_supervisors import SupervisorTaskState
                        state = SupervisorTaskState(user_guid=self.user.user_id)
                        result = await task_instance.llm_call(state)
                        LogFire.log("IMAP_IDLE", f"Direct task execution result: {result}")
                    except Exception as direct_error:
                        LogFire.log("ERROR", f"Direct task execution also failed: {str(direct_error)}")
                        raise
                
            except Exception as e:
                LogFire.log("ERROR", f"IMAP IDLE scheduled execution failed: {str(e)}")
                await self.calling_bot.send_reply(f"IMAP IDLE check failed: {str(e)}", self, self.original_physical_message, False)
            
            # Handle recurring execution
            if self.schedule_type.value == "once":
                self.is_active = False
                break
            else:
                # Schedule next execution in 30 minutes
                from datetime import timedelta
                self.next_execution = datetime.now() + timedelta(seconds=self.delay_seconds)
                self._set_task_status({
                    'status': 'scheduled',
                    'message': f'Next IMAP IDLE check at {self.next_execution.strftime("%Y-%m-%d %H:%M:%S")}',
                    'next_execution': self.next_execution.isoformat()
                })

# Convenience functions for easy integration

async def start_imap_idle_monitoring(user_id: str = None, calling_bot: MyBot_Generic = None, original_message = None) -> ScheduledZairaTask:
    """
    Convenience function to start IMAP IDLE monitoring for a specific user or default user.
    
    Args:
        user_id: Optional user ID. If None, uses default system user
        calling_bot: Optional bot instance for responses
        original_message: Optional original message context
        
    Returns:
        ScheduledZairaTask: The created scheduled task
    """
    if user_id:
        user = await ZairaUserManager.find_user(user_id)
    else:
        # Create or get default system user for automated tasks
        user = await ZairaUserManager.get_or_create_system_user()
    
    if not calling_bot:
        # Create a minimal bot instance for system operations
        from endpoints.mybot_generic import MyBot_Generic
        calling_bot = MyBot_Generic()
        calling_bot.name = "system_imap_scheduler"
    
    return await IMAPIdleScheduledTask.start_imap_idle_scheduler(user, calling_bot, original_message)

async def stop_imap_idle_monitoring(calling_bot: MyBot_Generic = None, original_message = None):
    """
    Convenience function to stop IMAP IDLE monitoring.
    
    Args:
        calling_bot: Optional bot instance for responses
        original_message: Optional original message context
    """
    await IMAPIdleScheduledTask.stop_imap_idle_scheduler(calling_bot, original_message)

def get_imap_idle_status() -> Dict[str, Any]:
    """
    Convenience function to get IMAP IDLE scheduler status.
    
    Returns:
        Dict containing current scheduler status
    """
    return IMAPIdleScheduledTask.get_scheduler_status()