# tests/unit/test_qdrant_multimodal.py
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *
from managers.manager_qdrant import QDrantManager
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

class TestQDrantMultimodal:
    
    @pytest.fixture
    def manager(self):
        """Get QDrant manager instance"""
        return QDrantManager.get_instance()
    
    @pytest.fixture
    def sample_multimodal_data(self):
        """Sample multimodal data for testing"""
        return {
            "doc_id": str(uuid4()),
            "text_elements": [
                {"id": "text_1", "type": "NarrativeText", "text": "This is sample text."}
            ],
            "images": [
                {
                    "id": "img_1",
                    "type": "Image",
                    "text": "Image description",
                    "summary": "A chart showing sales data",
                    "asset_path": "/path/to/image.png",
                    "has_asset": True
                }
            ],
            "tables": [
                {
                    "id": "tbl_1",
                    "type": "Table", 
                    "text": "Table content",
                    "summary": "Sales data by region",
                    "markdown": "| Region | Sales |\n| --- | --- |\n| North | 100 |\n| South | 150 |",
                    "has_structure": True,
                    "key_info": {
                        "headers": ["Region", "Sales"],
                        "num_columns": 2,
                        "num_rows": 2
                    }
                }
            ],
            "figures": [],
            "captions": [],
            "all_elements": []
        }
    
    @pytest.fixture
    def sample_base_metadata(self):
        """Sample base metadata for testing"""
        return {
            "file_path": "/test/document.pdf",
            "file_name": "document.pdf",
            "file_type": "application/pdf",
            "file_size": 1024000
        }
    
    def test_enhance_text_with_multimodal_context(self, manager, sample_multimodal_data):
        """Test text enhancement with multimodal context"""
        text_chunk = "This is a sample text chunk."
        
        # Test with asyncio.run since this is an async method
        async def run_test():
            enhanced_text = await manager._enhance_text_with_multimodal_context(
                text_chunk, sample_multimodal_data, 0
            )
            
            # Should contain original text
            assert text_chunk in enhanced_text
            
            # Should contain image context
            assert "Document contains images:" in enhanced_text
            assert "A chart showing sales data" in enhanced_text
            
            # Should contain table context  
            assert "Document contains tables:" in enhanced_text
            assert "Sales data by region" in enhanced_text
            
            return enhanced_text
        
        # Run the async test
        import asyncio
        enhanced_text = asyncio.run(run_test())
        assert enhanced_text is not None
    
    def test_enhance_text_with_no_multimodal(self, manager):
        """Test text enhancement with no multimodal content"""
        text_chunk = "This is a sample text chunk."
        multimodal_data = {"images": [], "tables": []}
        
        async def run_test():
            enhanced_text = await manager._enhance_text_with_multimodal_context(
                text_chunk, multimodal_data, 0
            )
            
            # Should just return original text
            assert enhanced_text == text_chunk
            return enhanced_text
        
        import asyncio
        enhanced_text = asyncio.run(run_test())
        assert enhanced_text == text_chunk
    
    @patch('managers.manager_qdrant.Globals')
    @patch('managers.manager_qdrant.QDrantManager.upsert')
    async def test_upsert_multimodal_structure(self, mock_upsert, mock_globals, manager, 
                                              sample_multimodal_data, sample_base_metadata):
        """Test multimodal upsert creates correct node structure"""
        # Mock the upsert method to return a node
        mock_node = MagicMock()
        mock_upsert.return_value = mock_node
        
        # Mock the index
        mock_index = MagicMock()
        mock_globals.get_index.return_value = mock_index
        
        doc_id = "test_doc_id"
        text_chunks = ["Chunk 1", "Chunk 2"]
        
        # Call the method
        nodes_created = await manager.upsert_multimodal(
            doc_id=doc_id,
            text_chunks=text_chunks,
            multimodal_data=sample_multimodal_data,
            base_metadata=sample_base_metadata
        )
        
        # Verify calls were made
        # Should create nodes for: 2 text chunks + 1 image + 1 table = 4 nodes
        expected_calls = 4
        assert mock_upsert.call_count == expected_calls
        
        # Verify index.insert_nodes was called
        mock_index.insert_nodes.assert_called_once()
        
        # Verify return value
        assert nodes_created == expected_calls
    
    def test_upsert_multimodal_metadata_structure(self, manager, sample_multimodal_data, sample_base_metadata):
        """Test that multimodal metadata is properly structured"""
        doc_id = "test_doc_id"
        
        # Extract the metadata that would be created
        multimodal_metadata = {
            "has_images": len(sample_multimodal_data.get("images", [])) > 0,
            "has_tables": len(sample_multimodal_data.get("tables", [])) > 0,
            "image_count": len(sample_multimodal_data.get("images", [])),
            "table_count": len(sample_multimodal_data.get("tables", [])),
            "figure_count": len(sample_multimodal_data.get("figures", [])),
            "caption_count": len(sample_multimodal_data.get("captions", [])),
            "multimodal_doc_id": doc_id,
        }
        
        # Verify metadata structure
        assert multimodal_metadata["has_images"] is True
        assert multimodal_metadata["has_tables"] is True
        assert multimodal_metadata["image_count"] == 1
        assert multimodal_metadata["table_count"] == 1
        assert multimodal_metadata["figure_count"] == 0
        assert multimodal_metadata["caption_count"] == 0
        assert multimodal_metadata["multimodal_doc_id"] == doc_id
    
    @patch('managers.manager_qdrant.Globals')
    async def test_search_multimodal_filters(self, mock_globals, manager):
        """Test multimodal search filter construction"""
        # Mock the index and query engine
        mock_query_engine = MagicMock()
        mock_query_engine.query.return_value = "Search results"
        
        mock_index = MagicMock()
        mock_index.as_query_engine.return_value = mock_query_engine
        mock_globals.get_index.return_value = mock_index
        
        # Test search with image filter
        result = await manager.search_multimodal(
            query="test query",
            has_images=True,
            has_tables=None,
            content_type="image_summary",
            limit=5
        )
        
        # Verify query engine was created with filters
        mock_index.as_query_engine.assert_called_once()
        call_args = mock_index.as_query_engine.call_args
        
        # Check that filters parameter was passed
        assert 'filters' in call_args.kwargs
        
        # Verify query was executed
        mock_query_engine.query.assert_called_once_with("test query")
    
    @patch('managers.manager_qdrant.Globals')
    async def test_get_document_assets_structure(self, mock_globals, manager):
        """Test document assets retrieval structure"""
        # Mock the retriever and nodes
        mock_node1 = MagicMock()
        mock_node1.metadata = {
            "content_type": "image_summary",
            "element_id": "img_1",
            "asset_path": "/path/to/image.png",
            "has_asset": True
        }
        mock_node1.text = "Image description"
        mock_node1.id_ = "doc_image_1"
        
        mock_node2 = MagicMock()
        mock_node2.metadata = {
            "content_type": "table_summary",
            "element_id": "tbl_1",
            "num_columns": 3,
            "num_rows": 5,
            "has_structure": True
        }
        mock_node2.text = "Table summary"
        mock_node2.id_ = "doc_table_1"
        
        mock_node3 = MagicMock()
        mock_node3.metadata = {
            "chunk_index": 0
        }
        mock_node3.text = "Regular text chunk"
        mock_node3.id_ = "doc_chunk_1"
        
        mock_retriever = MagicMock()
        mock_retriever.retrieve.return_value = [mock_node1, mock_node2, mock_node3]
        
        mock_index = MagicMock()
        mock_index.as_retriever.return_value = mock_retriever
        mock_globals.get_index.return_value = mock_index
        
        # Test assets retrieval
        assets = await manager.get_document_assets("test_doc_id")
        
        # Verify structure
        assert "images" in assets
        assert "tables" in assets
        assert "chunks" in assets
        
        # Verify image asset
        assert len(assets["images"]) == 1
        assert assets["images"][0]["id"] == "img_1"
        assert assets["images"][0]["summary"] == "Image description"
        assert assets["images"][0]["asset_path"] == "/path/to/image.png"
        assert assets["images"][0]["has_asset"] is True
        
        # Verify table asset
        assert len(assets["tables"]) == 1
        assert assets["tables"][0]["id"] == "tbl_1"
        assert assets["tables"][0]["summary"] == "Table summary"
        assert assets["tables"][0]["num_columns"] == 3
        assert assets["tables"][0]["num_rows"] == 5
        assert assets["tables"][0]["has_structure"] is True
        
        # Verify text chunk
        assert len(assets["chunks"]) == 1
        assert assets["chunks"][0]["id"] == "doc_chunk_1"
        assert assets["chunks"][0]["text"] == "Regular text chunk"
        assert assets["chunks"][0]["chunk_index"] == 0

class TestQDrantMultimodalIntegration:
    """Integration tests for QDrant multimodal functionality"""
    
    @pytest.fixture
    def manager(self):
        return QDrantManager.get_instance()
    
    # Note: These integration tests would require actual QDrant connection
    # They are designed to test the complete pipeline when QDrant is available
    
    async def test_full_multimodal_pipeline_mock(self, manager):
        """Test complete multimodal pipeline with mocked dependencies"""
        # This test demonstrates the expected flow without requiring actual QDrant
        
        sample_data = {
            "doc_id": str(uuid4()),
            "images": [{"summary": "Test image", "asset_path": "/test.png"}],
            "tables": [{"summary": "Test table", "markdown": "| A | B |\n|---|---|\n| 1 | 2 |"}],
            "text_elements": [],
            "figures": [],
            "captions": [],
            "all_elements": []
        }
        
        text_chunks = ["Sample chunk 1", "Sample chunk 2"]
        base_metadata = {"file_name": "test.pdf"}
        
        # With mocked Globals and upsert, this would test the complete flow
        with patch('managers.manager_qdrant.Globals') as mock_globals, \
             patch.object(manager, 'upsert') as mock_upsert:
            
            mock_node = MagicMock()
            mock_upsert.return_value = mock_node
            
            mock_index = MagicMock()
            mock_globals.get_index.return_value = mock_index
            
            # Execute the pipeline
            result = await manager.upsert_multimodal(
                doc_id=sample_data["doc_id"],
                text_chunks=text_chunks,
                multimodal_data=sample_data,
                base_metadata=base_metadata
            )
            
            # Verify the pipeline executed
            assert result > 0  # Should create some nodes
            mock_index.insert_nodes.assert_called_once()

if __name__ == "__main__":
    pytest.main([__file__, "-v"])