"""
Integration tests for database coordination (PostgreSQL + Qdrant)
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.integration
@pytest.mark.asyncio
class TestDatabaseCoordination:
    """Test coordination between PostgreSQL and Qdrant"""
    
    async def test_scheduled_task_with_vector_storage(self, mock_database_connections, sample_scheduled_task_data):
        """Test scheduled task creation with vector document storage"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        from managers.manager_qdrant import QDrantManager
        from userprofiles.ScheduledZairaTask import ScheduledZairaTask
        
        # Mock managers
        task_manager = ScheduledTaskPersistenceManager.get_instance()
        vector_manager = QDrantManager.get_instance()
        
        # Mock successful operations
        mock_database_connections['postgresql'].execute.return_value = True
        mock_database_connections['qdrant'].upsert.return_value = True
        
        # Mock the components needed for ScheduledZairaTask
        from userprofiles.ZairaUser import Zaira<PERSON>ser
        from endpoints.mybot_generic import MyBot_Generic
        
        # Create mock user and bot
        mock_user = MagicMock(spec=<PERSON>airaUser)
        mock_user.user_guid = sample_scheduled_task_data['user_guid']
        mock_bot = MagicMock(spec=MyBot_Generic)
        mock_message = MagicMock()
        
        # Create scheduled task with proper constructor
        task = ScheduledZairaTask(
            user=mock_user,
            calling_bot=mock_bot,
            original_message=mock_message,
            schedule_prompt=sample_scheduled_task_data['schedule_prompt']
        )
        
        # Mock the save operations
        with patch.object(task_manager, 'save_task') as mock_save_task, \
             patch.object(vector_manager, 'upsert') as mock_upsert:
            
            mock_save_task.return_value = True
            mock_upsert.return_value = True
            
            # Test coordinated save operation
            postgres_result = await task_manager.save_task(task)
            vector_result = await vector_manager.upsert(
                f"task_{task.task_id}",
                task.schedule_prompt,
                {"task_type": "scheduled", "user_guid": task.user.user_guid}
            )
            
            assert postgres_result is True
            assert vector_result is True
            
            # Verify both operations were called
            mock_save_task.assert_called_once()
            mock_upsert.assert_called_once()
    
    async def test_user_data_synchronization(self, mock_database_connections, sample_user_data):
        """Test user data synchronization between databases"""
        from managers.manager_users import ZairaUserManager
        from managers.manager_qdrant import QDrantManager
        
        user_manager = ZairaUserManager.get_instance()
        vector_manager = QDrantManager.get_instance()
        
        # Mock database operations
        mock_database_connections['postgresql'].fetch.return_value = [sample_user_data]
        mock_database_connections['qdrant'].upsert.return_value = True
        
        # Mock the create_user method
        with patch.object(user_manager, 'create_user', new_callable=AsyncMock, create=True) as mock_create_user, \
             patch.object(vector_manager, 'upsert') as mock_upsert:
            
            mock_create_user.return_value = sample_user_data
            mock_upsert.return_value = True
            
            # Test user creation with vector profile
            user = await user_manager.create_user(
                sample_user_data['user_guid'],
                sample_user_data['platform']
            )
        
            # Sync user profile to vector database
            vector_result = await vector_manager.upsert(
                f"user_{user['user_guid']}",
                f"User profile for {user['platform']} user",
                {"type": "user_profile", "platform": user['platform']}
            )
            
            assert user is not None
            assert vector_result is True
    
    async def test_cross_database_recovery(self, mock_database_connections):
        """Test recovery mechanisms across databases"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        from managers.manager_qdrant import QDrantManager
        
        task_manager = ScheduledTaskPersistenceManager.get_instance()
        vector_manager = QDrantManager.get_instance()
        
        # Simulate PostgreSQL failure, Qdrant success
        mock_database_connections['postgresql'].execute.side_effect = Exception("PostgreSQL connection lost")
        mock_database_connections['qdrant'].search_similar.return_value = [
            {"id": "task_123", "payload": {"schedule_prompt": "check email daily"}}
        ]
        
        # Mock the operations for failure testing
        with patch.object(task_manager, 'save_task') as mock_save_task, \
             patch.object(vector_manager, 'search_similar') as mock_search:
            
            mock_save_task.side_effect = Exception("PostgreSQL connection lost")
            mock_search.return_value = [
                {"id": "task_123", "payload": {"schedule_prompt": "check email daily"}}
            ]
            
            # Test recovery from vector database
            try:
                await task_manager.save_task({"task_id": "test_123"})
                pytest.fail("Expected exception not raised")
            except Exception as e:
                assert "PostgreSQL connection lost" in str(e)
                
                # Attempt recovery from Qdrant
                vector_tasks = await vector_manager.search_similar("scheduled tasks", limit=100)
                assert len(vector_tasks) == 1

@pytest.mark.integration
@pytest.mark.asyncio
class TestRAGDocumentPipeline:
    """Test complete RAG document processing pipeline"""
    
    async def test_document_ingestion_to_search(self, mock_database_connections, mock_rag_components):
        """Test complete document ingestion to search pipeline"""
        from managers.manager_retrieval import RetrievalManager
        from managers.manager_qdrant import QDrantManager
        
        retrieval_manager = RetrievalManager.get_instance()
        vector_manager = QDrantManager.get_instance()
        
        # Mock document processing pipeline
        with patch.object(retrieval_manager, 'process_document') as mock_process, \
             patch.object(retrieval_manager, 'chunk_document') as mock_chunk, \
             patch.object(retrieval_manager, 'generate_embeddings') as mock_embed:
            
            # Setup mocks
            mock_process.return_value = "Processed document content"
            mock_chunk.return_value = ["Chunk 1", "Chunk 2", "Chunk 3"]
            mock_embed.return_value = [0.1, 0.2, 0.3, 0.4]
            mock_database_connections['qdrant'].upsert.return_value = True
            
            # Test complete pipeline
            document_content = "This is a test document with important information."
            
            # Step 1: Process document
            processed_content = await retrieval_manager.process_document(document_content)
            assert processed_content == "Processed document content"
            
            # Step 2: Chunk document
            chunks = await retrieval_manager.chunk_document(processed_content)
            assert len(chunks) == 3
            
            # Step 3: Generate embeddings and store
            with patch.object(vector_manager, 'upsert') as mock_upsert:
                mock_upsert.return_value = True
                
                for i, chunk in enumerate(chunks):
                    embeddings = await retrieval_manager.generate_embeddings(chunk)
                    result = await vector_manager.upsert(f"doc_chunk_{i}", chunk, {
                        "chunk_index": i,
                        "document_id": "test_doc_123"
                    })
                    assert result is True
            
            # Verify all steps completed
            mock_process.assert_called_once()
            mock_chunk.assert_called_once()
            assert mock_embed.call_count == 3
    
    async def test_search_and_retrieval_pipeline(self, mock_database_connections, mock_rag_components):
        """Test search and retrieval pipeline"""
        from managers.manager_retrieval import RetrievalManager
        from managers.manager_qdrant import QDrantManager
        
        retrieval_manager = RetrievalManager.get_instance()
        vector_manager = QDrantManager.get_instance()
        
        # Mock search results
        mock_search_results = [
            {"id": "doc_1", "score": 0.95, "payload": {"text": "Relevant document content"}},
            {"id": "doc_2", "score": 0.85, "payload": {"text": "Another relevant document"}}
        ]
        mock_database_connections['qdrant'].search_similar.return_value = mock_search_results
        
        # Mock embedding generation for query
        with patch.object(retrieval_manager, 'generate_embeddings') as mock_embed:
            mock_embed.return_value = [0.1, 0.2, 0.3, 0.4]
            
            # Test search pipeline
            query = "Find information about scheduled tasks"
            
            # Step 1: Generate query embedding
            query_embedding = await retrieval_manager.generate_embeddings(query)
            assert len(query_embedding) == 4
            
            # Step 2: Search similar documents
            with patch.object(vector_manager, 'search_similar') as mock_search:
                mock_search.return_value = mock_search_results
                
                search_results = await vector_manager.search_similar(query, limit=5)
                assert len(search_results) == 2
                assert search_results[0]['score'] == 0.95
            
            # Step 3: Retrieve and rank results
            retrieved_docs = await retrieval_manager.retrieve_similar(query, top_k=5)
            assert len(retrieved_docs) == 2

@pytest.mark.integration
@pytest.mark.asyncio
class TestSystemRecovery:
    """Test system recovery scenarios"""
    
    async def test_system_restart_recovery(self, mock_database_connections):
        """Test system recovery after restart"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        
        task_manager = ScheduledTaskPersistenceManager.get_instance()
        
        # Mock existing tasks in database
        mock_tasks = [
            {"task_id": "task_1", "is_active": True, "schedule_prompt": "check email hourly"},
            {"task_id": "task_2", "is_active": True, "schedule_prompt": "backup daily"},
            {"task_id": "task_3", "is_active": False, "schedule_prompt": "old task"}
        ]
        mock_database_connections['postgresql'].fetch.return_value = mock_tasks
        
        # Mock the recovery method since it may not exist
        with patch.object(task_manager, 'get_active_tasks') as mock_get_active:
            mock_get_active.return_value = [task for task in mock_tasks if task['is_active']]
            
            # Test recovery process
            recovered_tasks = await task_manager.get_active_tasks("system")
            
            # Should only recover active tasks
            assert len(recovered_tasks) == 2
            assert all(task['is_active'] for task in recovered_tasks)