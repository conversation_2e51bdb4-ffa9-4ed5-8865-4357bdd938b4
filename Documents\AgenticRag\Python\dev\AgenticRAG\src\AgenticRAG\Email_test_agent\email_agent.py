from imports import *

import asyncio
from typing import (
    Callable,
    Sequence,
    Union,
    Any,
    Dict,
    List,
    Set,
)
from langchain_community.tools.tavily_search import TavilySearchResults
from langchain_core.tools import tool
from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import StrOutputParser
from langchain_openai import ChatOpenAI
from llama_index.core.vector_stores import MetadataFilters
from llama_index.core.query_engine import BaseQueryEngine
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage
from langgraph.types import Command
from langchain_core.tools import BaseTool
from llama_index.core.postprocessor import LLMRerank

from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorSupervisor_ChainOfThought, SupervisorTask_SingleAgent, SupervisorTaskState



async def expand_query(user_input: str) -> list[str]:
    """Generate multiple perspectives of the user query"""
    print(f"\n[DEBUG] Expanding query: '{user_input}'")

    template = """You are an AI language model assistant. Your task is to generate three
    different versions of the given user question to retrieve relevant documents.
    By generating multiple perspectives on the user question, your goal is to help
    overcome limitations of similarity search. Provide these alternative questions
    separated by newlines.

    Make sure the alternative query is:
    1. Rephrases the question using different synonyms
    


    Original question: {user_input}"""

    prompt_perspectives = ChatPromptTemplate.from_template(template)

    generate_queries = (
        prompt_perspectives
        | ChatOpenAI(temperature=0)
        | StrOutputParser()
        | (lambda x: x.split("\n"))
    )

    try:
        print("[DEBUG] Generating query variations using LLM...")
        expanded_queries = await generate_queries.ainvoke({"user_input": user_input})

        # Filter out potential empty strings or numbering (e.g., "1. Query") more robustly
        cleaned_queries = [
            q.strip() for q in expanded_queries if q and q.strip() and not q.strip().isnumeric()
        ]
        # Optional: Remove potential numbering like "1. ", "2. "
        cleaned_queries = [q.split(". ", 1)[-1] if q[0].isdigit() and q[1] == '.' else q for q in cleaned_queries]

        # Print each expanded query for debugging
        print("[DEBUG] Generated the following query variations:")
        for i, query in enumerate(cleaned_queries):
            print(f"[DEBUG]   {i+1}. {query}")

        return cleaned_queries
    except Exception as e:
        print(f"[DEBUG] Error during query expansion: {e}")
        etc.helper_functions.exception_triggered(e)
        return [] # Return empty list on failure
    


async def rag_search_tool(query: str, state: SupervisorTaskState = None):
    """Function to do RAG search using the provided index"""
    try:
        print(f"\n[DEBUG] ===== RAG SEARCH TOOL STARTED =====")
        print(f"[DEBUG] Original query: '{query}'")

        # Pass None as query_engine since it's not used in retrieve_data
        query_engine = None

        # Expand the query - use original input if available
        query_to_expand = state.original_input if state and hasattr(state, 'original_input') and state.original_input else query
        expanded_queries = await expand_query(query_to_expand)
        print(f"[DEBUG] Successfully generated {len(expanded_queries)} query variations")

        # List of all query texts to use for retrieval
        all_retrieval_query_texts = [query] + expanded_queries
        print(f"[DEBUG] Total queries to process: {len(all_retrieval_query_texts)}")

        # Gather results for all queries
        print(f"[DEBUG] Starting retrieval for all query variations...")
        tasks = []
        for query_text in all_retrieval_query_texts:
            tasks.append(retrieve_data(query_engine, query_text))

        print(f"[DEBUG] Awaiting all retrieval tasks to complete...")
        all_results = await asyncio.gather(*tasks, return_exceptions=True)
        print(f"[DEBUG] All retrieval tasks completed")

        # Variables to store aggregated metrics and all source nodes
        all_metrics = []
        all_source_nodes = []
        best_metrics = None
        successful_queries = 0

        # Process results and collect all source nodes
        print(f"[DEBUG] Processing retrieval results and collecting all source nodes...")
        for i, result in enumerate(all_results):
            query_used = all_retrieval_query_texts[i]
            if isinstance(result, Exception):
                print(f"[DEBUG] Error retrieving data for query '{query_used}': {result}")
            elif result:
                successful_queries += 1

                # Extract source nodes and metrics from the result
                if isinstance(result, dict):
                    if 'source_nodes' in result and result['source_nodes']:
                        # Add source nodes to our collection
                        all_source_nodes.extend(result['source_nodes'])
                        print(f"[DEBUG] Added {len(result['source_nodes'])} nodes from query '{query_used}'")

                    # Collect metrics if available
                    if 'metrics' in result and result['metrics'] is not None:
                        all_metrics.append(result['metrics'])

                        # Use the metrics from the original query as the best metrics
                        if query_used == query and best_metrics is None:
                            best_metrics = result['metrics']

        print(f"[DEBUG] Successfully retrieved data for {successful_queries}/{len(all_retrieval_query_texts)} queries")
        print(f"[DEBUG] Collected a total of {len(all_source_nodes)} source nodes from all queries")

        # If we have no source nodes, return empty result
        if not all_source_nodes:
            print("[DEBUG] No relevant data found for your query.")
            return "No relevant information found for your query."

        # Perform LLM reranking on all collected nodes
        print(f"[DEBUG] Performing LLM reranking on all {len(all_source_nodes)} collected nodes...")

        # Create an LLM reranker
        llm_reranker = LLMRerank(top_n=7)  # Get top 7 nodes

        # Rerank all nodes
        reranked_nodes = llm_reranker.postprocess_nodes(all_source_nodes, query_str=query)
        print(f"[DEBUG] After LLM reranking, selected top {len(reranked_nodes)} nodes")

        # Extract text from reranked nodes and combine
        node_texts = []
        for node in reranked_nodes:
            if hasattr(node, 'text') and node.text:
                node_texts.append(node.text)
            elif hasattr(node, 'get_text') and callable(node.get_text):
                node_texts.append(node.get_text())
            elif hasattr(node, 'node') and hasattr(node.node, 'text'):
                node_texts.append(node.node.text)

        # Create a tree summarizer to combine the node texts
        from llama_index.core.response_synthesizers import TreeSummarize
        summarizer = TreeSummarize(verbose=True)

        # Summarize the reranked nodes
        print(f"[DEBUG] Summarizing the top {len(reranked_nodes)} nodes...")
        summary_response = await summarizer.aget_response(query, node_texts)
        print(f"[DEBUG] Successfully generated summary from top nodes")

     

        print(f"[DEBUG] ===== RAG SEARCH TOOL COMPLETED =====")

        # Return the final response
        return summary_response
    except Exception as e:
        print(f"[DEBUG] Critical error in rag_search_tool: {e}")
        return f"An error occurred: {e}"
    
    