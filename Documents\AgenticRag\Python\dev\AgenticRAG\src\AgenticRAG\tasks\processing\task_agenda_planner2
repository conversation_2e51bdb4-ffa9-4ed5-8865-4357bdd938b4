# from imports import *

# from langchain_core.tools import tool
# import logging
# import smtplib
# import base64

# from typing import Optional, Dict, Any
# from datetime import datetime, timedelta
# import asyncio
# import json

# from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState, SupervisorTask_Base
# from endpoints.oauth._verifier_ import OAuth2Verifier
# from managers.manager_users import ZairaUserManager
# from userprofiles.LongRunningZairaTask import LongRunningZairaTask

# from googleapiclient.discovery import build
# from google.oauth2.credentials import Credentials





# @tool
# async def calendar_tool(action: str, state: SupervisorTaskState, **kwargs):
#     """Performs calendar operations based on the specified action."""
#     task = SupervisorManager.get_task("agenda_task")
#     user_guid = state.user_guid
#     user = await ZairaUserManager.find_user(user_guid)
#     if action == "list_calendars":
#         return list_calendar_list(kwargs.get('max_capacity', 50))
#     elif action == "list_events":
#         return list_calandar_events(kwargs.get('calendar_id'), kwargs.get('max_capacity', 20))
#     elif action == "create_calendar":
#         return create_calendar_list(kwargs.get('summary'))
#     elif action == "create_event":
#         return insert_calender_event(kwargs.get('calendar_id'), kwargs.get('event_details'))

# def get_calendar_service():
#     # Get the OAuth tokens from the verifier
#     tokens = OAuth2Verifier.get_full_token("gcalendar")
#     if not tokens:
#         raise Exception("No calendar OAuth tokens found")
    
#     # Create Google credentials from the tokens
#     credentials = Credentials(
#         token=tokens["access_token"],
#         refresh_token=tokens["refresh_token"],
#         token_uri="https://oauth2.googleapis.com/token",
#         client_id=OAuth2Verifier.get_instance().apps["gcalendar"].client_id,
#         client_secret=OAuth2Verifier.get_instance().apps["gcalendar"].client_secret,
#         scopes=OAuth2Verifier.get_instance().apps["gcalendar"].scopes
#     )

#     # Build and return the calendar service
#     calendar_service = build('calendar', 'v3', credentials=credentials)
#     return calendar_service

# def create_calendar_list(calendar_name):
#     """
#     Creates a new calendar list.

#     Parameters:
#     - calendar_name (str): The name of the new calendar list.

#     Returns:
#     - dict: A dictionary containing the ID of the new calendar list.
#     """
#     # Get calendar service
#     calendar_service = get_calendar_service()

#     calendar_list = {
#         'summary': calendar_name
#     }

#     created_calendar_list = calendar_service.calendarList().insert(body=calendar_list).execute()
#     return created_calendar_list
   
# def list_calendar_list(max_capacity=200):
#     """
#     Lists calendar lists until the total number of items reaches max_capacity.

#     Parameters:
#     - max_capacity (int or str, optional): The maximum number of calendar lists to retrieve. Defaults to 200.
#       If a string is provided, it will be converted to an integer.

#     Returns:
#     - list: A list of dictionaries containing cleaned calendar list information with 'id', 'name', and 'description'.
#     """
#     if isinstance(max_capacity, str):
#         max_capacity = int(max_capacity)

#     all_calendars = []
#     all_calendars_cleaned = []
#     next_page_token = None
#     capacity_tracker = 0

#     # Get calendar service
#     calendar_service = get_calendar_service()

#     while True:
#         calendar_list = calendar_service.calendarList().list(
#             maxResults=min(200, max_capacity - capacity_tracker),
#             pageToken=next_page_token
#         ).execute()
#         calendars = calendar_list.get('items', [])
#         all_calendars.extend(calendars)
#         capacity_tracker += len(calendars)
#         if capacity_tracker >= max_capacity:
#             break
#         next_page_token = calendar_list.get('nextPageToken')
#         if not next_page_token:
#             break

#     for calendar in all_calendars:
#         all_calendars_cleaned.append({
#             'id': calendar['id'],
#             'name': calendar['summary'],
#             'description': calendar.get('description', '')
#         })

#     return all_calendars_cleaned

# def list_calandar_events(calendar_id, max_capacity=20):
#     """
#     Lists events from a specific calendar until the total number of items reaches max_capacity.

#     Parameters:
#     - calendar_id (str): The ID of the calendar to list events from.
#     - max_capacity (int or str, optional): The maximum number of events to retrieve. Defaults to 20.
#       If a string is provided, it will be converted to an integer.

#     Returns:
#     - lis: a list of events from a the specified calendar.
#     """

#     if isinstance(max_capacity, str):
#         max_capacity = int(max_capacity)

#     all_events = []
#     next_page_token = None
#     capacity_tracker = 0

#     # Get calendar service
#     calendar_service = get_calendar_service()

#     while True:
#         events = calendar_service.events().list(
#             calendarId=calendar_id,
#             maxResults=min(250, max_capacity - capacity_tracker),
#             pageToken=next_page_token
#         ).execute()
#         events_list = events.get('items', [])
#         all_events.extend(events)
#         capacity_tracker += len(events)
#         if capacity_tracker >= max_capacity:
#             break
#         next_page_token = events.get('nextPageToken')
#         if not next_page_token:
#             break

#     return all_events

# def insert_calender_event(calendar_id, **kwargs):
#     """
#     Inserts an event into the specified calendar.

#     Parameters:
#     - calendar_id: the ID of the calendar where the event will be inserted.
#     - **kwargs: Additonal keyword arguments representing the event details.
#     Returns:
#     - the created event.
#     """
#     # Get calendar service
#     calendar_service = get_calendar_service()

#     request_body = json.loads(kwargs['kwargs'])
#     event = calendar_service.events().insert(calendarId=calendar_id, body=request_body).execute()
#     return event



# async def create_supervisor_agenda_planner() -> SupervisorSupervisor:
#     class TaskCreator:
#         agenda_task: SupervisorTask_SingleAgent = None
        

#         async def create_tasks(self):
#             # Create datechecker task first
           
#             # Register the agenda task with updated prompt to handle the new datechecker response format
#             self.agenda_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(
#                 name="agenda_expert", 
#                 tools=[calendar_tool], 
#                 prompt="""Calendar Operations Guide:

# 1. List Available Calendars
#    Use the calendar_tool with 'list_calendars' action to retrieve available calendars:
#    - Example: calendar_tool(action='list_calendars', state=state, max_capacity=50)
#    - Default max_capacity is 50 calendars unless specified otherwise

# 2. List Calendar Events
#    Use the calendar_tool with 'list_events' action to retrieve events from a calendar:
#    - For primary calendar: calendar_tool(action='list_events', state=state, calendar_id='primary', max_capacity=20)
#    - For specific calendar: First get the calendar ID from list_calendars, then use:
#      calendar_tool(action='list_events', state=state, calendar_id='specific_calendar_id', max_capacity=20)

# 3. Create New Calendar
#    Use the calendar_tool with 'create_calendar' action to create a new calendar:
#    - Example: calendar_tool(action='create_calendar', state=state, summary='My New Calendar')
#    - The calendar will be created with Europe/Amsterdam timezone by default

# 4. Create Calendar Event
#    Use the calendar_tool with 'create_event' action to create a new event:
#    Example event details:
#    {
#        'summary': 'Meeting with Team',
#        'location': 'Conference Room 1',
#        'description': 'Weekly project status update',
#        'start': {
#            'dateTime': '2025-06-14T10:00:00',
#            'timeZone': 'Europe/Amsterdam'
#        },
#        'end': {
#            'dateTime': '2025-06-14T11:00:00',
#            'timeZone': 'Europe/Amsterdam'
#        },
#        'attendees': [
#            {'email': '<EMAIL>'}
#        ]
#    }
   
#    Usage: calendar_tool(action='create_event', state=state, calendar_id='primary', event_details=event_details)

# Notes:
# - All dates are managed in Europe/Amsterdam timezone
# - The calendar_tool requires a valid state parameter containing user_guid
# - Always handle potential errors returned by the calendar operations""",
#             ))

#         async def create_supervisor(self) -> SupervisorSupervisor:
#             return SupervisorManager.register_supervisor(SupervisorSupervisor(
#                 name="agenda_supervisor",
#                 prompt="""You are an agenda and calendar supervisor that actively manages calendars and events. Always travel tot the agenda_expert for any calendar operations.

#                 For ANY calendar-related request:
#                 1. ALWAYS start by listing available calendars using calendar_tool with 'list_calendars' action
#                 2. Based on the user's request:
#                    - For viewing events: Use 'list_events' action to show relevant events
#                    - For creating events: Use 'create_event' action to add new events
#                    - For new calendars: Use 'create_calendar' action to create them
                
#                 IMPORTANT:
#                 - NEVER just respond with text - ALWAYS use the calendar_tool for any calendar operations
#                 - For each request, use at least one calendar_tool action
#                 - After performing actions, provide a clear summary of what was done
                
#                 Example workflow:
#                 1. User asks about their events
#                 2. You list calendars with calendar_tool
#                 3. You list events with calendar_tool
#                 4. You summarize the findings

#                 Remember: Your responses should ALWAYS include actual calendar operations using the tool, not just descriptions."""
#             )) \
#             \
#             .add_task(self.agenda_task, priority=2)\
#             .compile()

#     creator = TaskCreator()
#     await creator.create_tasks()
#     return await creator.create_supervisor()

