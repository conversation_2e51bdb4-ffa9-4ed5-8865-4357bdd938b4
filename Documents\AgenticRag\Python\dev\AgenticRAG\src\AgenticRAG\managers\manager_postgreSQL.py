from imports import *
from asyncpg import connect, create_pool, CannotConnectNowError
import asyncio
import atexit
import signal
from socket import gaierror

class PostgreSQLManager:
    _instance = None
    _initialized = False
    user="userzairaask"
    password="wordzairap4ss"
    host=("postgres" if Globals.is_docker() else "localhost")
    port=5432

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._connections = {}
            cls._instance._pools = {}
            cls._instance._connection_modes = {}  # dbname -> True if pool, False otherwise
        return cls._instance

    @classmethod
    async def setup(cls):
        instance = cls.__new__(cls)
        if instance._initialized:
            return
        instance._initialized = True
        cls._register_exit_hooks()

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    def _register_exit_hooks(cls):
        """
        Registers cleanup logic for interpreter exit and termination signals.
        """
        # Make sure we don't register multiple times
        if getattr(cls, "_exit_hooks_registered", False):
            return

        cls._exit_hooks_registered = True

        def sync_cleanup():
            try:
                # During shutdown, just create a new event loop for cleanup
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(cls.close_all_connections())
                    loop.close()
                except Exception:
                    # If we can't clean up properly, just pass silently during shutdown
                    pass
            except Exception:
                # During shutdown, logging might not work, so just pass silently
                pass

        atexit.register(sync_cleanup)

        for sig in (signal.SIGINT, signal.SIGTERM):
            signal.signal(sig, lambda s, f: (sync_cleanup(), exit(0)))

    @classmethod
    async def create_database(cls, dbname):
        """
        Creates a new database with the specified name, only if it doesn't already exist.
        """
        try:
            # Connect to the PostgreSQL server (not specifying a database yet)
            conn = await connect(user=cls.get_instance().user, password=cls.get_instance().password, host=cls.get_instance().host, port=cls.get_instance().port, database="postgres")

            # Check if the database already exists
            query = f"SELECT 1 FROM pg_database WHERE datname = '{dbname}';"
            existing_db = await conn.fetch(query)

            if existing_db:
                print(f"Database '{dbname}' already exists.")
            else:
                # If the database doesn't exist, create it
                query = f"CREATE DATABASE {dbname};"
                await conn.execute(query)
                print(f"Database '{dbname}' created successfully.")

            # Close the connection
            await conn.close()

        except Exception as e:
            print(f"Error creating database '{dbname}': {e}")
            # Re-raise for proper handling upstream
            raise

    @classmethod
    async def delete_database(cls, dbname):
        """
        Deletes a specified database if it exists and there are no active connections.
        """
        try:
            # Connect to the PostgreSQL server (not specifying a database yet)
            conn = await connect(user=cls.user, password=cls.password, host=cls.host, port=cls.port,database="postgres")

            # Check if the database exists
            query = f"SELECT 1 FROM pg_database WHERE datname = '{dbname}';"
            existing_db = await conn.fetch(query)

            if not existing_db:
                print(f"Database '{dbname}' does not exist.")
                await conn.close()
                return

            # Check for active connections to the database
            query = f"""
                SELECT pid FROM pg_stat_activity WHERE datname = '{dbname}' AND pid <> pg_backend_pid();
            """
            active_connections = await conn.fetch(query)

            if active_connections:
                print(f"Cannot delete database '{dbname}' because there are active connections.")
                await conn.close()
                return

            # If no active connections, proceed to drop the database
            query = f"DROP DATABASE {dbname};"
            await conn.execute(query)
            print(f"Database '{dbname}' deleted successfully.")

            # Close the connection
            await conn.close()

        except Exception as e:
            print(f"Error deleting database '{dbname}': {e}")

    @classmethod
    async def connect_to_database(cls, dbname, use_pool=False, min_size=1, max_size=10, retries=5, delay=1):
        instance = cls.__new__(cls)
        instance._connection_modes[dbname] = use_pool

        for attempt in range(retries):
            try:
                if use_pool:
                    pool = await create_pool(
                        database=dbname,
                        user=cls.user,
                        password=cls.password,
                        host=cls.host,
                        port=cls.port,
                        min_size=min_size,
                        max_size=max_size
                    )
                    instance._pools[dbname] = pool
                    return pool
                else:
                    conn = await connect(
                        database=dbname,
                        user=cls.user,
                        password=cls.password,
                        host=cls.host,
                        port=cls.port
                    )
                    instance._connections[dbname] = conn
                    return conn

            except (CannotConnectNowError, ConnectionRefusedError, gaierror, OSError) as e:
                if attempt < retries - 1:
                    await asyncio.sleep(delay * (2 ** attempt))  # exponential backoff
                else:
                    raise RuntimeError(f"Database '{dbname}' not ready after {retries} attempts: {e}")

    @classmethod
    def _using_pool(cls, dbname):
        return cls.__new__(cls)._connection_modes.get(dbname, False)

    @classmethod
    async def get_connection(cls, dbname):
        instance = cls.__new__(cls)
        if cls._using_pool(dbname):
            return instance._pools.get(dbname)
        return instance._connections.get(dbname)
            
    @classmethod
    async def get_table_names(cls, dbname, schema:str="public"):
        """
        Retrieves the list of table names in the specified database.
        """
        try:
            # Check if we are using a pool or a direct connection
            if cls._using_pool(dbname):
                pool = await cls.get_connection(dbname)
                if pool is None:
                    raise Exception(f"No pool found for database '{dbname}'.")
                async with pool.acquire() as conn:
                    query = "SELECT table_name FROM information_schema.tables WHERE table_schema = '" + schema + "';"
                    tables = await conn.fetch(query)
            else:
                conn = await cls.get_connection(dbname)
                if conn is None:
                    raise Exception(f"No connection found for database '{dbname}'.")
                query = "SELECT table_name FROM information_schema.tables WHERE table_schema = '" + schema + "';"
                tables = await conn.fetch(query)

            # Extract table names from the results and return them
            table_names = [table['table_name'] for table in tables]
            return table_names
        except Exception as e:
            print(f"Error retrieving table names for database '{dbname}': {e}")
            return []

    @classmethod
    async def execute_query(cls, dbname, query, params=None):
        params = params or []
        if cls._using_pool(dbname):
            pool = await cls.get_connection(dbname)
            if pool is None:
                raise Exception(f"No pool found for database '{dbname}'.")
            async with pool.acquire() as conn:
                try:
                    return await conn.fetch(query, *params)
                except Exception as e:
                    print("Error during query execution:", e)
        else:
            conn = await cls.get_connection(dbname)
            if conn is None:
                raise Exception(f"No connection found for database '{dbname}'.")
            return await conn.fetch(query, *params)

    @classmethod
    async def execute_non_query(cls, dbname, query, params=None):
        params = params or []
        if cls._using_pool(dbname):
            pool = await cls.get_connection(dbname)
            if pool is None:
                raise Exception(f"No pool found for database '{dbname}'.")
            async with pool.acquire() as conn:
                await conn.execute(query, *params)
        else:
            conn = await cls.get_connection(dbname)
            if conn is None:
                raise Exception(f"No connection found for database '{dbname}'.")
            await conn.execute(query, *params)

    @classmethod
    async def start_transaction(cls, dbname):
        if cls._using_pool(dbname):
            pool = await cls.get_connection(dbname)
            if pool is None:
                raise Exception(f"No pool found for database '{dbname}'.")
            conn = await pool.acquire()
            tx = conn.transaction()
            await tx.start()
            return conn, tx
        else:
            conn = await cls.get_connection(dbname)
            if conn is None:
                raise Exception(f"No connection found for database '{dbname}'.")
            tx = conn.transaction()
            await tx.start()
            return conn, tx

    @classmethod
    async def commit_transaction(cls, conn, tx, dbname=None):
        await tx.commit()
        if dbname and cls._using_pool(dbname):
            pool = await cls.get_connection(dbname)
            await pool.release(conn)

    @classmethod
    async def rollback_transaction(cls, conn, tx, dbname=None):
        await tx.rollback()
        if dbname and cls._using_pool(dbname):
            pool = await cls.get_connection(dbname)
            await pool.release(conn)

    @classmethod
    async def execute_many(cls, dbname, query, list_of_params):
        if cls._using_pool(dbname):
            pool = await cls.get_connection(dbname)
            if pool is None:
                raise Exception(f"No pool found for database '{dbname}'.")
            async with pool.acquire() as conn:
                await conn.executemany(query, list_of_params)
        else:
            conn = await cls.get_connection(dbname)
            if conn is None:
                raise Exception(f"No connection found for database '{dbname}'.")
            await conn.executemany(query, list_of_params)

    @classmethod
    async def reconnect(cls, dbname, user="ai", password="ai", host="localhost", port=PORT_POSTGRESQL, min_size=1, max_size=10):
        """Note that the default host and port only work if not running inside docker!!! ALLLLL clients are running docker!"""
        instance = cls.__new__(cls)
        use_pool = instance._connection_modes.get(dbname, False)
        await cls.close_connection(dbname)
        return await cls.connect_to_database(dbname, user, password, host, port, use_pool, min_size, max_size)

    @classmethod
    async def close_connection(cls, dbname):
        instance = cls.__new__(cls)
        if cls._using_pool(dbname):
            pool = instance._pools.get(dbname)
            if pool:
                await pool.close()
                del instance._pools[dbname]
        else:
            conn = instance._connections.get(dbname)
            if conn:
                await conn.close()
                del instance._connections[dbname]
        instance._connection_modes.pop(dbname, None)

    @classmethod
    async def close_all_connections(cls):
        instance = cls.__new__(cls)
        for conn in instance._connections.values():
            await conn.close()
        for pool in instance._pools.values():
            await pool.close()
        instance._connections.clear()
        instance._pools.clear()
        instance._connection_modes.clear()

    @classmethod
    async def test_connection(cls, dbname):
        try:
            if cls._using_pool(dbname):
                pool = await cls.connect_to_database(dbname, use_pool=True)
                async with pool.acquire() as conn:
                    await conn.fetch("SELECT 1")
            else:
                conn = await cls.connect_to_database(dbname)
                await conn.fetch("SELECT 1")
            return True
        except Exception as e:
            print(f"Connection test failed for {dbname}: {e}")
            return False

