"""
Unit tests for database operations (PostgreSQL and Qdrant)
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.unit
@pytest.mark.asyncio
class TestPostgreSQLOperations:
    """Test PostgreSQL database operations"""
    
    async def test_connection_management(self, mock_database_connections):
        """Test database connection creation and management"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        connection = await manager.get_connection("test_db")
        
        assert connection is not None
        mock_database_connections['postgresql'].get_connection.assert_called_once()
    
    async def test_scheduled_task_persistence(self, sample_scheduled_task_data, mock_database_connections):
        """Test scheduled task CRUD operations"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        
        manager = ScheduledTaskPersistenceManager.get_instance()
        
        # Mock the database operations
        with patch.object(manager, 'save_task') as mock_save, \
             patch.object(manager, 'get_active_tasks') as mock_get:
            
            mock_save.return_value = True
            mock_get.return_value = [sample_scheduled_task_data]
            
            # Create a mock scheduled task object
            from unittest.mock import MagicMock
            mock_task = MagicMock()
            mock_task.task_id = 'test-task-123'
            mock_task.user.GUID = 'test-user-123'
            
            # Test save
            result = await manager.save_task(mock_task)
            assert result is True
            
            # Test retrieve
            tasks = await manager.get_active_tasks("test-user-123")
            assert len(tasks) == 1
            assert tasks[0]['task_id'] == 'test-task-123'
    
    async def test_database_error_handling(self, mock_database_connections):
        """Test database error handling and recovery"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        # Simulate connection failure
        mock_database_connections['postgresql'].get_connection.side_effect = Exception("Connection failed")
        
        with pytest.raises(Exception) as exc_info:
            await manager.get_connection("test_db")
        
        assert "Connection failed" in str(exc_info.value)

@pytest.mark.unit
@pytest.mark.asyncio
class TestQdrantOperations:
    """Test Qdrant vector database operations"""
    
    async def test_vector_upsert(self, mock_database_connections):
        """Test vector embedding upsert operations"""
        from managers.manager_qdrant import QDrantManager
        
        manager = QDrantManager.get_instance()
        mock_database_connections['qdrant'].upsert.return_value = True
        
        result = await manager.upsert("doc-123", "Test document content", {"source": "test"})
        assert result is True
        mock_database_connections['qdrant'].upsert.assert_called_once()
    
    async def test_vector_search(self, mock_database_connections):
        """Test vector similarity search"""
        from managers.manager_qdrant import QDrantManager
        
        manager = QDrantManager.get_instance()
        mock_results = [
            {'id': 'doc-1', 'score': 0.95, 'payload': {'text': 'Relevant document'}},
            {'id': 'doc-2', 'score': 0.85, 'payload': {'text': 'Another document'}}
        ]
        mock_database_connections['qdrant'].search_similar.return_value = mock_results
        
        results = await manager.search_similar("test query", limit=10)
        assert len(results) == 2
        assert results[0]['score'] == 0.95
    
    async def test_vector_search_empty_results(self, mock_database_connections):
        """Test vector search with no results"""
        from managers.manager_qdrant import QDrantManager
        
        manager = QDrantManager.get_instance()
        mock_database_connections['qdrant'].search_similar.return_value = []
        
        results = await manager.search_similar("nonexistent query")
        assert len(results) == 0