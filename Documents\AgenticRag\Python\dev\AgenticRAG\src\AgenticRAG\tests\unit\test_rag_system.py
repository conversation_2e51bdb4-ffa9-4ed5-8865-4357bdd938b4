"""
Unit tests for RAG (Retrieval-Augmented Generation) system
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.unit
@pytest.mark.asyncio
class TestRAGRetrieval:
    """Test RAG retrieval system components"""
    
    async def test_retrieval_manager_initialization(self, mock_rag_components):
        """Test RetrievalManager initialization and configuration"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        assert manager is not None
    
    async def test_document_embedding_generation(self, mock_rag_components):
        """Test document embedding generation"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Mock embedding generation
        with patch.object(manager, 'generate_embeddings') as mock_embed:
            mock_embed.return_value = [0.1, 0.2, 0.3, 0.4]  # Mock embedding vector
            
            embeddings = await manager.generate_embeddings("Test document content")
            assert len(embeddings) == 4
            assert all(isinstance(x, float) for x in embeddings)
    
    async def test_similarity_search(self, mock_rag_components):
        """Test vector similarity search functionality"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Test similarity search
        results = await manager.retrieve_similar("test query", top_k=5)
        assert len(results) == 2  # From fixture
        assert results[0]['score'] == 0.9
        assert results[1]['score'] == 0.8
    
    async def test_document_chunking(self, mock_rag_components):
        """Test document chunking logic"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Mock chunking
        with patch.object(manager, 'chunk_document') as mock_chunk:
            long_document = "This is a very long document. " * 100
            mock_chunk.return_value = [
                "This is a very long document. " * 25,
                "This is a very long document. " * 25,
                "This is a very long document. " * 25,
                "This is a very long document. " * 25
            ]
            
            chunks = await manager.chunk_document(long_document)
            assert len(chunks) == 4
            assert all(len(chunk) > 0 for chunk in chunks)
    
    async def test_ocr_document_processing(self, mock_rag_components):
        """Test OCR document processing with Tesseract"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Mock OCR processing
        with patch.object(manager, 'process_image_document') as mock_ocr:
            mock_ocr.return_value = "Extracted text from image document"
            
            # Simulate image document processing
            extracted_text = await manager.process_image_document("fake_image_path.png")
            assert extracted_text == "Extracted text from image document"

@pytest.mark.unit
@pytest.mark.asyncio
class TestRAGMetrics:
    """Test RAG system performance metrics"""
    
    async def test_metrics_logging(self, mock_rag_components):
        """Test RAG metrics logging functionality"""
        from etc.rag_metrics_logger import log_rag_metrics_to_csv
        
        # Test metrics logging with the actual function
        query = "test query"
        vector_search_results = 10
        keyword_search_results = 5
        llm_reranker_results = 3
        chunk_scores = [0.9, 0.8, 0.7]
        total_chunks_used = 3
        chunk_contexts = [
            ("Test chunk 1", "source1.txt", 0.9),
            ("Test chunk 2", "source2.txt", 0.8),
            ("Test chunk 3", "source3.txt", 0.7)
        ]
        
        # Test logging (use a test file path to avoid writing to logs)
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as tmp_file:
            test_csv_path = tmp_file.name
        
        try:
            result = log_rag_metrics_to_csv(
                query=query,
                vector_search_results=vector_search_results,
                keyword_search_results=keyword_search_results,
                llm_reranker_results=llm_reranker_results,
                chunk_scores=chunk_scores,
                total_chunks_used=total_chunks_used,
                chunk_contexts=chunk_contexts,
                csv_file_path=test_csv_path
            )
            
            assert result == test_csv_path
            
            # Verify file was created and has content
            import os
            assert os.path.exists(test_csv_path)
            assert os.path.getsize(test_csv_path) > 0
            
        finally:
            # Clean up test file
            import os
            if os.path.exists(test_csv_path):
                os.unlink(test_csv_path)
    
    async def test_retrieval_accuracy_scoring(self, mock_rag_components):
        """Test retrieval result accuracy scoring"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Mock accuracy calculation
        with patch.object(manager, 'calculate_retrieval_accuracy') as mock_accuracy:
            mock_accuracy.return_value = 0.85  # 85% accuracy
            
            accuracy = await manager.calculate_retrieval_accuracy(
                query="test query",
                expected_results=["doc1", "doc2"],
                actual_results=[
                    {"id": "doc1", "score": 0.9},
                    {"id": "doc3", "score": 0.8}
                ]
            )
            
            assert accuracy == 0.85

@pytest.mark.unit
@pytest.mark.asyncio  
class TestRAGErrorHandling:
    """Test RAG system error handling"""
    
    async def test_embedding_service_failure(self, mock_rag_components):
        """Test handling of embedding service failures"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Simulate embedding service failure
        with patch.object(manager, 'generate_embeddings') as mock_embed:
            mock_embed.side_effect = Exception("Embedding service unavailable")
            
            with pytest.raises(Exception) as exc_info:
                await manager.generate_embeddings("test document")
            
            assert "Embedding service unavailable" in str(exc_info.value)
    
    async def test_search_service_failure(self, mock_rag_components):
        """Test handling of vector search service failures"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Simulate search service failure
        manager.retrieve_similar.side_effect = Exception("Vector search unavailable")
        
        with pytest.raises(Exception) as exc_info:
            await manager.retrieve_similar("test query")
        
        assert "Vector search unavailable" in str(exc_info.value)
    
    async def test_malformed_document_handling(self, mock_rag_components):
        """Test handling of malformed or corrupted documents"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Mock malformed document processing
        with patch.object(manager, 'process_document') as mock_process:
            mock_process.side_effect = Exception("Document format not supported")
            
            with pytest.raises(Exception) as exc_info:
                await manager.process_document("corrupted_document.xyz")
            
            assert "Document format not supported" in str(exc_info.value)