"""
Unit tests for the LangGraph supervisor framework
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.unit
@pytest.mark.asyncio
class TestSupervisorFramework:
    """Test LangGraph supervisor framework components"""
    
    async def test_supervisor_task_base(self, mock_database_connections):
        """Test SupervisorTask_Base functionality"""
        from managers.manager_supervisors import SupervisorTask_Base, SupervisorTaskState
        
        class TestTask(SupervisorTask_Base):
            async def llm_call(self, state: SupervisorTaskState) -> SupervisorTaskState:
                # Add a test result to sections instead of direct state assignment
                from managers.manager_supervisors import SupervisorSection
                state.sections['test_result'] = SupervisorSection.from_values('test_result', 'success')
                return state
        
        task = TestTask("test_task")
        initial_state = SupervisorTaskState(
            conversation_history=[],
            reasoning_steps=[],
            original_input='test',
            user_guid='test-user'
        )
        
        result_state = await task.llm_call(initial_state)
        assert 'test_result' in result_state.sections
        assert str(result_state.sections['test_result']) == 'success'
    
    async def test_supervisor_task_chainofthought(self, mock_database_connections):
        """Test SupervisorTask_ChainOfThought reasoning"""
        from managers.manager_supervisors import SupervisorTask_ChainOfThought, SupervisorTaskState
        
        class TestCoTTask(SupervisorTask_ChainOfThought):
            async def llm_call(self, state: SupervisorTaskState) -> SupervisorTaskState:
                # Add reasoning step
                state.reasoning_steps.append("Step 1: Analyzing input")
                state.reasoning_steps.append("Step 2: Processing data")
                from managers.manager_supervisors import SupervisorSection
                state.sections['test_result'] = SupervisorSection.from_values('test_result', 'cot_success')
                return state
        
        task = TestCoTTask("test_cot_task")
        initial_state = SupervisorTaskState(
            conversation_history=[],
            reasoning_steps=[],
            original_input='cot_test',
            user_guid='test-user'
        )
        
        result_state = await task.llm_call(initial_state)
        assert len(result_state.reasoning_steps) == 2
        assert 'test_result' in result_state.sections
        assert str(result_state.sections['test_result']) == 'cot_success'
    
    async def test_supervisor_manager_registration(self, mock_database_connections):
        """Test supervisor task registration and retrieval"""
        from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base
        
        class TestRegistrationTask(SupervisorTask_Base):
            async def llm_call(self, state):
                return state
        
        manager = SupervisorManager.get_instance()
        
        # Create a test task instance
        test_task = TestRegistrationTask("test_registration_task")
        
        # Register the test task
        registered_task = manager.register_task(test_task)
        
        # Verify registration
        assert registered_task == test_task
        found_task = manager.find_task(test_task.task_id)
        assert found_task == test_task
    
    async def test_supervisor_state_management(self, mock_database_connections):
        """Test supervisor state persistence and management"""
        from managers.manager_supervisors import SupervisorTaskState
        from langchain_core.messages import HumanMessage, AIMessage
        
        # Create initial state
        state = SupervisorTaskState(
            conversation_history=[
                HumanMessage(content="Test question"),
                AIMessage(content="Test response")
            ],
            reasoning_steps=["Initial reasoning"],
            original_input='state_test',
            user_guid='test-user'
        )
        
        # Verify state structure
        assert len(state.conversation_history) == 2
        assert len(state.reasoning_steps) == 1
        assert state.original_input == 'state_test'
        
        # Test state modification
        state.reasoning_steps.append("Additional reasoning")
        assert len(state.reasoning_steps) == 2

@pytest.mark.unit
@pytest.mark.asyncio
class TestSupervisorCoordination:
    """Test supervisor coordination and routing"""
    
    async def test_supervisor_supervisor_routing(self, mock_database_connections):
        """Test SupervisorSupervisor task routing"""
        from managers.manager_supervisors import SupervisorSupervisor, SupervisorTaskState
        
        # Create supervisor instance
        supervisor = SupervisorSupervisor("test_supervisor")
        
        # Create test state
        state = SupervisorTaskState(
            conversation_history=[],
            reasoning_steps=[],
            original_input='routing_test',
            user_guid='test-user'
        )
        
        # Test supervisor creation
        assert supervisor.name == 'test_supervisor'
        assert len(supervisor.get_tasks()) == 0
    
    async def test_task_error_handling(self, mock_database_connections):
        """Test error handling in supervisor tasks"""
        from managers.manager_supervisors import SupervisorTask_Base, SupervisorTaskState
        
        class ErrorTask(SupervisorTask_Base):
            async def llm_call(self, state: SupervisorTaskState) -> SupervisorTaskState:
                raise Exception("Test error")
        
        task = ErrorTask("error_task")
        state = SupervisorTaskState(
            conversation_history=[],
            reasoning_steps=[],
            original_input='error_test',
            user_guid='test-user'
        )
        
        # Test error handling
        with pytest.raises(Exception) as exc_info:
            await task.llm_call(state)
        
        assert "Test error" in str(exc_info.value)