"""
Comprehensive test suite for multimodal RAG integration
Tests the full multimodal pipeline from document processing to search
"""

from imports import *
import asyncio
import json
from pathlib import Path
import tempfile
from typing import Dict, Any, List

# Create test PDF with images and tables
def create_test_pdf():
    """Create a test PDF with images and tables using reportlab"""
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.lib.units import inch
        from reportlab.lib import colors
        from reportlab.graphics.shapes import Drawing, Rect
        from reportlab.graphics import renderPDF
        
        # Create temporary file
        temp_file = tempfile.NamedTemporaryFile(suffix='.pdf', delete=False)
        doc = SimpleDocTemplate(temp_file.name, pagesize=letter)
        
        # Create content
        story = []
        styles = getSampleStyleSheet()
        
        # Add title
        title = Paragraph("Multimodal Test Document", styles['Title'])
        story.append(title)
        story.append(Spacer(1, 12))
        
        # Add text content
        text1 = Paragraph("This document contains various multimodal elements for testing RAG capabilities.", styles['Normal'])
        story.append(text1)
        story.append(Spacer(1, 12))
        
        # Add table
        table_data = [
            ['Product', 'Sales Q1', 'Sales Q2', 'Growth'],
            ['Product A', '$50,000', '$65,000', '30%'],
            ['Product B', '$75,000', '$90,000', '20%'],
            ['Product C', '$40,000', '$55,000', '37.5%']
        ]
        
        table = Table(table_data)
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 14),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 12))
        
        # Add more text
        text2 = Paragraph("The table above shows quarterly sales data for three products. Product C shows the highest growth rate at 37.5%.", styles['Normal'])
        story.append(text2)
        story.append(Spacer(1, 12))
        
        # Add section about images
        text3 = Paragraph("This document also contains visual elements that demonstrate multimodal capabilities.", styles['Normal'])
        story.append(text3)
        
        # Build PDF
        doc.build(story)
        
        return temp_file.name
        
    except ImportError:
        print("reportlab not available, creating simple text file instead")
        # Create simple text file with table-like content
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False)
        temp_file.write("""
Multimodal Test Document

This document contains various multimodal elements for testing RAG capabilities.

Sales Data Table:
Product     | Sales Q1 | Sales Q2 | Growth
Product A   | $50,000  | $65,000  | 30%
Product B   | $75,000  | $90,000  | 20%
Product C   | $40,000  | $55,000  | 37.5%

The table above shows quarterly sales data for three products. Product C shows the highest growth rate at 37.5%.

This document also contains visual elements that demonstrate multimodal capabilities.
""")
        temp_file.close()
        return temp_file.name

async def test_multimodal_extraction():
    """Test multimodal element extraction from documents"""
    print("\n=== Testing Multimodal Element Extraction ===")
    
    # Create test document
    test_file = create_test_pdf()
    print(f"Created test document: {test_file}")
    
    try:
        # Test multimodal extraction
        from managers.manager_multimodal import MultimodalManager
        
        # Setup manager
        await MultimodalManager.setup()
        
        # Extract multimodal elements
        doc_id = "test_doc_001"
        result = await MultimodalManager.extract_multimodal_elements(test_file, doc_id)
        
        print(f"Extraction result keys: {list(result.keys())}")
        print(f"Total elements extracted: {len(result.get('all_elements', []))}")
        print(f"Text elements: {len(result.get('text_elements', []))}")
        print(f"Tables: {len(result.get('tables', []))}")
        print(f"Images: {len(result.get('images', []))}")
        
        # Show table details if any
        if result.get('tables'):
            for i, table in enumerate(result['tables']):
                print(f"Table {i+1}:")
                print(f"  - Summary: {table.get('summary', 'No summary')[:100]}...")
                print(f"  - Has structure: {table.get('has_structure', False)}")
                if table.get('key_info'):
                    print(f"  - Columns: {table['key_info'].get('num_columns', 0)}")
                    print(f"  - Rows: {table['key_info'].get('num_rows', 0)}")
        
        # Show image details if any
        if result.get('images'):
            for i, image in enumerate(result['images']):
                print(f"Image {i+1}:")
                print(f"  - Has asset: {image.get('has_asset', False)}")
                print(f"  - Summary: {image.get('summary', 'No summary')[:100]}...")
        
        return result
        
    finally:
        # Clean up
        import os
        os.unlink(test_file)

async def test_multimodal_chunking():
    """Test multimodal-aware chunking"""
    print("\n=== Testing Multimodal Chunking ===")
    
    # Create mock multimodal data
    mock_data = {
        "doc_id": "test_doc_002",
        "all_elements": [
            {
                "type": "Title",
                "text": "Test Document Title",
                "element_index": 0
            },
            {
                "type": "NarrativeText",
                "text": "This is introductory text before the image.",
                "element_index": 1
            },
            {
                "type": "Image",
                "text": "Image placeholder",
                "summary": "A chart showing sales growth over time",
                "element_index": 2
            },
            {
                "type": "NarrativeText",
                "text": "This text follows the image and explains the data shown.",
                "element_index": 3
            },
            {
                "type": "Table",
                "text": "Table content",
                "summary": "Sales data table with quarterly figures",
                "markdown": "| Product | Q1 | Q2 |\n|---------|----|----|Product A|$50K|$65K|\n| Product B|$75K|$90K|",
                "element_index": 4
            },
            {
                "type": "NarrativeText",
                "text": "The table shows significant growth across all products.",
                "element_index": 5
            }
        ]
    }
    
    from managers.manager_retrieval import RetrievalManager
    
    # Setup manager
    await RetrievalManager.setup()
    
    # Test chunking
    chunks = await RetrievalManager.chunk_multimodal_elements(mock_data, chunk_size=500)
    
    print(f"Generated {len(chunks)} chunks")
    for i, chunk in enumerate(chunks):
        print(f"Chunk {i+1} (length: {len(chunk)}):")
        print(f"  Preview: {chunk[:150]}...")
        print(f"  Contains [IMAGE]: {'[IMAGE:' in chunk}")
        print(f"  Contains [TABLE]: {'[TABLE:' in chunk}")
        print()
    
    return chunks

async def test_multimodal_search():
    """Test multimodal search functionality"""
    print("\n=== Testing Multimodal Search ===")
    
    # Test the search tools
    from tasks.inputs.task_retrieval import multimodal_search_tool, image_focused_search_tool, table_focused_search_tool
    
    test_queries = [
        "sales data",
        "quarterly growth",
        "product performance",
        "charts and graphs"
    ]
    
    for query in test_queries:
        print(f"\nTesting query: '{query}'")
        
        try:
            # Test multimodal search
            result = await multimodal_search_tool.ainvoke({"query": query})
            print(f"Multimodal search result length: {len(str(result))}")
            
            # Test image search
            img_result = await image_focused_search_tool.ainvoke({"query": query})
            print(f"Image search result length: {len(str(img_result))}")
            
            # Test table search
            table_result = await table_focused_search_tool.ainvoke({"query": query})
            print(f"Table search result length: {len(str(table_result))}")
            
        except Exception as e:
            print(f"Error testing query '{query}': {e}")

async def test_qdrant_multimodal_integration():
    """Test Qdrant multimodal search integration"""
    print("\n=== Testing Qdrant Multimodal Integration ===")
    
    from managers.manager_qdrant import QDrantManager
    
    # Setup QDrant
    await QDrantManager.setup()
    
    test_cases = [
        {
            "query": "sales performance",
            "has_images": True,
            "has_tables": None,
            "content_type": None
        },
        {
            "query": "quarterly data",
            "has_images": None,
            "has_tables": True,
            "content_type": "table_summary"
        },
        {
            "query": "product analysis",
            "has_images": None,
            "has_tables": None,
            "content_type": None
        }
    ]
    
    for case in test_cases:
        print(f"\nTesting case: {case}")
        try:
            result = await QDrantManager.search_multimodal(**case)
            print(f"Search result type: {type(result)}")
            print(f"Result preview: {str(result)[:200]}...")
        except Exception as e:
            print(f"Error in multimodal search: {e}")

async def test_document_asset_retrieval():
    """Test document asset retrieval"""
    print("\n=== Testing Document Asset Retrieval ===")
    
    from managers.manager_qdrant import QDrantManager
    from tasks.inputs.task_retrieval import get_document_assets_tool
    
    # Test asset retrieval
    test_doc_ids = ["test_doc_001", "test_doc_002", "nonexistent_doc"]
    
    for doc_id in test_doc_ids:
        print(f"\nTesting document ID: {doc_id}")
        try:
            # Test via QDrant manager
            assets = await QDrantManager.get_document_assets(doc_id)
            print(f"Assets found: {len(assets.get('images', []))} images, {len(assets.get('tables', []))} tables, {len(assets.get('chunks', []))} chunks")
            
            # Test via tool
            tool_result = await get_document_assets_tool.ainvoke({"doc_id": doc_id})
            print(f"Tool result length: {len(str(tool_result))}")
            
        except Exception as e:
            print(f"Error retrieving assets for {doc_id}: {e}")

async def test_integration_with_supervisor():
    """Test integration with supervisor task system"""
    print("\n=== Testing Supervisor Integration ===")
    
    from tasks.inputs.task_retrieval import create_supervisor_retrieval
    from managers.manager_supervisors import SupervisorTaskState
    
    try:
        # Create supervisor
        supervisor = await create_supervisor_retrieval()
        print(f"Created supervisor: {supervisor.name}")
        
        # Test multimodal queries
        test_queries = [
            "Show me sales charts",
            "Find tables with quarterly data",
            "What images show product performance?",
            "Get document assets for test_doc_001"
        ]
        
        for query in test_queries:
            print(f"\nTesting supervisor query: '{query}'")
            
            # Create test state
            state = SupervisorTaskState(
                original_input=query,
                user_guid="test_user_001",
                messages=[],
                call_trace=[],
                completed_tasks=[]
            )
            
            try:
                # Test routing decision
                result = await supervisor.route_decision(state)
                print(f"Routing result: {result}")
                
            except Exception as e:
                print(f"Error in supervisor routing: {e}")
                
    except Exception as e:
        print(f"Error creating supervisor: {e}")

async def run_comprehensive_test():
    """Run all multimodal tests"""
    print("Starting Comprehensive Multimodal RAG Test Suite")
    print("=" * 60)
    
    try:
        # Test 1: Multimodal extraction
        extraction_result = await test_multimodal_extraction()
        
        # Test 2: Multimodal chunking
        chunking_result = await test_multimodal_chunking()
        
        # Test 3: Multimodal search
        await test_multimodal_search()
        
        # Test 4: Qdrant integration
        await test_qdrant_multimodal_integration()
        
        # Test 5: Document asset retrieval
        await test_document_asset_retrieval()
        
        # Test 6: Supervisor integration
        await test_integration_with_supervisor()
        
        print("\n" + "=" * 60)
        print("[OK] Comprehensive Multimodal RAG Test Suite Completed")
        print("=" * 60)
        
    except Exception as e:
        print(f"\n[ERROR] Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(run_comprehensive_test())