from imports import *

import json
import asyncio
from datetime import datetime, timed<PERSON>ta
from typing import Dict, List, Optional, Any
from uuid import UUID, uuid4

from managers.manager_postgreSQL import PostgreSQLManager
from userprofiles.ZairaUser import Zaira<PERSON>ser
from endpoints.mybot_generic import MyBot_Generic

class ScheduledTaskPersistenceManager:
    """
    Manages persistence of ScheduledZairaTasks to PostgreSQL database.
    Handles saving, loading, and recovery of scheduled tasks across server restarts.
    """
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.ScheduledZairaTask import ScheduledZairaTask
    
    _instance: Optional['ScheduledTaskPersistenceManager'] = None
    _initialized: bool = False
    _active_tasks: Dict[str, 'ScheduledZairaTask'] = {}
    _paused_tasks: Dict[str, Dict[str, Any]] = {}  # Tasks waiting for user login
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance._db_available = True
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> 'ScheduledTaskPersistenceManager':
        """Get singleton instance of ScheduledTaskPersistenceManager"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        """Initialize the persistence manager and create database tables"""
        instance = cls()
        if instance._initialized:
            return
        
        instance._initialized = True
        await instance._create_tables()
        await instance._recover_tasks()
        
        LogFire.log("SCHEDULED_TASKS", "ScheduledTaskPersistenceManager initialized")
    
    async def _create_tables(self):
        """Create the scheduled_tasks table if it doesn't exist"""
        try:
            # First check if connection exists, if not create it
            connection = await PostgreSQLManager.get_connection("zaira_scheduled_tasks")
            if connection is None:
                # Create database first if it doesn't exist
                await PostgreSQLManager.create_database("zaira_scheduled_tasks")
                # Then connect to it
                connection = await PostgreSQLManager.connect_to_database("zaira_scheduled_tasks")
            
            create_table_query = """
            CREATE TABLE IF NOT EXISTS scheduled_tasks (
                task_id VARCHAR(36) PRIMARY KEY,
                user_id VARCHAR(36) NOT NULL,
                schedule_prompt TEXT NOT NULL,
                target_prompt TEXT NOT NULL,
                delay_seconds FLOAT NOT NULL,
                schedule_type VARCHAR(20) NOT NULL,
                next_execution TIMESTAMP,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                calling_bot_name VARCHAR(100),
                task_data JSONB,
                cancellation_reason TEXT,
                cancelled_at TIMESTAMP
            );
            
            CREATE INDEX IF NOT EXISTS idx_scheduled_tasks_user_id ON scheduled_tasks(user_id);
            CREATE INDEX IF NOT EXISTS idx_scheduled_tasks_active ON scheduled_tasks(is_active);
            CREATE INDEX IF NOT EXISTS idx_scheduled_tasks_next_execution ON scheduled_tasks(next_execution);
            """
            
            await connection.execute(create_table_query)
            LogFire.log("SCHEDULED_TASKS", "Database tables created/verified")
            
        except (ConnectionRefusedError, RuntimeError, OSError) as e:
            LogFire.log("WARNING", f"PostgreSQL not available, scheduled tasks will be disabled: {str(e)}")
            # Don't raise the error - allow the application to continue without scheduled tasks
            self._db_available = False
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Failed to create scheduled tasks tables")
            raise
    
    async def save_task(self, scheduled_task: 'ScheduledZairaTask') -> bool:
        """
        Save a ScheduledZairaTask to the database
        
        Args:
            scheduled_task: The ScheduledZairaTask instance to save
            
        Returns:
            bool: True if saved successfully, False otherwise
        """
        try:
            connection = await PostgreSQLManager.get_connection("zaira_scheduled_tasks")
            
            # Prepare task data
            task_data = {
                'schedule_info': scheduled_task.get_schedule_info(),
                'task_status': scheduled_task.get_task_status(),
                'original_message_type': type(scheduled_task.original_physical_message).__name__ if scheduled_task.original_physical_message else None
            }
            
            insert_query = """
            INSERT INTO scheduled_tasks (
                task_id, user_id, schedule_prompt, target_prompt, 
                delay_seconds, schedule_type, next_execution, is_active,
                calling_bot_name, task_data, updated_at
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            ON CONFLICT (task_id) DO UPDATE SET
                schedule_prompt = EXCLUDED.schedule_prompt,
                target_prompt = EXCLUDED.target_prompt,
                delay_seconds = EXCLUDED.delay_seconds,
                schedule_type = EXCLUDED.schedule_type,
                next_execution = EXCLUDED.next_execution,
                is_active = EXCLUDED.is_active,
                calling_bot_name = EXCLUDED.calling_bot_name,
                task_data = EXCLUDED.task_data,
                updated_at = EXCLUDED.updated_at
            """
            
            await connection.execute(
                insert_query,
                str(scheduled_task.task_id),
                str(scheduled_task.user.GUID),
                scheduled_task.schedule_prompt,
                scheduled_task.target_prompt,
                scheduled_task.delay_seconds,
                scheduled_task.schedule_type.value,
                scheduled_task.next_execution,
                scheduled_task.is_active,
                scheduled_task.calling_bot.name if scheduled_task.calling_bot else None,
                json.dumps(task_data),
                datetime.now()
            )
            
            # Add to active tasks tracking
            self._active_tasks[str(scheduled_task.task_id)] = scheduled_task
            
            LogFire.log("SCHEDULED_TASKS", f"Saved scheduled task {scheduled_task.task_id}")
            return True
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to save scheduled task {scheduled_task.task_id}: {str(e)}")
            return False
    
    async def load_task(self, task_id: str) -> Optional[Dict[str, Any]]:
        """
        Load a scheduled task from the database
        
        Args:
            task_id: The task ID to load
            
        Returns:
            Dict with task data or None if not found
        """
        try:
            connection = await PostgreSQLManager.get_connection("zaira_scheduled_tasks")
            
            query = "SELECT * FROM scheduled_tasks WHERE task_id = $1"
            result = await connection.fetchrow(query, task_id)
            
            if result:
                return dict(result)
            return None
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to load scheduled task {task_id}: {str(e)}")
            return None
    
    async def get_active_tasks(self, user_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get all active scheduled tasks, optionally filtered by user
        
        Args:
            user_id: Optional user ID to filter by
            
        Returns:
            List of task dictionaries
        """
        try:
            connection = await PostgreSQLManager.get_connection("zaira_scheduled_tasks")
            
            if user_id:
                query = "SELECT * FROM scheduled_tasks WHERE user_id = $1 AND is_active = TRUE ORDER BY created_at"
                results = await connection.fetch(query, user_id)
            else:
                query = "SELECT * FROM scheduled_tasks WHERE is_active = TRUE ORDER BY created_at"
                results = await connection.fetch(query)
            
            return [dict(row) for row in results]
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get active tasks: {str(e)}")
            return []
    
    async def cancel_task(self, task_id: str, reason: str = "User requested cancellation") -> bool:
        """
        Cancel a scheduled task
        
        Args:
            task_id: The task ID to cancel
            reason: Reason for cancellation
            
        Returns:
            bool: True if cancelled successfully
        """
        try:
            connection = await PostgreSQLManager.get_connection("zaira_scheduled_tasks")
            
            # Update database
            update_query = """
            UPDATE scheduled_tasks 
            SET is_active = FALSE, 
                cancellation_reason = $1, 
                cancelled_at = $2,
                updated_at = $2
            WHERE task_id = $3
            """
            
            result = await connection.execute(
                update_query, 
                reason, 
                datetime.now(), 
                task_id
            )
            
            # Cancel active task if running
            if task_id in self._active_tasks:
                active_task = self._active_tasks[task_id]
                active_task.cancel_schedule()
                del self._active_tasks[task_id]
            
            LogFire.log("SCHEDULED_TASKS", f"Cancelled scheduled task {task_id}: {reason}")
            return True
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to cancel task {task_id}: {str(e)}")
            return False
    
    async def _recover_tasks(self):
        """
        Recover and restart active scheduled tasks after server restart
        """
        try:
            active_tasks = await self.get_active_tasks()
            recovered_count = 0
            
            for task_data in active_tasks:
                try:
                    # Check if task should still be active
                    next_execution = task_data.get('next_execution')
                    if next_execution and isinstance(next_execution, datetime):
                        # If next execution is far in the past, may need adjustment
                        time_diff = datetime.now() - next_execution
                        if time_diff.total_seconds() > 3600:  # More than 1 hour overdue
                            LogFire.log("SCHEDULED_TASKS", f"Task {task_data['task_id']} is overdue, adjusting schedule")
                    
                    # Recreate the scheduled task
                    recovered_task = await self._recreate_task_from_data(task_data)
                    if recovered_task:
                        self._active_tasks[task_data['task_id']] = recovered_task
                        
                        # Start the task in background
                        asyncio.create_task(recovered_task.run_task())
                        recovered_count += 1
                        
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to recover task {task_data['task_id']}: {str(e)}")
                    # Mark task as failed
                    await self.cancel_task(task_data['task_id'], f"Recovery failed: {str(e)}")
            
            paused_count = len(self._paused_tasks)
            LogFire.log("SCHEDULED_TASKS", f"Recovered {recovered_count} scheduled tasks, {paused_count} tasks paused waiting for user login")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to recover scheduled tasks: {str(e)}")
    
    async def resume_user_tasks(self, user_guid: str):
        """
        Resume paused tasks for a user when they log in
        
        Args:
            user_guid: The GUID of the user who logged in
        """
        try:
            tasks_to_resume = []
            for task_id, task_data in list(self._paused_tasks.items()):
                if task_data['user_id'] == user_guid:
                    tasks_to_resume.append((task_id, task_data))
            
            resumed_count = 0
            for task_id, task_data in tasks_to_resume:
                try:
                    # Remove from paused tasks
                    del self._paused_tasks[task_id]
                    
                    # Recreate and start the task
                    recovered_task = await self._recreate_task_from_data(task_data)
                    if recovered_task:
                        self._active_tasks[task_id] = recovered_task
                        # Start the task in background
                        asyncio.create_task(recovered_task.run_task())
                        resumed_count += 1
                        
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to resume task {task_id}: {str(e)}")
            
            if resumed_count > 0:
                LogFire.log("SCHEDULED_TASKS", f"Resumed {resumed_count} paused tasks for user {user_guid}")
                
        except Exception as e:
            LogFire.log("ERROR", f"Failed to resume tasks for user {user_guid}: {str(e)}")
    
    def get_paused_tasks_count(self) -> int:
        """Get the number of paused tasks waiting for user login"""
        return len(self._paused_tasks)
    
    def get_paused_tasks_info(self) -> List[Dict[str, Any]]:
        """Get information about paused tasks"""
        return [
            {
                'task_id': task_id,
                'user_id': task_data['user_id'],
                'schedule_prompt': task_data['schedule_prompt'],
                'target_prompt': task_data['target_prompt']
            }
            for task_id, task_data in self._paused_tasks.items()
        ]
    
    async def _recreate_task_from_data(self, task_data: Dict[str, Any]) -> Optional['ScheduledZairaTask']:
        """
        Recreate a ScheduledZairaTask from database data
        
        Args:
            task_data: Task data from database
            
        Returns:
            ScheduledZairaTask instance or None if recreation fails
        """
        try:
            from userprofiles.ScheduledZairaTask import ScheduledZairaTask, ScheduleType
            from managers.manager_users import ZairaUserManager
            
            # Get user by GUID
            user = await ZairaUserManager.find_user(task_data['user_id'])
            if not user:
                LogFire.log("SCHEDULED_TASKS", f"User {task_data['user_id']} not found, pausing task {task_data['task_id']}")
                # Store task data for later resumption when user logs in
                self._paused_tasks[task_data['task_id']] = task_data
                return None
            
            # Create bot instance
            bot = MyBot_Generic(None, task_data.get('calling_bot_name', 'recovered_task'))
            
            # Create task instance
            task = ScheduledZairaTask.__new__(ScheduledZairaTask)
            
            # Restore properties
            task.task_id = UUID(task_data['task_id'])
            task.user = user
            task.calling_bot = bot
            task.original_physical_message = None  # Can't restore original message
            task.schedule_prompt = task_data['schedule_prompt']
            task.target_prompt = task_data['target_prompt']
            task.delay_seconds = task_data['delay_seconds']
            task.schedule_type = ScheduleType(task_data['schedule_type'])
            task.next_execution = task_data['next_execution']
            task.is_active = task_data['is_active']
            task.complete_message = f"Recovered task: {task_data['schedule_prompt']}"
            
            # Initialize parent class properties
            task.task_status = {}
            task._lock = asyncio.Lock()
            
            LogFire.log("SCHEDULED_TASKS", f"Recreated task {task_data['task_id']}")
            return task
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to recreate task from data: {str(e)}")
            return None
    
    async def cleanup_old_tasks(self, days_old: int = 30):
        """
        Clean up old cancelled/completed tasks from database
        
        Args:
            days_old: Remove tasks older than this many days
        """
        try:
            connection = await PostgreSQLManager.get_connection("zaira_scheduled_tasks")
            
            cutoff_date = datetime.now() - timedelta(days=days_old)
            
            delete_query = """
            DELETE FROM scheduled_tasks 
            WHERE is_active = FALSE 
            AND (cancelled_at < $1 OR (cancelled_at IS NULL AND updated_at < $1))
            """
            
            result = await connection.execute(delete_query, cutoff_date)
            LogFire.log("SCHEDULED_TASKS", f"Cleaned up old tasks: {result}")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to cleanup old tasks: {str(e)}")
    
    def get_active_task(self, task_id: str) -> Optional['ScheduledZairaTask']:
        """Get an active task by ID"""
        return self._active_tasks.get(task_id)
    
    def get_all_active_tasks(self) -> Dict[str, 'ScheduledZairaTask']:
        """Get all currently active tasks"""
        return self._active_tasks.copy()


# Global instance
_persistence_manager = ScheduledTaskPersistenceManager()

async def get_persistence_manager() -> ScheduledTaskPersistenceManager:
    """Get the global persistence manager instance - DEPRECATED: Use ScheduledTaskPersistenceManager.get_instance() instead"""
    manager = ScheduledTaskPersistenceManager.get_instance()
    await manager.setup()
    return manager