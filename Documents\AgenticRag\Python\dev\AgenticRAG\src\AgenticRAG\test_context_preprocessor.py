"""
Test script for the Context Preprocessor

This script tests the context preprocessor with various conversation scenarios
to ensure it properly handles different types of conversations and produces
consistent, structured output for supervisor routing.
"""

import asyncio
import sys
from datetime import datetime
from typing import List
from uuid import uuid4

# Add the src directory to Python path for imports
sys.path.append('/mnt/c/Users/<USER>/Documents/AgenticRag/Python/dev/AgenticRAG/src/AgenticRAG')

from managers.manager_context_preprocessor import preprocess_for_supervisor, ContextPreprocessor
from userprofiles.ZairaMessage import ZairaMessage, MessageRole
from userprofiles.ZairaUser import ZairaUser


class MockUser:
    """Mock user for testing purposes"""
    def __init__(self, username="TestUser"):
        self.username = username
        self.GUID = uuid4()


def create_test_messages() -> List[ZairaMessage]:
    """Create test conversation scenarios"""
    messages = []
    
    # Scenario 1: Email task conversation
    messages.append(ZairaMessage.create_user_message(
        "Can you help me draft an email to <PERSON> about the quarterly report?"
    ))
    messages.append(ZairaMessage.create_assistant_message(
        "I'd be happy to help you draft an email to <PERSON> about the quarterly report. Could you provide more details about what you'd like to include in the email?"
    ))
    messages.append(ZairaMessage.create_user_message(
        "I need to ask him to review the Q3 numbers and provide feedback by Friday."
    ))
    messages.append(ZairaMessage.create_assistant_message(
        "Here's a draft email for John about the quarterly report review..."
    ))
    
    # Scenario 2: Topic shift to search
    messages.append(ZairaMessage.create_user_message(
        "Actually, can you search for any documents about project Alpha first?"
    ))
    
    return messages


async def test_basic_preprocessing():
    """Test basic preprocessing functionality"""
    print("🧪 Testing Basic Preprocessing...")
    
    # Create test data
    user = MockUser()
    messages = create_test_messages()
    current_query = "Find the latest version of the Alpha project documentation"
    
    # Test preprocessing
    try:
        result = await preprocess_for_supervisor(
            chat_history=messages,
            current_query=current_query,
            user=user,
            max_messages=5
        )
        
        print("✅ Basic preprocessing successful!")
        print("📄 Formatted Output:")
        print("-" * 50)
        print(result)
        print("-" * 50)
        print(f"📊 Estimated tokens: {len(result) // 4}")
        
    except Exception as e:
        print(f"❌ Basic preprocessing failed: {e}")
        import traceback
        traceback.print_exc()


async def test_empty_conversation():
    """Test preprocessing with empty conversation"""
    print("\n🧪 Testing Empty Conversation...")
    
    user = MockUser()
    current_query = "Hello, can you help me with something?"
    
    try:
        result = await preprocess_for_supervisor(
            chat_history=[],
            current_query=current_query,
            user=user,
            max_messages=5
        )
        
        print("✅ Empty conversation preprocessing successful!")
        print("📄 Formatted Output:")
        print("-" * 30)
        print(result)
        print("-" * 30)
        
    except Exception as e:
        print(f"❌ Empty conversation preprocessing failed: {e}")
        import traceback
        traceback.print_exc()


async def test_long_conversation():
    """Test preprocessing with long conversation"""
    print("\n🧪 Testing Long Conversation...")
    
    user = MockUser()
    
    # Create a long conversation
    long_messages = []
    topics = ["email", "calendar", "search", "analysis", "technical"]
    
    for i in range(15):  # Create 15 messages
        topic = topics[i % len(topics)]
        if i % 2 == 0:  # User message
            content = f"Can you help me with {topic} task number {i//2 + 1}?"
            long_messages.append(ZairaMessage.create_user_message(content))
        else:  # Assistant message
            content = f"Sure, I can help you with that {topic} task. Here's what I found..."
            long_messages.append(ZairaMessage.create_assistant_message(content))
    
    current_query = "Summarize all the tasks we've discussed"
    
    try:
        result = await preprocess_for_supervisor(
            chat_history=long_messages,
            current_query=current_query,
            user=user,
            max_messages=10
        )
        
        print("✅ Long conversation preprocessing successful!")
        print("📄 Formatted Output (first 500 chars):")
        print("-" * 30)
        print(result[:500] + "..." if len(result) > 500 else result)
        print("-" * 30)
        print(f"📊 Total length: {len(result)} chars, ~{len(result) // 4} tokens")
        
    except Exception as e:
        print(f"❌ Long conversation preprocessing failed: {e}")
        import traceback
        traceback.print_exc()


async def test_classification_accuracy():
    """Test message classification accuracy"""
    print("\n🧪 Testing Message Classification...")
    
    preprocessor = ContextPreprocessor()
    
    test_cases = [
        ("How do I send an email?", "Should be classified as information_seeking"),
        ("Send an email to John about the meeting", "Should be classified as task_execution"),
        ("What did you mean by that?", "Should be classified as clarification"),
        ("Let me clarify my previous request", "Should be classified as clarification"),
        ("Continue with the analysis", "Should be classified as continuation"),
    ]
    
    for content, expected in test_cases:
        try:
            # Test entity extraction
            entities = preprocessor._extract_entities(content)
            topics = preprocessor._extract_topics(content)
            classification = preprocessor._classify_message_type(content, True, topics)
            
            print(f"📝 '{content}'")
            print(f"   🏷️ Classification: {classification.value}")
            print(f"   🎯 Topics: {topics}")
            print(f"   👤 Entities: {entities}")
            print(f"   ✅ {expected}")
            print()
            
        except Exception as e:
            print(f"❌ Classification failed for '{content}': {e}")


async def run_all_tests():
    """Run all test scenarios"""
    print("🚀 Starting Context Preprocessor Tests")
    print("=" * 60)
    
    await test_basic_preprocessing()
    await test_empty_conversation()
    await test_long_conversation()
    await test_classification_accuracy()
    
    print("\n" + "=" * 60)
    print("🎉 All tests completed!")


if __name__ == "__main__":
    # Run the tests
    try:
        asyncio.run(run_all_tests())
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n💥 Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()