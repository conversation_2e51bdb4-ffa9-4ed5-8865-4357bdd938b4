"""
Simple validation script for Context Preprocessor logic

This script validates the core logic components of the context preprocessor
without requiring all external dependencies to be installed.
"""

import re
from datetime import datetime
from typing import List, Dict, Any
from enum import Enum


class MessageClassification(Enum):
    """Classification types for conversation messages."""
    META_CONVERSATIONAL = "meta_conversational"
    CONTINUATION = "continuation" 
    NEW_TOPIC = "new_topic"
    CLARIFICATION = "clarification"
    TASK_EXECUTION = "task_execution"
    INFORMATION_SEEKING = "information_seeking"


class UserIntentType(Enum):
    """User intent classification for supervisor routing."""
    INFORMATION_SEEKING = "information_seeking"
    TASK_EXECUTION = "task_execution"
    CLARIFICATION = "clarification"
    META_DISCUSSION = "meta_discussion"


def test_entity_extraction():
    """Test entity extraction patterns"""
    print("🧪 Testing Entity Extraction...")
    
    # Entity patterns from the preprocessor
    entity_patterns = {
        'person': re.compile(r'\b[A-Z][a-z]+ [A-Z][a-z]+\b'),
        'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
        'date': re.compile(r'\b(?:today|tomorrow|yesterday|\d{1,2}[/-]\d{1,2}[/-]\d{2,4})\b'),
        'project': re.compile(r'\b(?:project|task|assignment)\s+[A-Z][a-zA-Z0-9_-]+\b', re.IGNORECASE),
        'document': re.compile(r'\b[A-Za-z0-9_-]+\.(?:pdf|doc|docx|txt|md)\b')
    }
    
    test_cases = [
        ("Can you email John Smith about the report?", ["person:John Smith"]),
        ("Send <NAME_EMAIL>", ["email:<EMAIL>"]),
        ("Review the document report.pdf today", ["document:report.pdf", "date:today"]),
        ("Project Alpha needs attention", ["project:Project Alpha"]),
        ("Meeting scheduled for 12/25/2024", ["date:12/25/2024"])
    ]
    
    for content, expected_types in test_cases:
        found_entities = []
        for entity_type, pattern in entity_patterns.items():
            matches = pattern.findall(content)
            for match in matches:
                found_entities.append(f"{entity_type}:{match}")
        
        print(f"📝 '{content}'")
        print(f"   Expected: {expected_types}")
        print(f"   Found: {found_entities}")
        print(f"   ✅ Match: {any(exp in found_entities for exp in expected_types)}")
        print()


def test_topic_classification():
    """Test topic classification"""
    print("🧪 Testing Topic Classification...")
    
    topic_keywords = {
        'email': ['email', 'message', 'send', 'reply', 'forward'],
        'calendar': ['meeting', 'appointment', 'schedule', 'calendar', 'agenda'],
        'search': ['find', 'search', 'look', 'retrieve', 'document'],
        'analysis': ['analyze', 'review', 'summarize', 'explain', 'compare'],
        'technical': ['code', 'programming', 'debug', 'error', 'implement'],
        'meta': ['help', 'how', 'what', 'explain', 'clarify']
    }
    
    def extract_topics(content: str) -> List[str]:
        topics = []
        content_lower = content.lower()
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                topics.append(topic)
                
        return topics if topics else ['general']
    
    test_cases = [
        ("Send an email to John", ["email"]),
        ("Schedule a meeting for tomorrow", ["calendar"]),
        ("Find the latest report", ["search"]),
        ("Analyze this document", ["analysis"]),
        ("Debug this code error", ["technical"]),
        ("How do I use this feature?", ["meta"]),
        ("Hello there", ["general"])
    ]
    
    for content, expected in test_cases:
        topics = extract_topics(content)
        print(f"📝 '{content}'")
        print(f"   Expected: {expected}")
        print(f"   Found: {topics}")
        print(f"   ✅ Match: {any(exp in topics for exp in expected)}")
        print()


def test_message_classification():
    """Test message classification logic"""
    print("🧪 Testing Message Classification...")
    
    def classify_message_type(content: str, is_current: bool, topics: List[str]) -> MessageClassification:
        content_lower = content.lower()
        
        if is_current:
            if any(word in content_lower for word in ['what', 'how', 'why', 'when', 'where']):
                return MessageClassification.INFORMATION_SEEKING
            elif any(word in content_lower for word in ['do', 'create', 'send', 'make', 'write']):
                return MessageClassification.TASK_EXECUTION
            else:
                return MessageClassification.CONTINUATION
                
        # For previous messages
        if any(word in content_lower for word in ['clarify', 'explain', 'sorry', 'confused']):
            return MessageClassification.CLARIFICATION
        elif 'meta' in topics:
            return MessageClassification.META_CONVERSATIONAL
        elif len(set(topics) & {'email', 'calendar', 'search', 'analysis'}) > 0:
            return MessageClassification.CONTINUATION
        else:
            return MessageClassification.NEW_TOPIC
    
    test_cases = [
        ("What is the status of project Alpha?", True, ["search"], MessageClassification.INFORMATION_SEEKING),
        ("Send an email to John about the meeting", True, ["email"], MessageClassification.TASK_EXECUTION),
        ("Can you clarify what you meant?", False, ["meta"], MessageClassification.CLARIFICATION),
        ("I need help with this feature", False, ["meta"], MessageClassification.META_CONVERSATIONAL),
        ("Continue with the analysis", True, ["analysis"], MessageClassification.CONTINUATION),
    ]
    
    for content, is_current, topics, expected in test_cases:
        result = classify_message_type(content, is_current, topics)
        print(f"📝 '{content}' (current={is_current})")
        print(f"   Topics: {topics}")
        print(f"   Expected: {expected.value}")
        print(f"   Found: {result.value}")
        print(f"   ✅ Match: {result == expected}")
        print()


def test_context_formatting():
    """Test context formatting logic"""
    print("🧪 Testing Context Formatting...")
    
    def format_preprocessed_context(summary: str, messages: List[Dict], active_context: Dict) -> str:
        output_lines = []
        
        # Summary
        output_lines.append(f"SUMMARY: {summary}")
        output_lines.append("")
        
        # Recent messages
        output_lines.append("RECENT MESSAGES (most recent first):")
        for i, msg in enumerate(messages):
            prefix = "Current:" if i == 0 else f"Previous-{i}:"
            output_lines.append(f'{prefix} "{msg["content"]}" -> {msg["context"]}')
        output_lines.append("")
        
        # Active context
        output_lines.append("ACTIVE CONTEXT:")
        output_lines.append(f"Entities: {', '.join(active_context.get('entities', []))}")
        output_lines.append(f"Topics: {', '.join(active_context.get('topics', []))}")
        output_lines.append(f"Conversation State: {active_context.get('conversation_state', 'Unknown')}")
        output_lines.append(f"User Intent: {active_context.get('user_intent', 'information_seeking')}")
        
        return "\n".join(output_lines)
    
    # Test data
    summary = "User requesting email composition assistance"
    messages = [
        {"content": "Send an email to John about the project", "context": "[task_execution | topics: email]"},
        {"content": "What's the status of project Alpha?", "context": "[information_seeking | topics: search]"}
    ]
    active_context = {
        "entities": ["person:John", "project:Alpha"],
        "topics": ["email", "search"],
        "conversation_state": "Task execution requested on email",
        "user_intent": "task_execution"
    }
    
    formatted = format_preprocessed_context(summary, messages, active_context)
    
    print("📄 Formatted Output:")
    print("-" * 50)
    print(formatted)
    print("-" * 50)
    print(f"📊 Length: {len(formatted)} characters, ~{len(formatted) // 4} tokens")
    print("✅ Formatting successful!")


def run_validation():
    """Run all validation tests"""
    print("🚀 Context Preprocessor Logic Validation")
    print("=" * 60)
    
    test_entity_extraction()
    print()
    test_topic_classification()
    print()
    test_message_classification()
    print()
    test_context_formatting()
    
    print("\n" + "=" * 60)
    print("🎉 All validation tests completed!")
    print("💡 Logic components are working correctly")
    print("🔧 Ready for integration testing with full dependencies")


if __name__ == "__main__":
    run_validation()