"""
Performance tests for RAG system components
"""
import pytest
import asyncio
import time
import psutil
import os
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.performance
@pytest.mark.asyncio
class TestRAGPerformance:
    """Test RAG system performance metrics"""
    
    async def test_query_response_time(self, mock_rag_components):
        """Test RAG query response time performance"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Performance benchmarks
        ACCEPTABLE_RESPONSE_TIME = 3.0  # seconds
        FAST_RESPONSE_TIME = 1.0  # seconds
        
        queries = [
            "What are the capabilities of this system?",
            "How do I schedule a task?",
            "Explain the architecture of the application",
            "What communication channels are supported?",
            "How does the OAuth integration work?"
        ]
        
        response_times = []
        
        for query in queries:
            start_time = time.time()
            
            # Mock the retrieval process
            results = await manager.retrieve_similar(query, top_k=5)
            
            end_time = time.time()
            response_time = end_time - start_time
            response_times.append(response_time)
            
            # Individual query performance check
            assert response_time < ACCEPTABLE_RESPONSE_TIME, f"Query too slow: {response_time:.2f}s for '{query}'"
        
        # Overall performance metrics
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        min_response_time = min(response_times)
        
        print(f"\nRAG Query Performance Results:")
        print(f"Average response time: {avg_response_time:.2f}s")
        print(f"Maximum response time: {max_response_time:.2f}s") 
        print(f"Minimum response time: {min_response_time:.2f}s")
        
        # Performance assertions
        assert avg_response_time < ACCEPTABLE_RESPONSE_TIME, f"Average response time too slow: {avg_response_time:.2f}s"
        assert max_response_time < ACCEPTABLE_RESPONSE_TIME * 2, f"Worst case too slow: {max_response_time:.2f}s"
        
        # Excellent performance check
        fast_queries = sum(1 for t in response_times if t < FAST_RESPONSE_TIME)
        fast_percentage = (fast_queries / len(queries)) * 100
        print(f"Fast queries (< {FAST_RESPONSE_TIME}s): {fast_percentage:.1f}%")
        
        return {
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'min_response_time': min_response_time,
            'fast_percentage': fast_percentage
        }
    
    async def test_embedding_generation_performance(self, mock_rag_components):
        """Test embedding generation performance"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Mock embedding generation with realistic timing
        with patch.object(manager, 'generate_embeddings') as mock_embed:
            async def mock_embedding_generation(text):
                # Simulate realistic embedding generation time
                await asyncio.sleep(0.1 + len(text) * 0.0001)  # Scales with text length
                return [0.1] * 384  # Mock 384-dimensional embedding
            
            mock_embed.side_effect = mock_embedding_generation
            
            # Test different text lengths
            test_texts = [
                "Short text",
                "Medium length text that contains more information and details about various topics",
                "Very long text " * 50,  # ~850 characters
                "Extremely long document content " * 100  # ~3000 characters
            ]
            
            embedding_times = []
            
            for text in test_texts:
                start_time = time.time()
                embeddings = await manager.generate_embeddings(text)
                end_time = time.time()
                
                embedding_time = end_time - start_time
                embedding_times.append(embedding_time)
                
                print(f"Text length: {len(text):4d} chars, Embedding time: {embedding_time:.3f}s")
                
                # Performance assertions
                assert embedding_time < 1.0, f"Embedding generation too slow: {embedding_time:.3f}s for {len(text)} chars"
                assert len(embeddings) == 384, "Incorrect embedding dimension"
            
            # Performance summary
            avg_time = sum(embedding_times) / len(embedding_times)
            print(f"Average embedding generation time: {avg_time:.3f}s")
            
            return {
                'avg_embedding_time': avg_time,
                'embedding_times': embedding_times
            }
    
    async def test_concurrent_query_performance(self, mock_rag_components):
        """Test performance under concurrent query load"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Simulate concurrent users
        CONCURRENT_QUERIES = 10
        MAX_CONCURRENT_TIME = 5.0  # seconds
        
        async def simulate_user_query(user_id: int):
            query = f"User {user_id} query about system capabilities"
            start_time = time.time()
            
            results = await manager.retrieve_similar(query, top_k=3)
            
            end_time = time.time()
            return {
                'user_id': user_id,
                'response_time': end_time - start_time,
                'results_count': len(results)
            }
        
        # Execute concurrent queries
        start_time = time.time()
        
        tasks = [simulate_user_query(i) for i in range(CONCURRENT_QUERIES)]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze results
        response_times = [r['response_time'] for r in results]
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        
        print(f"\nConcurrent Query Performance Results:")
        print(f"Concurrent queries: {CONCURRENT_QUERIES}")
        print(f"Total execution time: {total_time:.2f}s")
        print(f"Average response time: {avg_response_time:.2f}s")
        print(f"Maximum response time: {max_response_time:.2f}s")
        print(f"Queries per second: {CONCURRENT_QUERIES / max(total_time, 0.001):.2f}")
        
        # Performance assertions
        assert total_time < MAX_CONCURRENT_TIME, f"Concurrent execution too slow: {total_time:.2f}s"
        assert all(r['results_count'] > 0 for r in results), "Some queries returned no results"
        
        return {
            'total_time': total_time,
            'avg_response_time': avg_response_time,
            'max_response_time': max_response_time,
            'queries_per_second': CONCURRENT_QUERIES / max(total_time, 0.001)
        }

@pytest.mark.performance
@pytest.mark.asyncio
class TestMemoryPerformance:
    """Test memory usage and resource management"""
    
    async def test_memory_usage_during_rag_operations(self, mock_rag_components):
        """Test memory consumption during RAG operations"""
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Simulate heavy RAG operations
        for i in range(50):
            query = f"Complex query {i} with detailed information requirements"
            await manager.retrieve_similar(query, top_k=10)
            
            # Check memory every 10 operations
            if i % 10 == 0:
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                memory_increase = current_memory - initial_memory
                
                print(f"Operation {i:2d}: Memory usage: {current_memory:.1f} MB (+{memory_increase:.1f} MB)")
                
                # Memory usage assertions
                assert memory_increase < 100, f"Memory usage increased too much: +{memory_increase:.1f} MB"
        
        # Final memory check
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        total_memory_increase = final_memory - initial_memory
        
        print(f"\nMemory Performance Summary:")
        print(f"Initial memory: {initial_memory:.1f} MB")
        print(f"Final memory: {final_memory:.1f} MB")
        print(f"Total increase: {total_memory_increase:.1f} MB")
        
        # Memory leak check
        assert total_memory_increase < 50, f"Potential memory leak: +{total_memory_increase:.1f} MB"
        
        return {
            'initial_memory': initial_memory,
            'final_memory': final_memory,
            'memory_increase': total_memory_increase
        }
    
    async def test_resource_cleanup(self, mock_rag_components):
        """Test proper resource cleanup and garbage collection"""
        import gc
        from managers.manager_retrieval import RetrievalManager
        
        manager = RetrievalManager.get_instance()
        
        # Force garbage collection
        gc.collect()
        initial_objects = len(gc.get_objects())
        
        # Create and process many temporary objects
        for i in range(100):
            query = f"Temporary query {i} for resource testing"
            results = await manager.retrieve_similar(query, top_k=5)
            
            # Simulate object creation and cleanup
            temp_data = {
                'query': query,
                'results': results,
                'metadata': {'iteration': i, 'timestamp': time.time()}
            }
            del temp_data
        
        # Force garbage collection again
        gc.collect()
        final_objects = len(gc.get_objects())
        
        object_increase = final_objects - initial_objects
        
        print(f"\nResource Cleanup Test:")
        print(f"Initial objects: {initial_objects}")
        print(f"Final objects: {final_objects}")
        print(f"Object increase: {object_increase}")
        
        # Resource cleanup assertion
        assert object_increase < 1000, f"Too many objects not cleaned up: +{object_increase}"
        
        return {
            'initial_objects': initial_objects,
            'final_objects': final_objects,
            'object_increase': object_increase
        }