from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from langchain_core.tools import tool

from managers.manager_supervisors import SupervisorManager, SupervisorTask_SingleAgent, SupervisorTaskState, SupervisorTask_Base
from managers.manager_prompts import PromptManager

@tool
def convert_to_dutch(query:str, state: SupervisorTaskState):
    """You are an English to Dutch translator"""
    task = SupervisorManager.get_task("check_language")
    result = task.model.invoke(f"{PromptManager.get_prompt('Task_Language_Verifier_Tool_Dutch')} Message: {query}")
    if isinstance(result, etc.helper_functions.get_any_message_as_type()):
        result = result.content
    return result

async def create_task_language_verifier() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_SingleAgent(name="check_language", tools=[convert_to_dutch], prompt_id="Task_Language_Verifier"))
