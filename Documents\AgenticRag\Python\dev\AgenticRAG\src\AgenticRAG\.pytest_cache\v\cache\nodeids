["tests/integration/test_multimodal_workflow.py::TestMultimodalPerformance::test_chunking_performance", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_asset_management_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_end_to_end_storage_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_error_handling_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_meltano_multimodal_integration", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_multimodal_chunking_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_multimodal_search_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_table_summarization_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_vision_model_integration", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_cleanup_assets", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_encode_image_to_base64", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_encode_image_to_base64_nonexistent", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_extract_table_key_info", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_extract_table_key_info_invalid", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_get_asset_path", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_get_surrounding_context", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_get_surrounding_context_edge_cases", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_infer_column_types", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_is_date", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_is_numeric", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_save_image_asset", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_setup_idempotent", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_setup_initialization", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_singleton_instance_creation", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_singleton_pattern", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_structured_table_to_markdown", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_structured_table_to_markdown_empty", "tests/unit/test_multimodal_manager.py::TestMultimodalManagerIntegration::test_extract_multimodal_elements_integration"]