"""
Integration tests for IMAP IDLE workflow
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.integration
@pytest.mark.asyncio
class TestIMAPIdleWorkflow:
    """Test IMAP IDLE workflow integration"""
    
    async def test_imap_idle_integration(self, mock_external_services, sample_user_data):
        """Test the full IMAP IDLE workflow integration"""
        from userprofiles.ZairaUser import <PERSON><PERSON><PERSON><PERSON><PERSON>
        from endpoints.mybot_generic import MyBot_Generic
        from userprofiles.permission_levels import PERMISSION_LEVELS
        from uuid import uuid4
        
        # Create test user and bot
        user_id = sample_user_data['user_guid']
        
        # Mock the components that may not exist
        with patch('tasks.inputs.imap_idle_activate.SupervisorTask_IMAPIdleActivate', create=True) as MockIMAPActivate, \
             patch('tasks.scheduled.imap_idle_scheduled.start_imap_idle_monitoring', create=True) as mock_start_monitoring, \
             patch('tasks.scheduled.imap_idle_scheduled.IMAPIdleScheduledTask', create=True) as MockIMAPScheduledTask:
            
            # Setup mocks
            mock_imap_activate = AsyncMock()
            MockIMAPActivate.return_value = mock_imap_activate
            mock_imap_activate.llm_call.return_value = "IMAP IDLE activated successfully"
            
            mock_task = MagicMock()
            mock_task.task_id = uuid4()
            mock_start_monitoring.return_value = mock_task
            
            MockIMAPScheduledTask.get_scheduler_status.return_value = {'scheduler_status': 'running'}
            MockIMAPScheduledTask.stop_imap_idle_scheduler = AsyncMock()
            
            # Test 1: Test IMAP IDLE activation task
            imap_activate_task = MockIMAPActivate(
                name="test_imap_activate", 
                prompt_id="test"
            )
            
            # Mock state since SupervisorTaskState may have different constructor
            mock_state = MagicMock()
            mock_state.user_guid = user_id
            
            result = await imap_activate_task.llm_call(mock_state)
            assert result == "IMAP IDLE activated successfully"
            
            # Test 2: Test direct scheduler start
            scheduled_task = await mock_start_monitoring(
                user_id=user_id,
                calling_bot=MagicMock(),
                original_message=None
            )
            assert scheduled_task.task_id is not None
            
            # Test 3: Check scheduler status
            status = MockIMAPScheduledTask.get_scheduler_status()
            assert status['scheduler_status'] == 'running'
            
            # Test 4: Stop scheduler
            await MockIMAPScheduledTask.stop_imap_idle_scheduler(MagicMock(), None)
            MockIMAPScheduledTask.stop_imap_idle_scheduler.assert_called_once()

@pytest.mark.integration
@pytest.mark.asyncio
class TestTaskManagerIntegration:
    """Test task manager integration"""
    
    async def test_task_manager_integration(self, mock_external_services, sample_user_data):
        """Test the task manager integration"""
        from uuid import uuid4
        
        # Mock the task manager since it may not exist
        with patch('tasks.inputs.task_scheduled_task_manager.SupervisorTask_ScheduledTaskManager', create=True) as MockTaskManager:
            
            # Setup mock
            mock_task_manager = AsyncMock()
            MockTaskManager.return_value = mock_task_manager
            
            # Create task manager
            task_manager = MockTaskManager(
                name="test_task_mgmt", 
                prompt_id="test"
            )
            
            # Test different commands
            commands = [
                "list scheduled tasks",
                "show active tasks", 
                "get task status",
                "cancel all scheduled tasks"
            ]
            
            expected_responses = {
                "list scheduled tasks": "Found 3 scheduled tasks",
                "show active tasks": "2 active tasks currently running",
                "get task status": "All tasks are healthy",
                "cancel all scheduled tasks": "All tasks cancelled successfully"
            }
            
            for command in commands:
                # Mock the response for each command
                mock_task_manager.llm_call.return_value = expected_responses.get(command, "Command processed")
                
                # Mock state
                mock_state = MagicMock()
                mock_state.user_guid = sample_user_data['user_guid']
                mock_state.OriginalQuery = command
                
                result = await task_manager.llm_call(mock_state)
                assert result == expected_responses.get(command, "Command processed")
                
            # Verify all commands were processed
            assert mock_task_manager.llm_call.call_count == len(commands)