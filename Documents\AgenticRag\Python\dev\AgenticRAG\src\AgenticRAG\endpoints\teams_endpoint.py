from imports import *

from botbuilder.core import (
    BotFrameworkAdapterSettings,
    TurnContext,
    BotFrameworkAdapter,
    ActivityHandler,
)
from sys import stderr
from botbuilder.schema import Activity, ChannelAccount
from aiohttp import web

from managers.manager_users import ZairaUserManager
from userprofiles.permission_levels import PERMISSION_LEVELS
from endpoints.api_endpoint import APIEndpoint
from endpoints.mybot_generic import MyBot_Generic

# Catch-all for errors.
async def on_turn_error(context: TurnContext, error: Exception):
    from datetime import datetime
    from botbuilder.schema import ActivityTypes

    # This check writes out errors to console log .vs. app insights.
    # NOTE: In production environment, you should consider logging this to Azure
    #       application insights.
    print(f"\n [on_turn_error] unhandled error: {error}", file=stderr)
    etc.helper_functions.exception_triggered(error)

    # Send a message to the user
    await context.send_activity("The bot encountered an error or bug.")
    await context.send_activity(
        "To continue to run this bot, please fix the bot source code."
    )
    # Send a trace activity if we're talking to the Bot Framework Emulator
    if context.activity.channel_id == "emulator":
        # Create a trace activity that contains the error object
        trace_activity = Activity(
            label="TurnError",
            name="on_turn_error Trace",
            timestamp=datetime.utcnow(),
            type=ActivityTypes.trace,
            value=f"{error}",
            value_type="https://www.botframework.com/schemas/error",
        )
        # Send a trace activity, which will be displayed in Bot Framework Emulator
        await context.send_activity(trace_activity)

class MyTeamsBot(ActivityHandler):
    _instance = None
    _initialized = False
    bot_generic: MyBot_Generic = None
    
    TEAMS_SETTINGS: BotFrameworkAdapterSettings = None
    TEAMS_ADAPTER: BotFrameworkAdapter = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls) -> "MyTeamsBot":
        return cls()
    
    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        try:
            instance = cls.get_instance()
            # Create adapter.
            # See https://aka.ms/about-bot-adapter to learn more about how bots work.
            instance.TEAMS_SETTINGS = BotFrameworkAdapterSettings(MICROSOFT_AZURE_BOT_ID, MICROSOFT_AZURE_BOT_SECRET)
            instance.TEAMS_ADAPTER = BotFrameworkAdapter(instance.TEAMS_SETTINGS)
            instance.TEAMS_ADAPTER.on_turn_error = on_turn_error
            instance.bot_generic = MyBot_Generic(instance, "Teams")
            
            APIEndpoint.get_instance().aio_app.add_routes([
                web.post('/teams/api/messages', instance.teams_messages),
            ])

            instance._initialized = True

        except Exception as error:
            etc.helper_functions.exception_triggered(error)

    async def teams_messages(self, request: web.Request) -> web.Response:
        try:
            if request.content_type != 'application/json':
                return web.Response(status=415)

            body = await request.json()
            activity = Activity().deserialize(body)
            auth_header = request.headers.get("Authorization", "")

            if not auth_header:
                return web.Response(status=201)

            response = await self.TEAMS_ADAPTER.process_activity(activity, auth_header, self.on_turn)
            if response:
                return web.json_response(data=response.body, status=response.status)
            # Is called even if the on_turn function already returned a message. Not a problem however
            return web.Response(status=201)

        except Exception as error:
            etc.helper_functions.exception_triggered(error)
            return web.Response(status=201)

    # See https://aka.ms/about-bot-activity-message to learn more about the message and other activity types.
    async def on_message_activity(self, turn_context: TurnContext):
        user_input = turn_context.activity.text
        username = turn_context.activity.caller_id
        user = await ZairaUserManager.find_user(username)
        # Temporarily create a user if none exists
        if user == None:
            user = await ZairaUserManager.add_user(username, PERMISSION_LEVELS.USER, ZairaUserManager.create_guid(), ZairaUserManager.create_guid())
        await user.on_message(complete_message=user_input, calling_bot=self.bot_generic, attachments=[], original_message=turn_context)

    async def on_members_added_activity(
        self, members_added: ChannelAccount, turn_context: TurnContext
    ):
        for member_added in members_added:
            if member_added.id != turn_context.activity.recipient.id:
                await self.bot_generic.on_member_join(member_name=member_added.name, message=turn_context)

    async def on_members_removed_activity(
        self, members_removed: ChannelAccount, turn_context: TurnContext
    ):
        for member_removed in members_removed:
            if member_removed.id != turn_context.activity.recipient.id:
                print(f"Teams member removed: {member_removed.name}")

    @classmethod
    async def send_teams_broadcast(cls, response: str):
        if not response:
            print('Teams broadcast message was empty.')
            return
        
        # TODO: Implement Teams broadcast functionality
        print(f"Teams broadcast not implemented yet: {response}")

    @classmethod
    async def send_a_teams_message(cls, turn_context: TurnContext, response: str) -> None:
        if not response:
            print('Teams message was empty.')
            return

        try:
            await turn_context.send_activity(response)
        except Exception as e:
            print(f"Error sending Teams message: {e}")

    @classmethod
    async def on_ready(cls):
        instance = cls.get_instance()
        print("Teams bot is ready!")
        await instance.bot_generic.on_ready()
