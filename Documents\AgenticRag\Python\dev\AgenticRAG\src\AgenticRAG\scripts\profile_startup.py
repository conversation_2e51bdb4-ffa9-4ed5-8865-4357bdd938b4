#!/usr/bin/env python3
"""
Quick startup profiler to identify bottlenecks
"""
import time
import sys
from pathlib import Path

def profile_import(module_name):
    """Profile the time it takes to import a module"""
    start = time.time()
    try:
        __import__(module_name)
        elapsed = time.time() - start
        print(f"OK {module_name}: {elapsed:.3f}s")
        return elapsed
    except Exception as e:
        elapsed = time.time() - start
        print(f"FAIL {module_name}: {elapsed:.3f}s (FAILED: {e})")
        return elapsed

def main():
    print("Profiling AgenticRAG startup bottlenecks...")
    print("=" * 50)
    
    total_start = time.time()
    
    # Profile key imports
    modules_to_test = [
        "imports",  # The centralized imports
        "globals", 
        "config",
        "etc.ZairaSettings",
        "managers.manager_logfire",
        "etc.setup",
        "main_loop",
        "managers.manager_supervisors",
        "managers.manager_postgreSQL", 
        "managers.manager_qdrant",
        "managers.manager_retrieval",
        "llama_index.core",
        "langchain_core",
        "langgraph",
        "fastembed",
        "endpoints.discord_endpoint",
        "endpoints.teams_endpoint",
        "endpoints.slack_endpoint",
        "endpoints.whatsapp_endpoint"
    ]
    
    times = {}
    for module in modules_to_test:
        times[module] = profile_import(module)
    
    total_elapsed = time.time() - total_start
    
    print("\n" + "=" * 50)
    print(f"Total profiling time: {total_elapsed:.3f}s")
    print("\nSlowest imports:")
    
    # Sort by time and show top 10
    sorted_times = sorted(times.items(), key=lambda x: x[1], reverse=True)
    for module, elapsed in sorted_times[:10]:
        print(f"  {elapsed:.3f}s - {module}")
    
    print("\nRecommendations:")
    for module, elapsed in sorted_times[:5]:
        if elapsed > 1.0:
            print(f"- Consider lazy loading {module} (takes {elapsed:.3f}s)")
    
if __name__ == "__main__":
    main()