"""
Basic multimodal RAG functionality test
Tests core components without requiring full system setup
"""

import asyncio
import tempfile
from pathlib import Path

async def test_multimodal_manager():
    """Test basic MultimodalManager functionality"""
    print("=== Testing MultimodalManager ===")
    
    # Create a simple test file
    test_content = """
# Test Document

This is a test document with some content.

## Sales Data

Product A: $50,000
Product B: $75,000
Product C: $40,000

The data shows varying performance across products.
"""
    
    # Create temporary file
    with tempfile.NamedTemporaryFile(mode='w', suffix='.md', delete=False) as temp_file:
        temp_file.write(test_content)
        temp_file_path = temp_file.name
    
    try:
        from managers.manager_multimodal import MultimodalManager
        
        # Setup manager
        await MultimodalManager.setup()
        print("MultimodalManager setup completed")
        
        # Test extraction
        result = await MultimodalManager.extract_multimodal_elements(temp_file_path, "test_doc")
        
        print(f"Extraction results:")
        print(f"  - Total elements: {len(result.get('all_elements', []))}")
        print(f"  - Text elements: {len(result.get('text_elements', []))}")
        print(f"  - Tables: {len(result.get('tables', []))}")
        print(f"  - Images: {len(result.get('images', []))}")
        
        if result.get('error'):
            print(f"  - Error: {result['error']}")
        
        return True
        
    except Exception as e:
        print(f"Error testing MultimodalManager: {e}")
        return False
    finally:
        import os
        try:
            os.unlink(temp_file_path)
        except:
            pass

async def test_retrieval_chunking():
    """Test multimodal chunking in RetrievalManager"""
    print("\n=== Testing Multimodal Chunking ===")
    
    try:
        from managers.manager_retrieval import RetrievalManager
        
        # Setup manager
        await RetrievalManager.setup()
        print("RetrievalManager setup completed")
        
        # Test data
        mock_multimodal_data = {
            "doc_id": "test_001",
            "all_elements": [
                {
                    "type": "Title",
                    "text": "Test Document",
                    "element_index": 0
                },
                {
                    "type": "NarrativeText", 
                    "text": "This is some text content that precedes visual elements.",
                    "element_index": 1
                },
                {
                    "type": "Image",
                    "text": "Image placeholder",
                    "summary": "Chart showing quarterly sales data",
                    "element_index": 2
                },
                {
                    "type": "Table",
                    "text": "Table content",
                    "summary": "Sales performance table",
                    "markdown": "| Product | Q1 | Q2 |\n|---------|----|----|Product A|$50K|$65K|",
                    "element_index": 3
                }
            ]
        }
        
        chunks = await RetrievalManager.chunk_multimodal_elements(mock_multimodal_data)
        
        print(f"Generated {len(chunks)} chunks")
        for i, chunk in enumerate(chunks):
            print(f"  Chunk {i+1}: {len(chunk)} chars")
            print(f"    Contains IMAGE: {'[IMAGE:' in chunk}")
            print(f"    Contains TABLE: {'[TABLE:' in chunk}")
        
        return True
        
    except Exception as e:
        print(f"Error testing multimodal chunking: {e}")
        return False

async def test_basic_functionality():
    """Test basic multimodal functionality without full system"""
    print("Testing Basic Multimodal RAG Functionality")
    print("=" * 50)
    
    results = []
    
    # Test 1: MultimodalManager
    result1 = await test_multimodal_manager()
    results.append(("MultimodalManager", result1))
    
    # Test 2: Chunking
    result2 = await test_retrieval_chunking()
    results.append(("Multimodal Chunking", result2))
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    for test_name, success in results:
        status = "[OK]" if success else "[FAIL]"
        print(f"  {status} {test_name}")
    
    all_passed = all(result for _, result in results)
    overall_status = "[OK]" if all_passed else "[FAIL]"
    print(f"\nOverall Status: {overall_status}")
    
    return all_passed

if __name__ == "__main__":
    asyncio.run(test_basic_functionality())