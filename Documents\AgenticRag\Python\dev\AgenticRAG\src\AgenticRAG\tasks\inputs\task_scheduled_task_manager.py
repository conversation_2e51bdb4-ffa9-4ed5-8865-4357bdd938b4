from imports import *

from os import getcwd
from managers.manager_supervisors import <PERSON><PERSON><PERSON><PERSON><PERSON>, SupervisorSupervisor, SupervisorTask_Base, SupervisorTask_SingleAgent, SupervisorTaskState, SupervisorSection
from managers.manager_users import ZairaUserManager
from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
from langchain_core.tools import BaseTool
from langchain_core.language_models.base import BaseLanguageModel
from langchain_core.messages import HumanMessage
from endpoints.mybot_generic import MyBot_Generic
from typing import List, Dict, Any, Sequence, Union, Callable, Optional
from pydantic import BaseModel, Field
import asyncio


class CreateScheduledTaskTool(BaseTool):
    """Tool for creating new scheduled tasks"""
    name: str = "create_scheduled_task"
    description: str = "Create a new scheduled task based on natural language prompt"
    
    def _run(self, schedule_prompt: str, user_guid: str, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, schedule_prompt: str, user_guid: str, state: SupervisorTaskState = None) -> str:
        """Create a new scheduled task"""
        try:
            from userprofiles.ScheduledZairaTask import ScheduledZairaTask
            from managers.manager_users import ZairaUserManager
            
            user = await ZairaUserManager.find_user(user_guid)
            if not user:
                return f"User {user_guid} not found"
                
            # Create bot instance for the task
            bot = MyBot_Generic(None, "scheduled_task_manager")
            
            # Create the scheduled task - it will automatically parse the prompt
            # and determine the appropriate action (email checking, reminders, etc.)
            task = ScheduledZairaTask(user, bot, None, schedule_prompt)
            
            # Start the task
            asyncio.create_task(task.run_task())
            
            return f"Created scheduled task: {task.task_id} - {schedule_prompt}"
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Failed to create scheduled task", user if 'user' in locals() else None)
            return f"Failed to create scheduled task: {str(e)}"


class ListScheduledTasksTool(BaseTool):
    """Tool for listing scheduled tasks"""
    name: str = "list_scheduled_tasks"
    description: str = "List all active scheduled tasks for a user"
    
    def _run(self, user_guid: str, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, user_guid: str, state: SupervisorTaskState = None) -> str:
        """List all active scheduled tasks"""
        try:
            persistence_manager = ScheduledTaskPersistenceManager.get_instance()
            tasks = await persistence_manager.get_active_tasks(user_guid)
            
            if not tasks:
                return "No active scheduled tasks found"
                
            task_list = []
            for task in tasks:
                task_info = (
                    f"Task ID: {task['task_id']}\n"
                    f"Schedule: {task['schedule_prompt']}\n"
                    f"Target: {task['target_prompt']}\n"
                    f"Next execution: {task['next_execution']}\n"
                    f"Created: {task['created_at']}\n"
                    "---"
                )
                task_list.append(task_info)
                
            return f"Active scheduled tasks:\n\n" + "\n".join(task_list)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Failed to list scheduled tasks", None)
            return f"Failed to list scheduled tasks: {str(e)}"


class CancelScheduledTaskTool(BaseTool):
    """Tool for canceling scheduled tasks"""
    name: str = "cancel_scheduled_task"
    description: str = "Cancel a scheduled task by ID"
    
    def _run(self, task_id: str, reason: str = "User requested cancellation", state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, task_id: str, reason: str = "User requested cancellation", state: SupervisorTaskState = None) -> str:
        """Cancel a scheduled task"""
        try:
            persistence_manager = ScheduledTaskPersistenceManager.get_instance()
            success = await persistence_manager.cancel_task(task_id, reason)
            
            if success:
                return f"Successfully cancelled task {task_id}"
            else:
                return f"Failed to cancel task {task_id}"
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Failed to cancel scheduled task", None)
            return f"Failed to cancel task: {str(e)}"


class ScheduledTaskCreateTask(SupervisorTask_SingleAgent):
    """Task for creating new scheduled tasks"""
    
    def __init__(self, model: BaseLanguageModel = None):
        tools = [CreateScheduledTaskTool()]
        super().__init__(
            name="create_scheduled_task",
            prompt="You are specialized in creating scheduled tasks. Parse the user's natural language request and create the appropriate scheduled task using the create_scheduled_task tool.",
            tools=tools,
            model=model
        )


class ScheduledTaskListTask(SupervisorTask_SingleAgent):
    """Task for listing scheduled tasks"""
    
    def __init__(self, model: BaseLanguageModel = None):
        tools = [ListScheduledTasksTool()]
        super().__init__(
            name="list_scheduled_tasks",
            prompt="You are specialized in listing scheduled tasks. Use the list_scheduled_tasks tool to show all active scheduled tasks for the user.",
            tools=tools,
            model=model
        )


class ScheduledTaskCancelTask(SupervisorTask_SingleAgent):
    """Task for canceling scheduled tasks"""
    
    def __init__(self, model: BaseLanguageModel = None):
        tools = [CancelScheduledTaskTool()]
        super().__init__(
            name="cancel_scheduled_task",
            prompt="You are specialized in canceling scheduled tasks. Use the cancel_scheduled_task tool to cancel tasks by ID.",
            tools=tools,
            model=model
        )


class ScheduledTaskInfoTask(SupervisorTask_Base):
    """Task for providing information about scheduled tasks"""
    
    def __init__(self, model: BaseLanguageModel = None):
        super().__init__(
            name="scheduled_task_info",
            prompt="You are specialized in providing information about scheduled tasks. Explain how scheduled tasks work and what operations are available.",
            model=model
        )
    
    async def llm_call(self, state: SupervisorTaskState) -> str:
        """Provide information about scheduled tasks"""
        info = """
        Scheduled Tasks Information:
        
        I can help you manage scheduled tasks with the following operations:
        
        1. **Create Tasks**: Schedule tasks to run at specific times or intervals
           - Example: "Send me a reminder in 30 minutes"
           - Example: "Check my emails every hour"
           
        2. **List Tasks**: Show all your active scheduled tasks
           - Example: "Show me my scheduled tasks"
           - Example: "List all my recurring tasks"
           
        3. **Cancel Tasks**: Remove scheduled tasks you no longer need
           - Example: "Cancel task [task_id]"
           - Example: "Stop my hourly email check"
           
        4. **Task Types**:
           - **One-time**: Run once at a specific time
           - **Recurring**: Run repeatedly at intervals
           
        The system supports natural language scheduling like:
        - "Remind me to call John in 2 hours"
        - "Send me a daily report at 9am"
        - "Check for new emails every 15 minutes"
        
        What would you like to do with scheduled tasks?
        """
        return info


class SupervisorTask_ScheduledTaskManager(SupervisorSupervisor):
    """
    SupervisorSupervisor for managing all scheduled task operations.
    Routes requests to appropriate task handlers based on natural language intent.
    """
    
    def __init__(self, model: BaseLanguageModel = None):
        super().__init__(
            name="scheduled_task_manager",
            #prompt_id="Task_Scheduled_Task_Manager",
            prompt="""You are the Scheduled Task Manager supervisor. Route requests to the appropriate task based on user intent:

- For creating/scheduling new tasks: route to create_scheduled_task
- For listing/showing existing tasks: route to list_scheduled_tasks  
- For canceling/removing tasks: route to cancel_scheduled_task
- For general information about scheduled tasks: route to scheduled_task_info

Analyze the user's request and determine the most appropriate action.""",
            model=model
        )
        
        # Add all task types
        self.add_task(ScheduledTaskCreateTask(model))
        self.add_task(ScheduledTaskListTask(model))
        self.add_task(ScheduledTaskCancelTask(model))
        self.add_task(ScheduledTaskInfoTask(model))
    
    async def llm_call(self, state: SupervisorTaskState):
        """Execute scheduled task management operations using the supervisor"""
        user = await ZairaUserManager.find_user(state.user_guid)
        
        try:
            # Compile if not already compiled
            if not self.compiled_langgraph:
                self.compile()
            
            # Execute the supervisor
            result = await self.compiled_langgraph.ainvoke(state)
            
            # Extract response
            response = result.get("messages", [])
            if response:
                last_message = response[-1]
                if hasattr(last_message, 'content'):
                    LogFire.log("SCHEDULED_TASKS", f"ScheduledTaskManager handled request: {state.original_input[:50]}...", user)
                    return last_message.content
            
            LogFire.log("ERROR", "ScheduledTaskManager generated no response", user)
            return "No response generated"
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Scheduled task management failed", user)
            return f"Failed to manage scheduled tasks: {str(e)}"
    

async def create_task_scheduled_task_manager() -> SupervisorTask_Base:
    """Create and register the scheduled task manager task"""
    return SupervisorManager.register_task(
        SupervisorTask_ScheduledTaskManager()
    )