"""
Unit tests for scheduled task persistence functionality
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.unit
@pytest.mark.asyncio
async def test_scheduled_task_persistence():
    """Test the persistence functionality of ScheduledZairaTask"""
    from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
    
    manager = ScheduledTaskPersistenceManager.get_instance()
    
    # Mock task data
    task_data = {
        'task_id': 'test-task-123',
        'user_id': 'test-user-123', 
        'schedule_prompt': 'test task every 5 minutes',
        'target_prompt': 'Test target',
        'is_active': True,
        'created_at': '2024-01-01T00:00:00Z'
    }
    
    # Test save task
    with patch.object(manager, 'save_task') as mock_save:
        mock_save.return_value = True
        
        # Create mock task object
        mock_task = MagicMock()
        mock_task.task_id = 'test-task-123'
        
        result = await manager.save_task(mock_task)
        assert result is True
        mock_save.assert_called_once()
    
    # Test get active tasks
    with patch.object(manager, 'get_active_tasks') as mock_get:
        mock_get.return_value = [task_data]
        
        tasks = await manager.get_active_tasks("test-user-123")
        assert len(tasks) == 1
        assert tasks[0]['task_id'] == 'test-task-123'
    
    # Test cancel task
    with patch.object(manager, 'cancel_task') as mock_cancel:
        mock_cancel.return_value = True
        
        result = await manager.cancel_task("test-task-123", "Test cancellation")
        assert result is True
        mock_cancel.assert_called_once()

@pytest.mark.unit
@pytest.mark.asyncio 
async def test_task_management_commands():
    """Test the task management commands"""
    from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
    
    manager = ScheduledTaskPersistenceManager.get_instance()
    
    # Mock task data for list command
    mock_tasks = [
        {
            'task_id': 'task-1',
            'user_id': 'user-123',
            'schedule_prompt': 'check email every 30 minutes',
            'is_active': True
        },
        {
            'task_id': 'task-2', 
            'user_id': 'user-123',
            'schedule_prompt': 'backup data daily',
            'is_active': True
        }
    ]
    
    # Test listing active tasks
    with patch.object(manager, 'get_active_tasks') as mock_get:
        mock_get.return_value = mock_tasks
        
        result = await manager.get_active_tasks("user-123")
        assert len(result) == 2
        assert result[0]['schedule_prompt'] == 'check email every 30 minutes'
        assert result[1]['schedule_prompt'] == 'backup data daily'
    
    # Test load task
    with patch.object(manager, 'load_task') as mock_load:
        mock_load.return_value = mock_tasks[0]
        
        result = await manager.load_task("task-1")
        assert result['task_id'] == 'task-1'
        assert result['schedule_prompt'] == 'check email every 30 minutes'