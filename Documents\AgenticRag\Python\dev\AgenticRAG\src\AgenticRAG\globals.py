# imports.py

# ------------------------------------------------
# -             Configuration globals            -
# ------------------------------------------------
EMBEDDING_MODEL = "BAAI/bge-small-en-v1.5"
LLM_MODEL = "dolphin-llama3"
AGENT_MODEL_OLLAMA = "llama3.1:8b"

CHUNK_SIZE = 4000
CHUNK_OVERLAP = 800
EMBEDDING_SIZE = 384
TIMEOUT_LIMIT = 360 # seconds

#USED PORTS
IP_PYTHON = ["0.0.0.0"] # localhost #Server is hosted behind IIS + Cloudflare, so only allow local connections
IP_PYTHON_PUBLIC = "proxyhttpaio.askzaira.com"
ZAIRA_PYTHON_PORT = 41000
#PORT_AIRBYTE = 41001
PORT_QDRANT = 6333
PORT_OLLAMA = 11434
PORT_POSTGRESQL = 5433
#USED PORTS

# ------------------------------------------------
# -            Standard library globals          -
# ------------------------------------------------
from pathlib import Path
from typing import TYPE_CHECKING

# ------------------------------------------------
# -             Third-party globals              -
# ------------------------------------------------

# ------------------------------------------------
# - Local application / library specific globals -
# ------------------------------------------------

# ------------------------------------------------
# -     Common functions or configurations       -
# ------------------------------------------------
def BASE_DIR():
    saved_value = None
    first_run = True
    
    def save_variable_once(value):
        nonlocal saved_value, first_run
        if first_run:
            saved_value = value
            first_run = False
        return saved_value
    
    return save_variable_once(Path(__file__).parent.parent.parent) #Parent once for file -> folder, then twice over to move out of src/AgenticRAG)

class Globals:
    Index = None
    Storage = None
    DebugValues = False

    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from llama_index.core import VectorStoreIndex

    @staticmethod
    def set_index(Index) -> "VectorStoreIndex":
        Globals.Index = Index

    @staticmethod
    def get_index() -> "VectorStoreIndex":
        return Globals.Index

    @staticmethod
    def get_query_engine_default() -> "VectorStoreIndex":
        from llama_index.core.vector_stores import MetadataFilters
        filters = MetadataFilters(filters=[])
        return Globals.get_index().as_query_engine(
            filters=filters,
            response_mode="tree_summarize",
            similarity_top_k=5,
        )
    
    @staticmethod
    def set_debug(Debug):
        Globals.Debug = Debug

    @staticmethod
    def is_debug() -> bool:
        return Globals.Debug
    
    @staticmethod
    def set_debug_values(DebugValues) -> bool:
        Globals.DebugValues = DebugValues
    
    @staticmethod
    def is_debug_values() -> bool:
        return Globals.DebugValues
    
    @staticmethod
    def get_endpoint_address() -> str:
        if Globals.is_docker() == True:
            from etc.helper_functions import get_value_from_env
            host = get_value_from_env("ZAIRA_NETWORK_NAME", None)
            port = get_value_from_env("ZAIRA_PYTHON_PORT", ZAIRA_PYTHON_PORT)
            if not host: # No .env file found
                uri = f"https://{IP_PYTHON_PUBLIC}"
            elif host == "localhost":
                uri = f"http://{host}:{port}"
            else:
                uri = f"https://{host}.askzaira.com"
            return uri
        else:
            if Globals.is_debug() == True:
                return f"http://localhost:{ZAIRA_PYTHON_PORT}"
            else:
                return f"https://{IP_PYTHON_PUBLIC}"

    @staticmethod
    def is_docker() -> bool:
        from os import path as os_path
        cgroup_path = '/proc/1/cgroup'
        if os_path.exists(cgroup_path):
            with open(cgroup_path, 'r') as f:
                return 'docker' in f.read() or 'containerd' in f.read()
        return False
