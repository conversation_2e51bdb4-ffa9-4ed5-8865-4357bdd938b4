from imports import *

from langchain_core.tools import tool
import logging
import smtplib
import base64
from typing import Optional

from managers.manager_supervisors import Super<PERSON><PERSON><PERSON><PERSON>, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaTask import Long<PERSON><PERSON>ningZairaTask


@tool 


async def create_supervisor_document_analyzer() -> SupervisorSupervisor:
    class TaskCreator:
        document_analyzer_task: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.document_analyzer_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(name="document_analyzer", tools=[], prompt=
                "You're responsible analyzing documents. Call your tool with the parameters generated in the following way:"
                " Generate a content and subject based on the original user input and pass those as parameters."
                " Then, if you're unsure about the sender or recipient parameter it's important to leave those parameters blank. The tool will recognise the missing values and trigger Human-In-The-Loop."))
        
        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(SupervisorSupervisor(name="document_analyzer_supervisor", prompt=
                "................................................")) \
                .add_task(self.document_analyzer_task) \
                .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()