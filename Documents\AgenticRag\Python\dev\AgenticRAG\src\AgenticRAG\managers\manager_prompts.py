from imports import *

class PromptManager:
    _instance = None
    _initialized = False

    token_string_values = ["access_token", "refresh_token", "token_type", "str1", "str2", "str3", "str4", "str5", "str6", "str7"]
    token_int_values = ["expires_in", "refresh_token_expires_in", "int1", "int2", "int3", "int4", "int5", "int6", "int7", "int8"]

    prompts: dict = {
        "AskZaira_Prompt":  "You are Zaira, a highly intelligent system that reads data from thousands of sources,"
                            " most of which are related to the company that you work for. Since you are an incredibly loyal employee,"
                            " the answers you write are always directed in a professional manner and always good enough that when you"
                            " help out another colleague that your answers are written in such a way that your answers can be sent"
                            " directy to management and that the colleague who asked you for help will be given all the credit.",
        "Quick_RAG_Search": "Only return the original input question, nothing else.",
        "Quick_LLM_Search": """.Only return the original input question, nothing else.
                                            """,
        "Global_Supervisor_Prompt":  "You are a supervisor managing which task needs to be called next. Your tasks are sorted in order of relevance and importance." \
                                                " If a task needs to be called, respond with its task name." \
                                                " Always try to keep the amount of total tasks to a minimum." \
                                                f" If none of the tasks are likely to be of any help, respond with END instead." \
                                                " The following tasks have already been called previously, and cannot under any circumstances be called again (previous_tasks): ",
        "Top_Supervisor_Prompt":    "You are a supervisor tasked with managing a conversation between your tasks."
                                    " Given the following original_input, determine if a task needs to be called next."
                                    f" If top_output_supervisor is not yet in your called tasks, the top_output_supervisor task will be of help for at least a little bit."
                                    " For retrieving information, use search_supervisor. "
                                    " If the user wants to sent an email, use email_supervisor." \
                                    " If the user wants to scan and analyze receipts, use the receipts scanner task." \
                                    " If the user wants to manage their calendar or plan a meeting/agenda point, use agenda_supervisor." \
                                    " If the user wants to start IMAP IDLE monitoring or email monitoring, use imap_idle_activate_task." \
                                    " If the user wants to manage, cancel, or check scheduled tasks, use scheduled_task_manager." \
                                    ,
        "Output_Supervisor_Prompt":  "You are responsible for handling the last step of the control system.",
        "Output_Supervisor_Processing": "You are an output supervisor processing a message that originated from a single Source. "
                 "Based on the 'original input', determine if the 'Output message' needs any additional changes. "
                 "Most importantly, ensure the output is in the same language as the original input. "
                 "Use the language_verifier task to verify and translate if needed. "
                 "Process the following output message.",
        "Output_Supervisor_Sender": "If it's unlikely that any of your remaining tasks are related to the HumanMessage, respond with END instead of a task name.",
        "Supervisor_Retrieval": "You are a supervisor managing search experts. First use rag_search tool (priority 1). After RAG search, carefully evaluate: Does the RAG result fully answer the original " \
                "question? If RAG says 'no information found', gives incomplete answers, or the question needs current/external information not in company data, then use web_search_tool. If RAG fully answers the" \
                "question with company data, respond with END." \
                "The following team works under you as supervisor:" \
                "a rag search tool number 1 priority always try to use this tool first. a web search tool. a file path retriever. When someone is asking for the file location/file path use a file path retriever",
        "Task_Retrieval_Web_Search": "You are a web search tool. Your job is to search the web for information and provide the most relevant results.",
        "Task_Retrieval_RAG": "You are the most important search engine available. You are a tool with access to the company knowledge base. "
                                     "Call the rag_search_tool ONCE with the exact original user query - do not translate, interpret, or modify it. "
                                     "Use the exact text provided by the user, preserving the original language and wording. "
                                     "Process the entire request in a single search to get comprehensive results.",
        "Task_Retrieval_FilePath": "You are a file path retriever. Your job is to extract and return ONLY the file paths from documents when a user asks for a document. Do not provide any additional information, just the file paths.",
        "Task_Retrieval_Multimodal": "You are a multimodal search expert. Use the multimodal_search_tool to search for content that includes images, tables, or mixed media. Specify the content_type parameter as 'image', 'table', 'both', or leave empty for all types.",
        "Task_Retrieval_Images": "You are an image search specialist. Use the image_focused_search_tool to search specifically for documents containing images and their descriptions. Focus on visual content like charts, diagrams, photos, and illustrations.",
        "Task_Retrieval_Tables": "You are a table search specialist. Use the table_focused_search_tool to search specifically for documents containing tables and structured data. Focus on finding tabular information, spreadsheets, and data tables.",
        "Task_Retrieval_Assets": "You are a document asset specialist. Use the get_document_assets_tool to retrieve all assets (images, tables, text chunks) for a specific document when provided with a document ID.",
        "Task_GDrive": "You are a task responsible for retrieving new files through Google Drive. Only execute when called for as it's a slow task.",
        "Task_IMAP": "You are a task responsible for retrieving new emails through imap. Only execute when called for as it's a slow task.",
        "Task_IMAP_Email_Check": "You are a task responsible for retrieving new emails through imap. Only execute when called for as it's a slow task.",
        #"Task_Language_Verifier": "If the original_input requests the output to be in a specific language, use your tools to determine the result.",
        #"Task_Language_Verifier_Tool_Dutch": "Verify the message is in Dutch. If it is, return the input. If it's not, translate into Dutch.",
        "Task_EmailWriter_Supervisor": "You are supervising an email writing specialist. When asked to create an email, "
                "ensure it's professional, concise, and appropriate for the intended recipient. "
                "The email should clearly communicate the necessary information while maintaining "
                "a professional tone.",
        "Task_EmailWriter_Agent": "You're responsible for sending mails and emails. Call your email_tool with the parameters generated in the following way:"
                " Generate a content and subject based on the original user input and pass those as parameters."
                " Then, if you're unsure about the sender or recipient parameter it's important to leave those parameters blank. The tool will recognise the missing values and trigger Human-In-The-Loop.",
        #"Task_Language_Detector": "You are a language detection expert. Your task is to identify the language of the provided text. Return only the language name.",
        #"Task_Language_Detector_Tool_Detect_Language": "Detect the language of the following text and return only the language name (e.g., 'English', 'Dutch', 'Spanish', etc.)",
        "Task_Receipts_Scanner_Supervisor": "You are a receipts expert supervisor. Your job is to help users extract and analyze information from receipts. You can scan receipts from file paths or images, and then analyze the extracted text to identify key information like store name, date, total amount, items purchased, etc.",
        "Task_Receipts_Scanner_Agent_Scanner": "You are a receipt scanning expert. You have tools to scan receipts from file paths or base64-encoded images. Use these tools to help users extract and understand information from their receipts.",
        "Task_Receipts_Scanner_Agent_Analyzer": "You are a receipt scanning and analysis expert. You have tools toand to analyze the extracted text. Use these tools to help users extract and understand information from their receipts.",
        "Task_Change_Chat_Session": "You are responsible for switching to new and old chat sessions. Only execute when called for as it modifies the internal working.",
        "Task_IMAP_Idle_Activate": "You are responsible for activating IMAP IDLE monitoring that checks for new emails every 30 minutes. Only execute when the user specifically requests to start IMAP IDLE monitoring or email monitoring.",
        "Task_Scheduled_Task_Manager": "You are responsible for managing scheduled tasks - listing active tasks, cancelling tasks, and providing status information. Execute when users ask about scheduled tasks, want to cancel tasks, or need to see what's running.",
        "Output_Processing_Language_Verifier": "You are a language verification expert. Your task is to ensure that the output message is in the same language as the original input.",
        "Output_Processing_Language_Verifier_Detect_Input": "Detect the language of the following text and return only the language name (e.g., 'English', 'Dutch', 'Spanish', etc.)",
        "Output_Processing_Language_Verifier_Detect_Output": "Detect the language of the following text and return only the language name (e.g., 'English', 'Dutch', 'Spanish', etc.)",
        "Output_Sender_Discord": "You're a task responsible when the output message needs to be directed to Discord.",
        "Output_Sender_Mail": "You're a task responsible when the output message needs to be sent via (digital) mail or email.",
        "Output_Sender_HTTP": "You're a task responsible when the output message needs to be directed to HTTP.",
        "Output_Sender_Python": "You're a task responsible when the output message needs to be directed to Python.",
        "Output_Sender_Teams": "You're a task responsible when the output message needs to be directed to Teams.",
        "Output_Sender_Whatsapp": "You're a task responsible when the output message needs to be directed to Whatsapp.",
        
        # Chain of Thought Enhanced Prompts
        "Global_Supervisor_CoT_Prompt": "You are a supervisor managing which task needs to be called next. "
                                       "Work through this systematically. Your tasks are sorted in order of relevance and importance. "
                                       "PRIORITY GUIDANCE: Tasks marked [HIGH PRIORITY] should be considered first when appropriate. "
                                       "Tasks marked [FINAL STEP] should be considered when the workflow is nearing completion. "
                                       "However, ALWAYS prioritize relevance to the user's request over task priority markers. "
                                       "If a task needs to be called, respond with its task name. "
                                       "Always try to keep the amount of total tasks to a minimum. "
                                       "If none of the tasks are likely to be of any help, respond with END instead. "
                                       "The following tasks have already been called previously, and cannot under any circumstances be called again (previous_tasks): ",
        
        "Top_Supervisor_CoT_Prompt": "You are a top-level supervisor managing a conversation between tasks. Use chain of thought reasoning:\n\n"
                                    "1. UNDERSTAND: What is the user's original request? Break it into distinct parts.\n"
                                    "2. EVALUATE: What has been accomplished so far by previous tasks?\n"
                                    "3. IDENTIFY: What still needs to be done to fully address the request?\n"
                                    "4. PRIORITIZE: Which task would be most valuable to call next?\n"
                                    "5. DECIDE: Should I call a specific task or END the process?\n\n"
                                    "TASK MAPPING:\n"
                                    "- In all cases, execute quick_rag quick_llm and quick_complexity before any others\n"
                                    "- For retrieving information/data: use search_supervisor\n"
                                    "- For planning meetings/calendar/agenda: use agenda_supervisor\n"
                                    "- For sending emails: use email_supervisor\n"
                                    "- For scanning receipts: use receipts scanner task\n"
                                    "- For starting IMAP IDLE/email monitoring: use imap_idle_activate_task\n"
                                    "- For managing scheduled tasks (cancel, list, status): use scheduled_task_manager\n\n"
                                    "CRITICAL RULES:\n"
                                    "- Each task can only be called ONCE. If a task is already in previous_tasks, do NOT call it again.\n"
                                    "- If user request has multiple parts (e.g., 'get data AND plan meeting'), call tasks in logical sequence.\n"
                                    "- Once the main request is satisfied (e.g., search completed), call top_output_supervisor for final output formatting.\n"
                                    "- Only call END after top_output_supervisor has processed the final output.",
        
        "Supervisor_Retrieval_CoT": "You are a search supervisor managing retrieval experts. Use systematic reasoning:\n\n"
                                   "1. ANALYZE QUERY: What information is the user seeking?\n"
                                   "2. SOURCE PRIORITY: Should I check company data (RAG) first or external sources?\n"
                                   "3. RAG EVALUATION: After RAG search, does it fully answer the question?\n"
                                   "4. GAP ANALYSIS: What information is missing or incomplete?\n"
                                   "5. DECISION: Call next task or END if complete?\n\n"
                                   "WORKFLOW:\n"
                                   "- If no tasks have been called yet: Call rag_task first (priority 1)\n"
                                   "- If rag_task completed and gave good results: Respond with END\n"
                                   "- If rag_task completed but results incomplete/empty: Call web_search_task\n"
                                   "- If user asks for file paths specifically: Call file_path_retriever_task\n"
                                   "- If all relevant tasks completed: Respond with END\n\n"
                                   "CRITICAL: Only call each task once. If a task is already in completed_tasks, do not call it again.",
        
        "Task_EmailWriter_CoT_Prompt": "You are an email writing specialist. Before writing any email, think step-by-step:\n\n"
                                      "1. PURPOSE: What is the main goal of this email?\n"
                                      "2. AUDIENCE: Who is the recipient and what tone is appropriate?\n"
                                      "3. CONTEXT: What key information needs to be included?\n"
                                      "4. STRUCTURE: How should I organize the content for clarity?\n"
                                      "5. REVIEW: Does this achieve the intended purpose professionally?\n\n"
                                      "Work through each step systematically to create a well-structured, professional email.",
    }

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        instance._initialized = True

    @classmethod
    async def setDefaultPrompts(cls):
        instance = cls.get_instance()
        await instance.setDefaultPrompts_MemoryOnly()
        if Globals.is_docker():
            #identifier, key = await instance.get_prompt_identifier_and_key("Global Supervisor Prompt")
            etc.helper_functions.save_to_env(instance.prompts)

    async def setDefaultPrompts_MemoryOnly(self):
        from endpoints.oauth._verifier_ import OAuth2Verifier
        id = 1
        while True:
            identifier = "ZairaPrompts" + str(id)
            if identifier in OAuth2Verifier.apps:
                app = OAuth2Verifier.apps[identifier]
                int_id = 0
                str_id = 0
                for scope in app.scopes:
                    if ":" in scope:
                        split = scope.split(":", 1)
                        is_int = split[0]
                        token_key = self.token_string_values[str_id] if is_int == "str" else self.token_int_values[int_id]
                        actualscope = next((k for k, v in app.meltano_env.items() if v == token_key), None)#app.meltano_env[token_key]#split[1].replace(" ", "_")
                        env_value = etc.helper_functions.get_value_from_env(actualscope, "")
                        if not env_value:
                            # Retrieve the current token, add the value to it, and put it back into the OAuth system
                            if identifier in OAuth2Verifier.bot_tokens:
                                token = OAuth2Verifier.bot_tokens[identifier]
                            else:
                                token = {}
                            token[token_key] = self.prompts[actualscope]
                            OAuth2Verifier.bot_tokens[identifier] = token
                        if is_int == "str" or is_int == "bool":
                            str_id = str_id + 1
                        else:
                            int_id = int_id + 1
            else:
                break
            id = id + 1
        pass
        
    @classmethod
    async def loadDefaultPrompts(cls):
        instance = cls.get_instance()
        await instance.setDefaultPrompts_MemoryOnly()
        #if Globals.is_docker():
        from endpoints.oauth._verifier_ import OAuth2Verifier
        id = 1
        save_value: dict = {}
        token_string_values = ["access_token", "refresh_token", "token_type", "str1", "str2", "str3", "str4", "str5", "str6", "str7"]
        while True:
            identifier = "ZairaPrompts" + str(id)
            if identifier in OAuth2Verifier.apps:
                app = OAuth2Verifier.apps[identifier]
                str_id = 0
                for scope in app.scopes:
                    if ":" in scope:
                        #split = scope.split(":", 1)
                        token_key = token_string_values[str_id]
                        actualscope = next((k for k, v in app.meltano_env.items() if v == token_key), None)#app.meltano_env[token_key]#split[1].replace(" ", "_")
                        env_value = etc.helper_functions.get_value_from_env(actualscope, "")
                        if env_value:
                            OAuth2Verifier.bot_tokens[identifier][token_key] = env_value
                            save_value[actualscope] = env_value
                        else:
                            save_value[actualscope] = instance.prompts[actualscope]
                    str_id = str_id + 1
            else:
                break
            id = id + 1
        #etc.helper_functions.save_to_env(save_value)

    @classmethod
    def get_prompt_identifier_and_key(cls, name: str) -> tuple[str, str]:
        instance = cls.get_instance()
        # from endpoints.oauth._verifier_ import OAuth2Verifier
        # id = 1
        # while True:
        #     identifier = "ZairaPrompts" + str(id)
        #     if identifier in OAuth2Verifier.apps:
        #         app = OAuth2Verifier.apps[identifier]
        #         i = 0
        #         for scope in app.scopes:
        #             if ":" in scope:
        #                 split = scope.split(":", 1)
        #                 is_int = split[0]
        #                 actualscope = split[1]
        #                 if actualscope.lower() == name.lower():
        #                     token_key = instance.token_int_values[i] if is_int else instance.token_string_values[i]
        #                     return identifier, token_key
        #             i = i + 1
        #      else:
        #          break
        #      id = id + 1

    @classmethod
    def get_prompt(cls, name: str):
        if Globals.is_docker():
            #from endpoints.oauth._verifier_ import OAuth2Verifier
            #identifier, token_key = await cls.get_prompt_identifier_and_key(name)
            #token = await OAuth2Verifier.get_token(identifier, token_key)
            from dotenv import load_dotenv
            load_dotenv(encoding='latin-1', override=True)
            token = etc.helper_functions.get_value_from_env(name.replace(" ", "_"))
            if not token:
                raise RuntimeError("Prompt is requested despite not being implemented: " + name)
            return token
        else:
            instance = cls.get_instance()
            from dotenv import load_dotenv
            load_dotenv(encoding='latin-1', override=True)
            token = etc.helper_functions.get_value_from_env(name.replace(" ", "_"))
            if token:
                return token
            if not name in instance.prompts:
                raise RuntimeError("Prompt is requested despite not being implemented: " + name)
            token = instance.prompts[name.replace(" ", "_")]
            if not token:
                raise RuntimeError("Prompt is requested despite not being implemented: " + name)
            return token
