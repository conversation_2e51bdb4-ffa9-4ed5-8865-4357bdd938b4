from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaTask import LongRunningZairaTask

class SupervisorTask_Python(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)
        if user.my_task.calling_bot.name == "Python":
            await user.my_task.send_response("Call trace:\n" + "\n".join(state.call_trace) + "Result:\n" + input_message)
            LogFire.log("OUTPUT", "Python:", input_message)

async def create_out_task_python() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_Python(name="python_out", prompt_id="Output_Sender_Python"))
