from imports import *

from aioconsole import ainput
from asyncio import sleep

from managers.manager_users import ZairaUserManager
from userprofiles.permission_levels import PERMISSION_LEVELS
from endpoints.mybot_generic import MyBot_Generic

async def exec_main_loop():
    shouldStop = False
    user = await ZairaUserManager.add_user("Python", PERMISSION_LEVELS.ADMIN, ZairaUserManager.create_guid(), ZairaUserManager.create_guid())
    bot_generic = MyBot_Generic(None, "Python")
    while shouldStop == False:
        if Globals.is_docker():
            await sleep(60)
        else:
            print("")
            print("")
            print("")
            print("")
            print("")
            print("")
            print("")
            print("")
            print("")
            await sleep(1) # Give the LongRunningZairaTask a second to shut down
            print("")
            user_input: str = await ainput("Please ask your company-specific question: ")#"tell me about paul graham"#
            if user_input == "":
                continue

            if user_input == "Quit":
                shouldStop = True
            else:
                await user.on_message(user_input.strip(), bot_generic, [], None)
