"""
Enterprise-grade chat message system with comprehensive data handling,
validation, serialization, and conversion capabilities.
"""

from __future__ import annotations
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from enum import Enum
from uuid import uuid4
from hashlib import md5
from pydantic import (
    BaseModel, 
    Field, 
    ConfigDict, 
    field_validator, 
    computed_field,
    model_validator,
    field_serializer
)
from langchain.schema import HumanMessage, AIMessage, SystemMessage, BaseMessage
from langchain.schema.messages import FunctionMessage, ToolMessage

# ============================================================================
# ENUMS AND CONSTANTS
# ============================================================================

class MessageRole(str, Enum):
    """Standardized message roles with validation."""
    USER = "user"
    ASSISTANT = "assistant" 
    SYSTEM = "system"
    FUNCTION = "function"
    TOOL = "tool"
    
    @classmethod
    def from_string(cls, role: str) -> MessageRole:
        """Convert string to MessageRole with validation."""
        try:
            return cls(role.lower())
        except ValueError:
            raise ValueError(f"Invalid role: {role}. Must be one of {[r.value for r in cls]}")

class MessageStatus(str, Enum):
    """Message processing status."""
    PENDING = "pending"
    PROCESSED = "processed" 
    FAILED = "failed"
    ARCHIVED = "archived"

class Priority(str, Enum):
    """Message priority levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    URGENT = "urgent"

# ============================================================================
# CORE ZAIRA MESSAGE MODEL
# ============================================================================

class ZairaMessage(BaseModel):
    """
    Enterprise-grade Zaira message with comprehensive metadata and conversion capabilities.
    
    Features:
    - Pydantic validation and serialization
    - Multiple output formats (LangChain, OpenAI, Anthropic, etc.)
    - Comprehensive metadata tracking
    - Audit trail support
    - Enterprise-grade data handling
    """
    
    # Pydantic configuration
    model_config = ConfigDict(
        # Allow arbitrary types (for datetime, etc.)
        arbitrary_types_allowed=True,
        # Validate on assignment
        validate_assignment=True,
        # Use enum values in serialization
        use_enum_values=True,
        # Extra fields are forbidden
        extra='forbid',
        # JSON schema generation
        json_schema_serialization_defaults_required=True
    )
    
    # ========================================================================
    # CORE FIELDS
    # ========================================================================
    
    # Core message data
    role: MessageRole = Field(
        ..., 
        description="Message role (user, assistant, system, function, tool)"
    )
    content: str = Field(
        ..., 
        min_length=0,
        description="Message content"
    )
    
    # Identifiers and timestamps
    message_id: str = Field(
        default_factory=lambda: str(uuid4()),
        description="Unique message identifier"
    )
    conversation_id: Optional[str] = Field(
        default=None,
        description="Conversation/thread identifier"
    )
    timestamp: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Message creation timestamp (UTC)"
    )
    
    # Session tracking
    session_id: Optional[str] = Field(
        default=None,
        description="Session identifier"
    )
    
    # Message metadata
    status: MessageStatus = Field(
        default=MessageStatus.PROCESSED,
        description="Message processing status"
    )
    priority: Priority = Field(
        default=Priority.NORMAL,
        description="Message priority level"
    )
    
    # AI/Model specific metadata
    tokens_used: Optional[int] = Field(
        default=None,
        ge=0,
        description="Number of tokens used"
    )
    processing_time_ms: Optional[float] = Field(
        default=None,
        ge=0,
        description="Processing time in milliseconds"
    )
    
    # Content metadata  
    content_type: str = Field(
        default="text/plain",
        description="MIME type of the content"
    )
    content_hash: Optional[str] = Field(
        default=None,
        description="MD5 hash of content for deduplication"
    )
    
    # System metadata
    source: Optional[str] = Field(
        default=None,
        description="Source of the message (API, web, mobile, etc.)"
    )
    
    # Business context
    tags: List[str] = Field(
        default_factory=list,
        description="Tags for categorization"
    )
    categories: List[str] = Field(
        default_factory=list,
        description="Business categories"
    )
    sentiment_score: Optional[float] = Field(
        default=None,
        ge=-1.0,
        le=1.0,
        description="Sentiment score (-1 to 1)"
    )
    confidence_score: Optional[float] = Field(
        default=None,
        ge=0.0,
        le=1.0,
        description="Confidence score (0 to 1)"
    )
    
    # Custom metadata (flexible JSON)
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Custom metadata dictionary"
    )
    
    # Audit fields
    created_by: Optional[str] = Field(
        default=None,
        description="User who created the message"
    )
    
    # Parent/thread tracking
    parent_message_id: Optional[str] = Field(
        default=None,
        description="Parent message ID for threading"
    )
    
    # ========================================================================
    # COMPUTED FIELDS
    # ========================================================================
    
    @computed_field
    @property
    def word_count(self) -> int:
        """Calculate word count of content."""
        return len(self.content.split()) if self.content else 0
    
    @computed_field
    @property
    def character_count(self) -> int:
        """Calculate character count of content."""
        return len(self.content) if self.content else 0
    
    @computed_field
    @property
    def age_seconds(self) -> float:
        """Calculate message age in seconds."""
        return (datetime.now(timezone.utc) - self.timestamp).total_seconds()
    
    # ========================================================================
    # VALIDATORS
    # ========================================================================
    
    @field_validator('role', mode='before')
    @classmethod
    def validate_role(cls, v):
        """Validate and convert role to MessageRole enum."""
        if isinstance(v, str):
            return MessageRole.from_string(v)
        return v
    
    @field_validator('status', mode='before')
    @classmethod
    def validate_status(cls, v):
        """Validate and convert status to MessageStatus enum."""
        if isinstance(v, str):
            return MessageStatus(v)
        return v
    
    @field_validator('priority', mode='before')
    @classmethod
    def validate_priority(cls, v):
        """Validate and convert priority to Priority enum."""
        if isinstance(v, str):
            return Priority(v)
        return v
    
    @field_validator('content')
    @classmethod
    def validate_content(cls, v, info):
        """Validate content based on role."""
        # Allow empty content for system messages
        if not v and info.data.get('role') != MessageRole.SYSTEM:
            raise ValueError("Content cannot be empty for non-system messages")
        return v
    
    @field_validator('tags', 'categories')
    @classmethod
    def validate_string_lists(cls, v):
        """Ensure tags and categories are unique and cleaned."""
        if v:
            # Remove duplicates and strip whitespace
            cleaned = [tag.strip() for tag in v if tag.strip()]
            return list(dict.fromkeys(cleaned))  # Preserve order while removing duplicates
        return v
    
    @model_validator(mode='after')
    def generate_content_hash(self):
        """Generate content hash if not provided."""
        if self.content and not self.content_hash:
            self.content_hash = md5(self.content.encode()).hexdigest()
        return self
    
    # ========================================================================
    # SERIALIZATION
    # ========================================================================
    
    @field_serializer('timestamp')
    def serialize_datetime(self, value: Optional[datetime]) -> Optional[str]:
        """Serialize datetime to ISO format."""
        return value.isoformat() if value else None
    
    # ========================================================================
    # CONVERSION METHODS
    # ========================================================================
    
    def to_dict(
        self, 
        include_metadata: bool = True, 
        exclude_none: bool = True,
        include_computed: bool = False
    ) -> Dict[str, Any]:
        """
        Convert to dictionary with flexible options.
        
        Args:
            include_metadata: Include all metadata fields
            exclude_none: Exclude None values from output
            include_computed: Include computed fields
        """
        # Get base dict
        if exclude_none:
            data = self.model_dump(exclude_none=True)
        else:
            data = self.model_dump()
        
        # Include computed fields if requested
        if include_computed:
            data.update({
                'word_count': self.word_count,
                'character_count': self.character_count,
                'age_seconds': self.age_seconds
            })
        
        # Optionally exclude metadata
        if not include_metadata:
            metadata_fields = {
                'tokens_used', 'processing_time_ms', 'content_hash', 'source',
                'sentiment_score', 'confidence_score', 'created_by'
            }
            for field in metadata_fields:
                data.pop(field, None)
        
        return data
    
    def to_json(
        self, 
        indent: Optional[int] = None, 
        exclude_none: bool = True
    ) -> str:
        """Convert to JSON string with Pydantic's built-in serialization."""
        return self.model_dump_json(indent=indent, exclude_none=exclude_none)
    
    def to_langchain(self) -> BaseMessage:
        """Convert to appropriate LangChain message type."""
        
        # Create additional metadata for LangChain
        lc_metadata = {
            'message_id': self.message_id,
            'timestamp': self.timestamp.isoformat(),
            'tokens_used': self.tokens_used,
        }
        
        # Add custom metadata
        lc_metadata.update(self.metadata)
        
        # Remove None values
        lc_metadata = {k: v for k, v in lc_metadata.items() if v is not None}
        
        # Create appropriate message type
        if self.role == MessageRole.USER:
            return HumanMessage(content=self.content, additional_kwargs=lc_metadata)
        elif self.role == MessageRole.ASSISTANT:
            return AIMessage(content=self.content, additional_kwargs=lc_metadata)
        elif self.role == MessageRole.SYSTEM:
            return SystemMessage(content=self.content, additional_kwargs=lc_metadata)
        elif self.role == MessageRole.FUNCTION:
            return FunctionMessage(
                content=self.content, 
                name=self.metadata.get('function_name', 'unknown'),
                additional_kwargs=lc_metadata
            )
        elif self.role == MessageRole.TOOL:
            return ToolMessage(
                content=self.content, 
                tool_call_id=self.metadata.get('tool_call_id', ''),
                additional_kwargs=lc_metadata
            )
        else:
            # Fallback to HumanMessage
            return HumanMessage(content=self.content, additional_kwargs=lc_metadata)
    
    def to_openai_format(self) -> Dict[str, Any]:
        """Convert to OpenAI API message format."""
        base = {
            "role": self.role.value,
            "content": self.content
        }
        
        # Add function/tool specific fields if present
        if self.role == MessageRole.FUNCTION and 'function_name' in self.metadata:
            base['name'] = self.metadata['function_name']
        elif self.role == MessageRole.TOOL and 'tool_call_id' in self.metadata:
            base['tool_call_id'] = self.metadata['tool_call_id']
        
        return base
    
    def to_anthropic_format(self) -> Dict[str, Any]:
        """Convert to Anthropic API message format."""
        # Map roles for Anthropic
        role_mapping = {
            MessageRole.USER: "user",
            MessageRole.ASSISTANT: "assistant",
            MessageRole.SYSTEM: "user",  # Anthropic handles system messages differently
            MessageRole.FUNCTION: "user",  # Functions become user messages
            MessageRole.TOOL: "user"  # Tools become user messages
        }
        
        return {
            "role": role_mapping.get(self.role, "user"),
            "content": self.content
        }
    
    def to_minimal_dict(self) -> Dict[str, Any]:
        """Convert to minimal dictionary for storage efficiency."""
        return {
            "id": self.message_id,
            "role": self.role.value,
            "content": self.content,
            "timestamp": self.timestamp.isoformat(),
            "conversation_id": self.conversation_id
        }
    
    # ========================================================================
    # FACTORY METHODS
    # ========================================================================
    
    @classmethod
    def create_user_message(
        cls, 
        content: str,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        priority: Priority = Priority.NORMAL,
        tags: Optional[List[str]] = None,
        categories: Optional[List[str]] = None,
        metadata: Optional[Dict[str, Any]] = None,
        created_by: Optional[str] = None,
        parent_message_id: Optional[str] = None
    ) -> ZairaMessage:
        """Factory method for user messages."""
        return cls(
            role=MessageRole.USER, 
            content=content,
            conversation_id=str(conversation_id),
            session_id=str(session_id),
            priority=priority,
            tags=tags or [],
            categories=categories or [],
            metadata=metadata or {},
            created_by=created_by,
            parent_message_id=parent_message_id
        )
    
    @classmethod
    def create_assistant_message(
        cls, 
        content: str,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        tokens_used: Optional[int] = None,
        processing_time_ms: Optional[float] = None,
        priority: Priority = Priority.NORMAL,
        tags: Optional[List[str]] = None,
        categories: Optional[List[str]] = None,
        confidence_score: Optional[float] = None,
        metadata: Optional[Dict[str, Any]] = None,
        created_by: Optional[str] = None,
        parent_message_id: Optional[str] = None
    ) -> ZairaMessage:
        """Factory method for assistant messages."""
        return cls(
            role=MessageRole.ASSISTANT, 
            content=content,
            conversation_id=str(conversation_id),
            session_id=str(session_id),
            tokens_used=tokens_used,
            processing_time_ms=processing_time_ms,
            priority=priority,
            tags=tags or [],
            categories=categories or [],
            confidence_score=confidence_score,
            metadata=metadata or {},
            created_by=created_by,
            parent_message_id=parent_message_id
        )
    
    @classmethod
    def create_system_message(
        cls, 
        content: str,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        priority: Priority = Priority.NORMAL,
        metadata: Optional[Dict[str, Any]] = None,
        created_by: Optional[str] = None
    ) -> ZairaMessage:
        """Factory method for system messages."""
        return cls(
            role=MessageRole.SYSTEM, 
            content=content,
            conversation_id=str(conversation_id),
            session_id=str(session_id),
            priority=priority,
            metadata=metadata or {},
            created_by=created_by
        )
    
    @classmethod
    def create_function_message(
        cls, 
        content: str, 
        function_name: str,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        priority: Priority = Priority.NORMAL,
        metadata: Optional[Dict[str, Any]] = None,
        created_by: Optional[str] = None,
        parent_message_id: Optional[str] = None
    ) -> ZairaMessage:
        """Factory method for function messages."""
        func_metadata = metadata or {}
        func_metadata['function_name'] = function_name
        return cls(
            role=MessageRole.FUNCTION, 
            content=content,
            conversation_id=str(conversation_id),
            session_id=str(session_id),
            priority=priority,
            metadata=func_metadata,
            created_by=created_by,
            parent_message_id=parent_message_id
        )
    
    @classmethod
    def create_tool_message(
        cls, 
        content: str, 
        tool_call_id: str,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        priority: Priority = Priority.NORMAL,
        metadata: Optional[Dict[str, Any]] = None,
        created_by: Optional[str] = None,
        parent_message_id: Optional[str] = None
    ) -> ZairaMessage:
        """Factory method for tool messages."""
        tool_metadata = metadata or {}
        tool_metadata['tool_call_id'] = tool_call_id
        return cls(
            role=MessageRole.TOOL, 
            content=content,
            conversation_id=str(conversation_id),
            session_id=str(session_id),
            priority=priority,
            metadata=tool_metadata,
            created_by=created_by,
            parent_message_id=parent_message_id
        )
    
    @classmethod
    def from_langchain(
        cls, 
        lc_message: BaseMessage,
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        priority: Priority = Priority.NORMAL,
        created_by: Optional[str] = None
    ) -> ZairaMessage:
        """Create ZairaMessage from LangChain message."""
        
        # Determine role from message type
        role_mapping = {
            'HumanMessage': MessageRole.USER,
            'AIMessage': MessageRole.ASSISTANT,
            'SystemMessage': MessageRole.SYSTEM,
            'FunctionMessage': MessageRole.FUNCTION,
            'ToolMessage': MessageRole.TOOL,
        }
        
        message_type = type(lc_message).__name__
        role = role_mapping.get(message_type, MessageRole.USER)
        
        # Extract metadata
        metadata = getattr(lc_message, 'additional_kwargs', {})
        
        return cls(
            role=role,
            content=lc_message.content,
            conversation_id=conversation_id,
            session_id=session_id,
            priority=priority,
            metadata=metadata,
            created_by=created_by
        )
    
    @classmethod
    def from_openai_format(
        cls, 
        openai_message: Dict[str, Any],
        conversation_id: Optional[str] = None,
        session_id: Optional[str] = None,
        priority: Priority = Priority.NORMAL,
        created_by: Optional[str] = None
    ) -> ZairaMessage:
        """Create ZairaMessage from OpenAI API format."""
        role = MessageRole.from_string(openai_message['role'])
        content = openai_message['content']
        
        # Handle special fields
        metadata = {}
        if 'name' in openai_message:
            metadata['function_name'] = openai_message['name']
        if 'tool_call_id' in openai_message:
            metadata['tool_call_id'] = openai_message['tool_call_id']
        
        return cls(
            role=role, 
            content=content,
            conversation_id=conversation_id,
            session_id=session_id,
            priority=priority,
            metadata=metadata,
            created_by=created_by
        )
    
    # ========================================================================
    # UTILITY METHODS
    # ========================================================================
    
    def update_metadata(self, **metadata_updates) -> ZairaMessage:
        """Update metadata and return new instance."""
        new_metadata = {**self.metadata, **metadata_updates}
        return self.model_copy(update={'metadata': new_metadata})
    
    def mark_processed(self, processing_time_ms: Optional[float] = None) -> ZairaMessage:
        """Mark message as processed."""
        updates = {'status': MessageStatus.PROCESSED}
        if processing_time_ms is not None:
            updates['processing_time_ms'] = processing_time_ms
        
        return self.model_copy(update=updates)
    
    def mark_failed(self, error_message: Optional[str] = None) -> ZairaMessage:
        """Mark message as failed."""
        updates = {'status': MessageStatus.FAILED}
        if error_message:
            metadata = {**self.metadata, 'error_message': error_message}
            updates['metadata'] = metadata
        
        return self.model_copy(update=updates)
    
    def add_tags(self, *tags: str) -> ZairaMessage:
        """Add tags to message."""
        new_tags = list(dict.fromkeys(self.tags + list(tags)))  # Preserve order, remove duplicates
        return self.model_copy(update={'tags': new_tags})
    
    def add_categories(self, *categories: str) -> ZairaMessage:
        """Add categories to message."""
        new_categories = list(dict.fromkeys(self.categories + list(categories)))
        return self.model_copy(update={'categories': new_categories})
    
    def calculate_cost(self, cost_per_token: float) -> Optional[float]:
        """Calculate message cost based on tokens used."""
        if self.tokens_used:
            return self.tokens_used * cost_per_token
        return None
    
    def is_from_user(self) -> bool:
        """Check if message is from user."""
        return self.role == MessageRole.USER
    
    def is_from_assistant(self) -> bool:
        """Check if message is from assistant."""
        return self.role == MessageRole.ASSISTANT
    
    def is_system_message(self) -> bool:
        """Check if message is a system message."""
        return self.role == MessageRole.SYSTEM
    
    def __str__(self) -> str:
        """String representation."""
        content_preview = self.content[:50] + "..." if len(self.content) > 50 else self.content
        return f"ZairaMessage(id={self.message_id[:8]}, role={self.role.value}, content='{content_preview}')"
    
    def __repr__(self) -> str:
        """Detailed representation."""
        return (f"ZairaMessage(message_id='{self.message_id}', role={self.role.value}, "
                f"conversation_id='{self.conversation_id}', timestamp='{self.timestamp.isoformat()}')")