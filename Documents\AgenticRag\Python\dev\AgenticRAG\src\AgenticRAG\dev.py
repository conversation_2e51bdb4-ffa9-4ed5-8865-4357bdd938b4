from ast import NodeTransformer, copy_location, AST, parse, fix_missing_locations
from importlib.abc import Loader, MetaPathFinder
from importlib.util import spec_from_file_location, module_from_spec
from sys import meta_path
from sys import path as sys_path
from os import sep
from os import path as os_path

class TryRemover(NodeTransformer):
    def visit_Try(self, node):
        # Recursively visit the try body and flatten results
        new_body = []
        for stmt in node.body:
            visited = self.visit(stmt)
            if isinstance(visited, list):
                new_body.extend(visited)
            elif visited is not None:
                new_body.append(visited)

        # Apply location copying only to AST nodes
        new_body = [copy_location(n, node) for n in new_body if isinstance(n, AST)]

        return new_body  # Replace Try with just the body

class TrylessLoader(Loader):
    def __init__(self, fullname, mypath):
        self.fullname = fullname
        self.path = mypath

    def create_module(self, spec):
        return None

    def exec_module(self, module):
        with open(self.path, 'r', encoding='utf-8') as f:
            source = f.read()
        tree = parse(source)
        tree = fix_missing_locations(TryRemover().visit(tree))
        code = compile(tree, self.path, 'exec')
        exec(code, module.__dict__)

class TrylessFinder(MetaPathFinder):
    def __init__(self, root_path):
        self.root_path = os_path.abspath(root_path)

    def find_spec(self, fullname, mypath, target=None):
        module_rel_path = fullname.replace('.', sep) + '.py'
        module_path = os_path.join(self.root_path, module_rel_path)
        if os_path.isfile(module_path):
            return spec_from_file_location(
                fullname, module_path, loader=TrylessLoader(fullname, module_path)
            )
        return None

def run_tryless_project(entry_point, root_path):
    meta_path.insert(0, TrylessFinder(root_path))
    sys_path.insert(0, root_path)
    spec = spec_from_file_location('__main__', entry_point)
    module = module_from_spec(spec)
    spec.loader.exec_module(module)

# Run the async program
if __name__ == "__main__":
    current_file_path = os_path.abspath(__file__)
    project_root = os_path.dirname(current_file_path)
    entry_script = os_path.join(project_root, 'dev_run.py')  # update if your entry point differs
    run_tryless_project(entry_script, project_root)
