#!/usr/bin/env python3
"""
Test script to verify WhatsApp credentials are working
"""

import asyncio
import httpx
from config import WHATSAPP_PHONE_NUMBER_ID, WHATSAPP_ACCESS_TOKEN

async def test_whatsapp_credentials():
    """Test if WhatsApp credentials work by sending a test message"""
    
    # According to Meta docs, test numbers can only send to verified recipients
    # We need to check if the recipient number is verified in Meta Business Manager
    
    # Target phone number (your personal number) - trying different formats
    phone_formats = [
        "***********",      # Without +
        "+***********",     # With +
        "31 6 11 23 94 87"  # With spaces (this will likely fail)
    ]
    
    # Test message
    message = "Test message from WhatsApp bot - checking if recipient is verified"
    
    try:
        # Prepare the API endpoint
        url = f"https://graph.facebook.com/v18.0/{WHATSAPP_PHONE_NUMBER_ID}/messages"
        
        # Prepare the message payload
        payload = {
            "messaging_product": "whatsapp",
            "to": to_number,
            "type": "text",
            "text": {
                "body": message
            }
        }
        
        # Set up headers with the access token
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {WHATSAPP_ACCESS_TOKEN}"
        }
        
        print(f"Testing WhatsApp API with:")
        print(f"Phone Number ID: {WHATSAPP_PHONE_NUMBER_ID}")
        print(f"Access Token: {WHATSAPP_ACCESS_TOKEN[:20]}...")
        print(f"Target Number: {to_number}")
        print(f"URL: {url}")
        
        # Send the message
        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=payload, headers=headers)
            
            print(f"Response Status: {response.status_code}")
            print(f"Response Body: {response.text}")
            
            if response.status_code == 200:
                print("✅ WhatsApp message sent successfully!")
                return True
            else:
                print(f"❌ Failed to send WhatsApp message. Status: {response.status_code}")
                return False
                
    except Exception as e:
        print(f"❌ Error testing WhatsApp: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_whatsapp_credentials())