from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from langchain_core.messages import HumanMessage

from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from managers.manager_prompts import Prompt<PERSON>anager

def verify_output_language(query: str, output: str, state: SupervisorTaskState):
    """Verifies that the output is in the same language as the input query"""
    task = SupervisorManager.get_task("language_verifier")

    # Detect input language
    input_lang_result = task.model.invoke(f"{PromptManager.get_prompt('Output_Processing_Language_Verifier_Detect_Input')}: {query}")
    if isinstance(input_lang_result, etc.helper_functions.get_any_message_as_type()):
        input_lang_result = input_lang_result.content

    # Detect output language
    output_lang_result = task.model.invoke(f"{PromptManager.get_prompt('Output_Processing_Language_Verifier_Detect_Output')}: {output}")
    if isinstance(output_lang_result, etc.helper_functions.get_any_message_as_type()):
        output_lang_result = output_lang_result.content

    # If languages match, return the output as is
    if input_lang_result.strip().lower() == output_lang_result.strip().lower():
        return output

    # If languages don't match, translate the output to the input language
    translation_result = task.model.invoke(f"Translate the following text from {output_lang_result} to {input_lang_result}: {output}")
    if isinstance(translation_result, etc.helper_functions.get_any_message_as_type()):
        translation_result = translation_result.content

    return translation_result

class SupervisorTask_LanguageVerifier(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        # Extract the original input and output message
        input_message = state.original_input
        output_message = state.messages[-1].content if len(state.messages) > 0 else ""

        # Split the output message if it contains the marker
        #split = output_message.split("___Output message___:", 1)
        #if len(split) > 1:
        #    output_message = split[1].strip()

        # Verify the language and get the corrected output
        result = verify_output_language(input_message, output_message, state)

        # Return the verified output
        return result

async def create_out_processing_language_verifier() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_LanguageVerifier(name="language_verifier", prompt_id="Output_Processing_Language_Verifier"))
