from imports import *
from discord import Message
import asyncio

from endpoints.discord_endpoint import MyDiscordBot
from userprofiles.ZairaUser import Zaira<PERSON>ser

async def get_response(message: Message, user: <PERSON>air<PERSON><PERSON><PERSON>, is_private: bool) -> str:
    try:
        # Half-removed. Below code is stale and needs migrated to LongRunningZairaTask
        # If wait is requested
        def check(m):
            # Check if the reply is from the same user in the same channel
            return m.author == message.author and m.channel == message.channel

        async def handle_query():
            try:
                reply = await MyDiscordBot.bot.wait_for('message', check=check, timeout=360)
                MyDiscordBot.readyToReceive = True
                MyDiscordBot.readyToReceive_Notify = True
                await MyDiscordBot.send_a_discord_message(message, f'You replied: "{reply.content}" within 6 minutes! Nice!', is_private)
            except asyncio.TimeoutError:
                await message.channel.send(f'{message.author.mention}, time\’s up! You didn\’t reply within 6 minutes.')

        MyDiscordBot.readyToReceive = False
        MyDiscordBot.readyToReceive_Notify = False
        MyDiscordBot.bot.loop.create_task(handle_query())
        await MyDiscordBot.send_a_discord_message(message, f'{message.author.mention}, you have 6 minutes to reply!', is_private)
        return

        # If no valid command
        await MyDiscordBot.send_a_discord_message(message, choice([ 'I do not understand...',
                                                                    'What are you talking about?',
                                                                    'Do you mind rephrasing that?']), is_private)
    except Exception as e:
        raise NotImplementedError('Code is missing... {e}')
