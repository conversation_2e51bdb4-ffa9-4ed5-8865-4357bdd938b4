from imports import *

from langgraph.graph import END

from managers.manager_supervisors import <PERSON><PERSON><PERSON><PERSON><PERSON>, SupervisorSupervisor
from tasks.outputs.output_tasks.task_out_discord import create_out_task_discord
from tasks.outputs.output_tasks.task_out_teams import create_out_task_teams
from tasks.outputs.output_tasks.task_out_python import create_out_task_python
from tasks.outputs.output_tasks.task_out_http import create_out_task_http
from tasks.outputs.output_tasks.task_out_whatsapp import create_out_task_whatsapp


# Define tools for agents

async def create_supervisor_output_sender() -> SupervisorSupervisor:
    class TaskCreator:

        async def create_tasks(self):
            self.discord_task = await create_out_task_discord()
            self.teams_task = await create_out_task_teams()
            self.python_task = await create_out_task_python()
            self.http_task = await create_out_task_http()
            self.whatsapp_task = await create_out_task_whatsapp()


        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(SupervisorSupervisor(name="output_sender_supervisor", prompt_id="Output_Supervisor_Sender"
                #" If it's unlikely that any of your remaining tasks are related to the HumanMessage, respond with {END} instead of a task name."
                )) \
                .add_task(self.discord_task) \
                .add_task(self.teams_task) \
                .add_task(self.python_task) \
                .add_task(self.http_task) \
                .add_task(self.whatsapp_task) \
                .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()
