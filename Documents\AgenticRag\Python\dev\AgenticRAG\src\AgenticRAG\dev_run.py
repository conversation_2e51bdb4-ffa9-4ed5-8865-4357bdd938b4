from imports import *

from asyncio import run, get_event_loop
from main import mainFunc
from sys import gettrace
from deployment.oauth_endpoint_external import main as oauth_endpoint_external_main
import os

async def main():
    ZairaSettings.IsDebugMode = False
    Globals.set_debug(ZairaSettings.IsDebugMode)
    
    # Skip OAuth external endpoint if running in Claude environment
    from etc.helper_functions import is_claude_environment
    if ZairaSettings.IsDebugMode and not is_claude_environment():
        await oauth_endpoint_external_main()
    elif is_claude_environment():
        print("Claude environment detected - skipping OAuth external endpoint")
        
    await mainFunc()

    # Run multiple async tasks concurrently
    #task1 = asyncio.create_task(mainFunc(is_debug=True))

    # Wait for both tasks to complete
    #await task1

# Run the async program
if __name__ == "__main__":
    # if gettrace():
    #     # Debugger is attached, use event loop directly
    #     loop = get_event_loop()
    #     loop.run_until_complete(main())
    # else:
    run(main())
