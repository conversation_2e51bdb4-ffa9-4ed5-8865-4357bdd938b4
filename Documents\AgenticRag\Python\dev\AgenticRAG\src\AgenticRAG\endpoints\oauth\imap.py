from imports import *

from aiohttp import web

from endpoints.oauth._verifier_ import OAuth2App

class OAuth2IMAP(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_input("input", ["str:Server Naam", "int:Netwerk port", "str:E-mail adres", "str:E-mail wachtwoord"])

    async def on_success_return(self, request: web.Request) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return(request)
        ret_html += "Ophalen van mails wordt gestart."
        return ret_html

    async def on_success_execute(self, request: web.Request) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        ret_html = await super().on_success_execute(request)
        from managers.manager_supervisors import SupervisorManager
        await SupervisorManager.get_task("imap_retrieval_task").call_task()
        ret_html += "Mails zijn succesvol opgehaald."
        return ret_html
    
    async def on_success_execute_fail(self, request: web.Request) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute_fail(request)
        ret_html += ""
        return ret_html
