"""
Performance tests for database operations
"""
import pytest
import asyncio
import time
import psutil
import os
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.performance
@pytest.mark.asyncio
class TestDatabasePerformance:
    """Test database operation performance"""
    
    async def test_postgresql_connection_performance(self, mock_database_connections):
        """Test PostgreSQL connection establishment performance"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        # Connection performance benchmarks
        MAX_CONNECTION_TIME = 2.0  # seconds
        TARGET_CONNECTION_TIME = 0.5  # seconds
        
        connection_times = []
        
        # Test multiple connection attempts
        for i in range(10):
            start_time = time.time()
            
            # Mock connection establishment
            connection = await manager.get_connection("test_db")
            
            end_time = time.time()
            connection_time = end_time - start_time
            connection_times.append(connection_time)
            
            print(f"Connection {i+1}: {connection_time:.3f}s")
            
            # Individual connection performance
            assert connection_time < MAX_CONNECTION_TIME, f"Connection too slow: {connection_time:.3f}s"
        
        # Overall connection performance
        avg_connection_time = sum(connection_times) / len(connection_times)
        max_connection_time = max(connection_times)
        
        print(f"\nPostgreSQL Connection Performance:")
        print(f"Average connection time: {avg_connection_time:.3f}s")
        print(f"Maximum connection time: {max_connection_time:.3f}s")
        
        # Performance assertions
        assert avg_connection_time < TARGET_CONNECTION_TIME, f"Average connection time too slow: {avg_connection_time:.3f}s"
        
        return {
            'avg_connection_time': avg_connection_time,
            'max_connection_time': max_connection_time,
            'connection_times': connection_times
        }
    
    async def test_scheduled_task_crud_performance(self, mock_database_connections, sample_scheduled_task_data):
        """Test scheduled task CRUD operation performance"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        
        manager = ScheduledTaskPersistenceManager.get_instance()
        
        # CRUD performance benchmarks
        MAX_CRUD_TIME = 1.0  # seconds per operation
        NUM_OPERATIONS = 50
        
        # Mock successful operations
        mock_database_connections['postgresql'].execute.return_value = True
        mock_database_connections['postgresql'].fetch.return_value = [sample_scheduled_task_data]
        
        # Mock the save_task method to avoid actual database calls
        with patch.object(manager, 'save_task') as mock_save_task:
            mock_save_task.return_value = True
            
            # Test CREATE performance
            create_times = []
            for i in range(NUM_OPERATIONS):
                task_data = sample_scheduled_task_data.copy()
                task_data['task_id'] = f'perf_test_task_{i}'
                
                start_time = time.time()
                await manager.save_task(task_data)
                end_time = time.time()
                
                create_time = end_time - start_time
                create_times.append(create_time)
                
                assert create_time < MAX_CRUD_TIME, f"CREATE too slow: {create_time:.3f}s"
        
            # Test READ performance
            read_times = []
            with patch.object(manager, 'get_active_tasks') as mock_get_tasks:
                mock_get_tasks.return_value = [sample_scheduled_task_data]
                
                for i in range(NUM_OPERATIONS):
                    start_time = time.time()
                    tasks = await manager.get_active_tasks("test-user-123")
                    end_time = time.time()
                    
                    read_time = end_time - start_time
                    read_times.append(read_time)
                    
                    assert read_time < MAX_CRUD_TIME, f"READ too slow: {read_time:.3f}s"
                    assert len(tasks) > 0, "No tasks retrieved"
        
        # Performance summary
        avg_create_time = sum(create_times) / len(create_times)
        avg_read_time = sum(read_times) / len(read_times)
        
        print(f"\nScheduled Task CRUD Performance:")
        print(f"Average CREATE time: {avg_create_time:.3f}s")
        print(f"Average READ time: {avg_read_time:.3f}s")
        print(f"Operations per second (CREATE): {1/max(avg_create_time, 0.0001):.1f}")
        print(f"Operations per second (READ): {1/max(avg_read_time, 0.0001):.1f}")
        
        return {
            'avg_create_time': avg_create_time,
            'avg_read_time': avg_read_time,
            'create_ops_per_sec': 1/max(avg_create_time, 0.0001),
            'read_ops_per_sec': 1/max(avg_read_time, 0.0001)
        }
    
    async def test_qdrant_vector_operations_performance(self, mock_database_connections):
        """Test Qdrant vector operations performance"""
        from managers.manager_qdrant import QDrantManager
        
        manager = QDrantManager.get_instance()
        
        # Vector operation benchmarks
        MAX_UPSERT_TIME = 0.5  # seconds
        MAX_SEARCH_TIME = 1.0  # seconds
        NUM_VECTORS = 20
        
        # Mock vector operations
        mock_database_connections['qdrant'].upsert.return_value = True
        mock_database_connections['qdrant'].search_similar.return_value = [
            {'id': f'doc_{i}', 'score': 0.9 - i*0.01} for i in range(5)
        ]
        
        # Mock the upsert and search methods
        with patch.object(manager, 'upsert') as mock_upsert, \
             patch.object(manager, 'search_similar') as mock_search:
            
            mock_upsert.return_value = True
            mock_search.return_value = [{'id': f'doc_{i}', 'score': 0.9 - i*0.01} for i in range(5)]
            
            # Test UPSERT performance
            upsert_times = []
            for i in range(NUM_VECTORS):
                document_text = f"Test document {i} with various content and information"
                metadata = {'doc_id': i, 'category': 'test'}
                
                start_time = time.time()
                result = await manager.upsert(f"test_doc_{i}", document_text, metadata)
                end_time = time.time()
                
                upsert_time = end_time - start_time
                upsert_times.append(upsert_time)
                
                assert upsert_time < MAX_UPSERT_TIME, f"UPSERT too slow: {upsert_time:.3f}s"
                assert result is True, "Upsert operation failed"
            
            # Test SEARCH performance
            search_times = []
            search_queries = [
                "Find information about testing",
                "Locate documents with content",
                "Search for test data",
                "Retrieve information about performance"
            ]
            
            for query in search_queries:
                start_time = time.time()
                results = await manager.search_similar(query, limit=10)
                end_time = time.time()
                
                search_time = end_time - start_time
                search_times.append(search_time)
                
                assert search_time < MAX_SEARCH_TIME, f"SEARCH too slow: {search_time:.3f}s"
                assert len(results) > 0, "No search results returned"
        
        # Performance summary
        avg_upsert_time = sum(upsert_times) / len(upsert_times)
        avg_search_time = sum(search_times) / len(search_times)
        
        print(f"\nQdrant Vector Operations Performance:")
        print(f"Average UPSERT time: {avg_upsert_time:.3f}s")
        print(f"Average SEARCH time: {avg_search_time:.3f}s")
        print(f"Upserts per second: {1/max(avg_upsert_time, 0.0001):.1f}")
        print(f"Searches per second: {1/max(avg_search_time, 0.0001):.1f}")
        
        return {
            'avg_upsert_time': avg_upsert_time,
            'avg_search_time': avg_search_time,
            'upsert_ops_per_sec': 1/max(avg_upsert_time, 0.0001),
            'search_ops_per_sec': 1/max(avg_search_time, 0.0001)
        }

@pytest.mark.performance
@pytest.mark.asyncio
class TestConcurrentDatabasePerformance:
    """Test database performance under concurrent load"""
    
    async def test_concurrent_database_operations(self, mock_database_connections, sample_scheduled_task_data):
        """Test database performance under concurrent access"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        from managers.manager_qdrant import QDrantManager
        
        task_manager = ScheduledTaskPersistenceManager.get_instance()
        vector_manager = QDrantManager.get_instance()
        
        # Concurrent operation parameters
        CONCURRENT_USERS = 10
        OPERATIONS_PER_USER = 5
        MAX_CONCURRENT_TIME = 10.0  # seconds
        
        # Mock successful operations
        mock_database_connections['postgresql'].execute.return_value = True
        mock_database_connections['postgresql'].fetch.return_value = [sample_scheduled_task_data]
        mock_database_connections['qdrant'].upsert.return_value = True
        mock_database_connections['qdrant'].search_similar.return_value = [
            {'id': 'doc_1', 'score': 0.9}
        ]
        
        async def simulate_user_operations(user_id: int):
            """Simulate concurrent user database operations"""
            user_operations = []
            
            for op_id in range(OPERATIONS_PER_USER):
                # Alternate between different operations
                if op_id % 3 == 0:
                    # PostgreSQL operation
                    start_time = time.time()
                    task_data = sample_scheduled_task_data.copy()
                    task_data['task_id'] = f'user_{user_id}_task_{op_id}'
                    # Mock the save_task call for performance testing
                    with patch.object(task_manager, 'save_task') as mock_save:
                        mock_save.return_value = True
                        await task_manager.save_task(task_data)
                    end_time = time.time()
                    user_operations.append(('postgresql_save', end_time - start_time))
                    
                elif op_id % 3 == 1:
                    # Vector operation
                    start_time = time.time()
                    await vector_manager.upsert(
                        f'user_{user_id}_doc_{op_id}',
                        f'User {user_id} document {op_id}',
                        {'user_id': user_id, 'op_id': op_id}
                    )
                    end_time = time.time()
                    user_operations.append(('vector_upsert', end_time - start_time))
                    
                else:
                    # Search operation
                    start_time = time.time()
                    await vector_manager.search_similar(f'User {user_id} query {op_id}')
                    end_time = time.time()
                    user_operations.append(('vector_search', end_time - start_time))
            
            return user_operations
        
        # Execute concurrent operations
        start_time = time.time()
        
        tasks = [simulate_user_operations(user_id) for user_id in range(CONCURRENT_USERS)]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze results
        all_operations = []
        for user_ops in results:
            all_operations.extend(user_ops)
        
        # Group by operation type
        postgresql_ops = [op[1] for op in all_operations if op[0] == 'postgresql_save']
        vector_upsert_ops = [op[1] for op in all_operations if op[0] == 'vector_upsert']
        vector_search_ops = [op[1] for op in all_operations if op[0] == 'vector_search']
        
        print(f"\nConcurrent Database Performance Results:")
        print(f"Concurrent users: {CONCURRENT_USERS}")
        print(f"Operations per user: {OPERATIONS_PER_USER}")
        print(f"Total operations: {len(all_operations)}")
        print(f"Total execution time: {total_time:.2f}s")
        print(f"Operations per second: {len(all_operations) / max(total_time, 0.001):.2f}")
        
        if postgresql_ops:
            print(f"PostgreSQL avg time: {sum(postgresql_ops)/len(postgresql_ops):.3f}s")
        if vector_upsert_ops:
            print(f"Vector upsert avg time: {sum(vector_upsert_ops)/len(vector_upsert_ops):.3f}s")
        if vector_search_ops:
            print(f"Vector search avg time: {sum(vector_search_ops)/len(vector_search_ops):.3f}s")
        
        # Performance assertions
        assert total_time < MAX_CONCURRENT_TIME, f"Concurrent operations too slow: {total_time:.2f}s"
        assert len(all_operations) == CONCURRENT_USERS * OPERATIONS_PER_USER, "Some operations failed"
        
        return {
            'total_time': total_time,
            'operations_per_second': len(all_operations) / max(total_time, 0.001),
            'postgresql_avg': sum(postgresql_ops)/len(postgresql_ops) if postgresql_ops else 0,
            'vector_upsert_avg': sum(vector_upsert_ops)/len(vector_upsert_ops) if vector_upsert_ops else 0,
            'vector_search_avg': sum(vector_search_ops)/len(vector_search_ops) if vector_search_ops else 0
        }
    
    async def test_database_connection_pooling_performance(self, mock_database_connections):
        """Test database connection pooling under load"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        # Connection pooling parameters
        CONCURRENT_CONNECTIONS = 20
        MAX_POOL_TIME = 5.0  # seconds
        
        async def get_multiple_connections(connection_count: int):
            """Get multiple database connections"""
            connections = []
            connection_times = []
            
            for i in range(connection_count):
                start_time = time.time()
                connection = await manager.get_connection(f"test_db_{i}")
                end_time = time.time()
                
                connections.append(connection)
                connection_times.append(end_time - start_time)
            
            return connection_times
        
        # Test connection pooling performance
        start_time = time.time()
        
        # Simulate concurrent connection requests
        tasks = [get_multiple_connections(5) for _ in range(4)]  # 4 users, 5 connections each
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze connection times
        all_connection_times = []
        for user_times in results:
            all_connection_times.extend(user_times)
        
        avg_connection_time = sum(all_connection_times) / len(all_connection_times)
        max_connection_time = max(all_connection_times)
        
        print(f"\nDatabase Connection Pooling Performance:")
        print(f"Total connections: {len(all_connection_times)}")
        print(f"Total time: {total_time:.2f}s")
        print(f"Average connection time: {avg_connection_time:.3f}s")
        print(f"Maximum connection time: {max_connection_time:.3f}s")
        print(f"Connections per second: {len(all_connection_times) / max(total_time, 0.001):.2f}")
        
        # Performance assertions
        assert total_time < MAX_POOL_TIME, f"Connection pooling too slow: {total_time:.2f}s"
        assert avg_connection_time < 0.5, f"Average connection time too slow: {avg_connection_time:.3f}s"
        
        return {
            'total_time': total_time,
            'avg_connection_time': avg_connection_time,
            'max_connection_time': max_connection_time,
            'connections_per_second': len(all_connection_times) / max(total_time, 0.001)
        }