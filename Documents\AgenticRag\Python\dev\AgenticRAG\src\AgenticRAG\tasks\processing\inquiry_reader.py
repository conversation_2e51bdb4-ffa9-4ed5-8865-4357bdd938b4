from imports import *

from langchain_core.tools import tool
import logging
import smtplib
import base64
from typing import Optional

from managers.manager_supervisors import Supervisor<PERSON><PERSON><PERSON>, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaTask import LongRunningZairaTask


async def create_supervisor_receipts_scanner() -> SupervisorSupervisor:
    class TaskCreator:
        inquiry_scanner_task: SupervisorTask_SingleAgent = None
        inquiry_analyzer_task: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.inquiry_scanner_task = SupervisorManager.register_task(
                SupervisorTask_SingleAgent(
                    name="inquiry_scanner_task",
                    tools=[],
                    prompt="You are a inquiry scanningexpert. You have tools "
                )
            )


        
            self.inquiry_analyzer_task = SupervisorManager.register_task(
                SupervisorTask_SingleAgent(
                    name="inquiry_analyzer_task",
                    tools=[],
                    prompt="You are a inquiry analysis expert. You have tools "
                )
            )   

        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(
                SupervisorSupervisor(
                    name="inquiry_expert_supervisor",
                    prompt="You are a inquiry expert supervisor. Your job is to help users extract and analyze information from inquires. ."
                )
            ) \
            .add_task(self.inquiry_scanner_task) \
            .add_task(self.inquiry_analyzer_task) \
            .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()