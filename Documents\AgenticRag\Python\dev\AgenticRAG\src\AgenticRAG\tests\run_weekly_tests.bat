@echo off
echo ========================================
echo WEEKLY TESTING SUITE - AgenticRAG
echo ========================================
echo Running complete weekly testing methodology...
echo.

:: Get start time
set START_TIME=%time%
set START_DATE=%date%

:: Create master results directory
if not exist "test_results" mkdir test_results
if not exist "test_results\weekly" mkdir test_results\weekly

:: Initialize exit codes
set MONDAY_EXIT=0
set TUESDAY_EXIT=0
set WEDNESDAY_EXIT=0
set THURSDAY_EXIT=0
set FRIDAY_EXIT=0

echo ========================================
echo MONDAY - UNIT TESTING
echo ========================================
echo Running automated unit test suite...
echo.
call test_monday_unit.bat auto
set MONDAY_EXIT=%ERRORLEVEL%

echo.
echo ========================================
echo TUESDAY - INTEGRATION TESTING  
echo ========================================
echo Running integration test suite...
echo.
call test_tuesday_integration.bat auto
set TUESDAY_EXIT=%ERRORLEVEL%

echo.
echo ========================================
echo WEDNESDAY - MANUAL TESTING
echo ========================================
echo Manual testing phase - please follow WEDNESDAY_MANUAL_TESTING.md
echo.
echo IMPORTANT: Manual testing cannot be automated.
echo Please complete the manual testing checklist in:
echo   WEDNESDAY_MANUAL_TESTING.md
echo.
echo Manual testing covers:
echo - Scheduled task lifecycle testing
echo - RAG system accuracy validation  
echo - System interaction and conversation flow
echo - Error handling and recovery testing
echo - System capability exploration
echo - Stress testing and edge cases
echo - System restart and persistence verification
echo.
set /p MANUAL_COMPLETE=Has manual testing been completed? (Y/N): 
if /i "%MANUAL_COMPLETE%"=="Y" (
    echo Manual testing marked as COMPLETED
    set WEDNESDAY_EXIT=0
) else (
    echo Manual testing marked as INCOMPLETE
    set WEDNESDAY_EXIT=1
)

echo.
echo ========================================
echo THURSDAY - PERFORMANCE TESTING
echo ========================================
echo Running performance test suite...
echo.
call test_thursday_performance.bat auto
set THURSDAY_EXIT=%ERRORLEVEL%

echo.
echo ========================================
echo FRIDAY - SYSTEM HEALTH CHECK
echo ========================================
echo Running system health monitoring...
echo.
call test_friday_health.bat auto
set FRIDAY_EXIT=%ERRORLEVEL%

:: Calculate overall results
set /a TOTAL_EXIT=%MONDAY_EXIT% + %TUESDAY_EXIT% + %WEDNESDAY_EXIT% + %THURSDAY_EXIT% + %FRIDAY_EXIT%

:: Generate comprehensive weekly report
echo.
echo ========================================
echo WEEKLY TESTING SUMMARY REPORT
echo ========================================
echo Testing Period: %START_DATE% %START_TIME% to %date% %time%
echo.

echo DAILY TEST RESULTS:
echo ----------------------------------------
if %MONDAY_EXIT% == 0 (
    echo MONDAY ^(Unit Tests^):        PASSED
) else (
    echo MONDAY ^(Unit Tests^):        FAILED ^(Exit code: %MONDAY_EXIT%^)
)

if %TUESDAY_EXIT% == 0 (
    echo TUESDAY ^(Integration^):      PASSED  
) else (
    echo TUESDAY ^(Integration^):      FAILED ^(Exit code: %TUESDAY_EXIT%^)
)

if %WEDNESDAY_EXIT% == 0 (
    echo WEDNESDAY ^(Manual^):         COMPLETED
) else (
    echo WEDNESDAY ^(Manual^):         INCOMPLETE
)

if %THURSDAY_EXIT% == 0 (
    echo THURSDAY ^(Performance^):     PASSED
) else (
    echo THURSDAY ^(Performance^):     FAILED ^(Exit code: %THURSDAY_EXIT%^)
)

if %FRIDAY_EXIT% == 0 (
    echo FRIDAY ^(Health Check^):      HEALTHY
) else (
    echo FRIDAY ^(Health Check^):      ISSUES DETECTED
)

echo.
echo OVERALL WEEKLY STATUS:
echo ----------------------------------------
if %TOTAL_EXIT% == 0 (
    echo STATUS: ALL TESTS PASSED
    echo The AgenticRAG system is healthy and all tests completed successfully.
    echo The system is ready for production use.
) else (
    echo STATUS: SOME TESTS FAILED
    echo Issues detected that require attention before production deployment.
    echo Please review individual test results for specific problems.
)

:: Test coverage summary
echo.
echo TEST COVERAGE SUMMARY:
echo ----------------------------------------
echo - Unit Tests: Database, Supervisor Framework, RAG System, OAuth
echo - Integration Tests: Multi-service coordination, Database coordination, Workflows
echo - Manual Tests: User interaction, Scheduled tasks, System capabilities
echo - Performance Tests: Response times, Memory usage, Concurrent load
echo - Health Checks: System resources, Configuration, Application startup

:: Generate consolidated report
echo.
echo GENERATING CONSOLIDATED REPORT...
..\..\..\.venv\Scripts\python.exe -c "
import json
import time
import os
from datetime import datetime

# Collect all test results
weekly_report = {
    'test_date': '%START_DATE%',
    'start_time': '%START_TIME%',
    'end_time': '%time%',
    'results': {
        'monday_unit': {'exit_code': %MONDAY_EXIT%, 'status': 'PASSED' if %MONDAY_EXIT% == 0 else 'FAILED'},
        'tuesday_integration': {'exit_code': %TUESDAY_EXIT%, 'status': 'PASSED' if %TUESDAY_EXIT% == 0 else 'FAILED'},
        'wednesday_manual': {'exit_code': %WEDNESDAY_EXIT%, 'status': 'COMPLETED' if %WEDNESDAY_EXIT% == 0 else 'INCOMPLETE'},
        'thursday_performance': {'exit_code': %THURSDAY_EXIT%, 'status': 'PASSED' if %THURSDAY_EXIT% == 0 else 'FAILED'},
        'friday_health': {'exit_code': %FRIDAY_EXIT%, 'status': 'HEALTHY' if %FRIDAY_EXIT% == 0 else 'ISSUES'}
    },
    'overall': {
        'total_exit_code': %TOTAL_EXIT%,
        'status': 'PASSED' if %TOTAL_EXIT% == 0 else 'FAILED',
        'tests_passed': sum(1 for code in [%MONDAY_EXIT%, %TUESDAY_EXIT%, %WEDNESDAY_EXIT%, %THURSDAY_EXIT%, %FRIDAY_EXIT%] if code == 0),
        'tests_failed': sum(1 for code in [%MONDAY_EXIT%, %TUESDAY_EXIT%, %WEDNESDAY_EXIT%, %THURSDAY_EXIT%, %FRIDAY_EXIT%] if code != 0)
    }
}

# Save consolidated report
timestamp = datetime.now().strftime('%%Y%%m%%d_%%H%%M%%S')
report_filename = f'test_results/weekly/weekly_report_{timestamp}.json'

with open(report_filename, 'w') as f:
    json.dump(weekly_report, f, indent=2)

print(f'Consolidated weekly report saved to: {report_filename}')

# Generate summary statistics
print('\\nWEEKLY TESTING STATISTICS:')
print(f'Tests Passed: {weekly_report['overall']['tests_passed']}/5')
print(f'Tests Failed: {weekly_report['overall']['tests_failed']}/5')
print(f'Success Rate: {(weekly_report['overall']['tests_passed'] / 5) * 100:.1f}%%')
"

echo.
echo AVAILABLE REPORTS:
echo ----------------------------------------
echo - Unit Test Results: test_results\unit_test_results.xml
echo - Integration Test Results: test_results\integration_test_results.xml  
echo - Performance Test Results: test_results\performance\
echo - Health Check Results: test_results\health\
echo - Weekly Summary: test_results\weekly\weekly_report_*.json
echo - Manual Testing Guide: WEDNESDAY_MANUAL_TESTING.md

echo.
echo NEXT STEPS:
echo ----------------------------------------
if %TOTAL_EXIT% == 0 (
    echo 1. Review all test reports for performance trends
    echo 2. Update documentation if needed
    echo 3. Schedule next weekly testing cycle
    echo 4. System is ready for continued operation
) else (
    echo 1. PRIORITY: Address failed tests immediately
    echo 2. Review specific error logs and outputs
    echo 3. Fix identified issues and re-run failed tests
    echo 4. Document any workarounds or fixes applied
    echo 5. Re-run complete weekly test cycle after fixes
)

echo.
echo ========================================
echo Weekly testing cycle completed on %date% at %time%
echo ========================================

pause
exit /b %TOTAL_EXIT%