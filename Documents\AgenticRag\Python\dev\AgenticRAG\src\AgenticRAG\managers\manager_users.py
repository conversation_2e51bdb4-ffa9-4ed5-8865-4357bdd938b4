from imports import *

from asyncio import Lock as asyncio_Lock
from typing import Dict, Optional, List
import logging
from uuid import uuid4, UUID

from userprofiles.permission_levels import PERMISSION_LEVELS

class ZairaUserManager:
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.ZairaUser import ZairaUser

    _instance: "ZairaUserManager" = None
    lock: asyncio_Lock = None
    users = Dict[str, "ZairaUser"]

    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    def __init__(self):
        self.users = {}
        self.lock = asyncio_Lock()

    @classmethod
    def get_instance(cls) -> "ZairaUserManager":
        if cls._instance is None:
            cls.logger.info("Creating a new singleton instance of ZairaUserManager.")
            cls._instance = cls()
        return cls._instance

    @classmethod
    async def add_user(cls, username: str, rank: PERMISSION_LEVELS, guid: UUID, device_guid: UUID) -> "ZairaUser":
        self = cls.get_instance()
        async with self.lock:
            if guid in self.users:
                cls.logger.error(f"Attempted to add user with existing GUID: {guid}")
                raise ValueError(f"User with GUID {guid} already exists.")
            from userprofiles.ZairaUser import ZairaUser
            user = ZairaUser(username, rank, guid, device_guid)
            self.users[str(guid)] = user
            cls.logger.info(f"User added: {username} with GUID: {guid}")
            
            # Resume any paused scheduled tasks for this user
            from managers.manager_scheduled_tasks import get_persistence_manager
            try:
                persistence_manager = await get_persistence_manager()
                await persistence_manager.resume_user_tasks(str(guid))
            except Exception as e:
                cls.logger.warning(f"Failed to resume scheduled tasks for user {guid}: {str(e)}")
            
            return user
        
    @classmethod
    def create_guid(cls) -> UUID:
        new_guid = uuid4()
        cls.logger.info(f"Generated new User GUID: {str(new_guid)}")
        return new_guid

    @classmethod
    async def remove_user(cls, guid: str) -> bool:
        self = cls.get_instance()
        async with self.lock:
            if guid in self.users:
                del self.users[guid]
                cls.logger.info(f"User removed: GUID {guid}")
                return True
            cls.logger.warning(f"Attempted to remove non-existent user: GUID {guid}")
            return False

    @classmethod
    async def find_user(cls, guid: str) -> Optional["ZairaUser"]:
        self = cls.get_instance()
        async with self.lock:
            user = self.users.get(guid)
            if user:
                cls.logger.info(f"User found: GUID {guid}")
            else:
                cls.logger.warning(f"User not found: GUID {guid}")
            return user
        
    @classmethod
    async def get_user(cls, username: str) -> Optional["ZairaUser"]:
        self = cls.get_instance()
        async with self.lock:
            for user in self.users.values():
                if user.username == username:
                    cls.logger.info(f"User found by username: {username}")
                    return user
            cls.logger.warning(f"User not found by username: {username}")
            return None

    @classmethod
    async def update_user(cls, guid: str, **kwargs) -> Optional["ZairaUser"]:
        self = cls.get_instance()
        async with self.lock:
            user = self.users.get(guid)
            if user:
                for key, value in kwargs.items():
                    if hasattr(user, key):
                        setattr(user, key, value)
                        cls.logger.info(f"Updated {key} for user GUID {guid}")
                    else:
                        cls.logger.warning(f"Attribute {key} does not exist for user GUID {guid}")
                return user
            cls.logger.warning(f"User not found for update: GUID {guid}")
            return None

    @classmethod
    async def list_users(cls) -> List["ZairaUser"]:
        self = cls.get_instance()
        async with self.lock:
            users_list = list(self.users.values())
            cls.logger.info(f"Listed {len(users_list)} users")
            return users_list

    @classmethod
    async def find_users_by_rank(cls, rank: PERMISSION_LEVELS) -> List["ZairaUser"]:
        self = cls.get_instance()
        async with self.lock:
            users_with_rank = [user for user in self.users.values() if user.rank == rank]
            cls.logger.info(f"Found {len(users_with_rank)} users with rank {rank}")
            return users_with_rank

    @classmethod
    async def user_exists(cls, guid: str) -> bool:
        self = await cls.get_instance()
        async with self.lock:
            exists = guid in self.users
            cls.logger.info(f"User existence check: GUID {guid} - {'exists' if exists else 'does not exist'}")
            return exists
