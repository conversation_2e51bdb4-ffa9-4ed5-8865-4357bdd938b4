# WEDNESDAY MANUAL TESTING GUIDE
## Comprehensive Manual System Testing for AgenticRAG

**Date:** _____________________  
**Tester:** ___________________  
**Version:** __________________  
**Duration:** 60-90 minutes  

---

## PRE-TESTING SETUP

### Environment Preparation
1. **Start the Application:**
   ```bash
   ../../.venv/Scripts/python.exe main.py
   ```

2. **Verify Claude Environment Detection:**
   - [ ] Application should auto-detect Claude environment
   - [ ] Debug mode should be enabled automatically
   - [ ] OAuth external service should be skipped
   - [ ] Discord/communication bots should be disabled

3. **Initial System Check:**
   - [ ] Application starts without errors
   - [ ] Interactive prompt appears: "Please ask your company-specific question:"
   - [ ] No critical error messages in console

---

## SECTION 1: SCHEDULED TASK SYSTEM TESTING (25 minutes)

### 1.1 Basic Scheduled Task Creation
**Objective:** Test natural language scheduled task creation

**Test Cases:**

**Test 1.1.1 - Simple Hourly Task**
1. **Input:** `"schedule a task to check email every hour"`
2. **Expected Results:**
   - [ ] Task created successfully
   - [ ] Task ID generated and displayed
   - [ ] Schedule parsed correctly as "every hour"
   - [ ] Task shows as active
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

**Test 1.1.2 - Daily Scheduled Task**
1. **Input:** `"schedule a task to send me a report every day at 9am"`
2. **Expected Results:**
   - [ ] Task created successfully
   - [ ] Schedule parsed correctly as "daily at 9am"
   - [ ] Time component recognized
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

**Test 1.1.3 - Complex Weekly Task**
1. **Input:** `"schedule a task to backup files every Monday at 5pm"`
2. **Expected Results:**
   - [ ] Task created successfully
   - [ ] Weekly schedule recognized
   - [ ] Specific day and time parsed
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

### 1.2 Task Management Operations
**Objective:** Test scheduled task management capabilities

**Test 1.2.1 - List Scheduled Tasks**
1. **Input:** `"list my scheduled tasks"`
2. **Expected Results:**
   - [ ] All created tasks displayed
   - [ ] Task IDs, schedules, and status shown
   - [ ] Active vs inactive tasks distinguished
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

**Test 1.2.2 - Task Cancellation**
1. **Input:** `"cancel my scheduled task [use task ID from previous test]"`
2. **Expected Results:**
   - [ ] Task cancelled successfully
   - [ ] Confirmation message displayed
   - [ ] Task no longer appears in active list
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

### 1.3 Advanced Scheduling Patterns
**Objective:** Test complex scheduling patterns

**Test 1.3.1 - Business Hours Scheduling**
1. **Input:** `"schedule a task to monitor system every hour during business hours"`
2. **Expected Results:**
   - [ ] Business hours constraint recognized
   - [ ] Hourly frequency within constraints
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

**Test 1.3.2 - Monthly Scheduling**
1. **Input:** `"schedule a task to generate monthly report on the first day of each month"`
2. **Expected Results:**
   - [ ] Monthly frequency recognized
   - [ ] "First day" constraint parsed
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

---

## SECTION 2: RAG SYSTEM TESTING (20 minutes)

### 2.1 Document Query and Retrieval
**Objective:** Test RAG system accuracy and performance

**Test 2.1.1 - Basic Information Retrieval**
1. **Input:** `"tell me about your capabilities"`
2. **Expected Results:**
   - [ ] Relevant information retrieved
   - [ ] Response generated within 5 seconds
   - [ ] Accurate information about system features
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

**Test 2.1.2 - Specific Feature Query**
1. **Input:** `"how do I set up email automation?"`
2. **Expected Results:**
   - [ ] Specific email automation information
   - [ ] Step-by-step guidance provided
   - [ ] References to relevant system components
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

**Test 2.1.3 - System Architecture Query**
1. **Input:** `"explain how the supervisor system works"`
2. **Expected Results:**
   - [ ] LangGraph supervisor information
   - [ ] Multi-agent coordination explanation
   - [ ] Technical details appropriate to query
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

### 2.2 Query Performance Testing
**Objective:** Test RAG system response times and accuracy

**Test 2.2.1 - Response Time Measurement**
1. **Input:** `"find information about database operations"`
2. **Measurements:**
   - [ ] Response time < 5 seconds
   - [ ] Relevant results returned
   - [ ] No timeout errors
3. **Actual Response Time:** ________________________
4. **Status:** [ ] PASS [ ] FAIL

**Test 2.2.2 - Complex Multi-part Query**
1. **Input:** `"I need to schedule tasks and also understand how OAuth authentication works"`
2. **Expected Results:**
   - [ ] Both topics addressed in response
   - [ ] Clear separation of information
   - [ ] Comprehensive coverage
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

---

## SECTION 3: SYSTEM INTERACTION TESTING (15 minutes)

### 3.1 Conversation Flow Testing
**Objective:** Test multi-turn conversation handling

**Test 3.1.1 - Follow-up Questions**
1. **First Input:** `"tell me about scheduled tasks"`
2. **Follow-up:** `"how do I cancel one?"`
3. **Expected Results:**
   - [ ] Context maintained between queries
   - [ ] Follow-up answered appropriately
   - [ ] Reference to previous conversation
4. **Actual Results:** ________________________________
5. **Status:** [ ] PASS [ ] FAIL

**Test 3.1.2 - Task Creation Follow-up**
1. **First Input:** `"schedule a task to check logs daily"`
2. **Follow-up:** `"what's the task ID for that?"`
3. **Expected Results:**
   - [ ] Task ID provided
   - [ ] Reference to just-created task
   - [ ] Context awareness demonstrated
4. **Actual Results:** ________________________________
5. **Status:** [ ] PASS [ ] FAIL

### 3.2 Error Handling Testing
**Objective:** Test system error handling and recovery

**Test 3.2.1 - Invalid Schedule Format**
1. **Input:** `"schedule a task to do something at invalid time"`
2. **Expected Results:**
   - [ ] Error message displayed
   - [ ] Helpful guidance provided
   - [ ] System remains responsive
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

**Test 3.2.2 - Ambiguous Request**
1. **Input:** `"do the thing"`
2. **Expected Results:**
   - [ ] Clarification requested
   - [ ] Helpful suggestions provided
   - [ ] No system crash
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

---

## SECTION 4: SYSTEM CAPABILITY EXPLORATION (15 minutes)

### 4.1 Feature Discovery
**Objective:** Test system's ability to explain its capabilities

**Test 4.1.1 - General Capabilities Query**
1. **Input:** `"what can you help me with?"`
2. **Expected Results:**
   - [ ] Comprehensive capability list
   - [ ] Clear explanations of features
   - [ ] Examples of usage provided
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

**Test 4.1.2 - Communication Channels Query**
1. **Input:** `"what communication channels do you support?"`
2. **Expected Results:**
   - [ ] Discord, Slack, Teams, WhatsApp mentioned
   - [ ] Current status (Claude mode) explained
   - [ ] Available features described
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

### 4.2 Technical Information Testing
**Objective:** Test system's technical knowledge

**Test 4.2.1 - Architecture Information**
1. **Input:** `"explain your technical architecture"`
2. **Expected Results:**
   - [ ] LangGraph and LlamaIndex mentioned
   - [ ] Database information provided
   - [ ] Technical details appropriate
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

**Test 4.2.2 - Integration Information**
1. **Input:** `"what external services do you integrate with?"`
2. **Expected Results:**
   - [ ] OAuth services listed
   - [ ] Database systems mentioned
   - [ ] Communication platforms described
3. **Actual Results:** ________________________________
4. **Status:** [ ] PASS [ ] FAIL

---

## SECTION 5: STRESS AND EDGE CASE TESTING (10 minutes)

### 5.1 Input Validation Testing
**Objective:** Test system handling of edge cases

**Test 5.1.1 - Very Long Input**
1. **Input:** `[Create a very long query with 500+ characters about multiple topics]`
2. **Expected Results:**
   - [ ] System handles long input gracefully
   - [ ] Response addresses main points
   - [ ] No truncation errors
3. **Status:** [ ] PASS [ ] FAIL

**Test 5.1.2 - Special Characters**
1. **Input:** `"schedule task @#$%^&*() every !!! minutes"`
2. **Expected Results:**
   - [ ] System handles special characters
   - [ ] Extracts meaningful content
   - [ ] No parsing errors
3. **Status:** [ ] PASS [ ] FAIL

### 5.2 Rapid Fire Testing
**Objective:** Test system responsiveness under quick inputs

**Test 5.2.1 - Quick Sequential Queries**
1. **Rapidly Input 5 queries:**
   - `"list tasks"`
   - `"schedule email check"`
   - `"what can you do"`
   - `"cancel last task"`
   - `"help me"`
2. **Expected Results:**
   - [ ] All queries processed
   - [ ] Responses maintain quality
   - [ ] No system delays
3. **Status:** [ ] PASS [ ] FAIL

---

## SYSTEM RESTART TESTING (5 minutes)

### 6.1 Persistence Verification
**Objective:** Test scheduled task persistence across restarts

**Test 6.1.1 - Pre-Restart Task Creation**
1. **Create 2-3 scheduled tasks** (record their IDs)
2. **Stop the application** (Ctrl+C)
3. **Restart the application**
4. **Query:** `"list my scheduled tasks"`
5. **Expected Results:**
   - [ ] All tasks from before restart are listed
   - [ ] Task IDs match previous session
   - [ ] Active status preserved
6. **Status:** [ ] PASS [ ] FAIL

---

## TESTING SUMMARY

### Overall Test Results
- **Total Tests:** 25
- **Tests Passed:** _____ / 25
- **Tests Failed:** _____ / 25
- **Pass Rate:** _____%

### Critical Issues Found
1. ________________________________________________
2. ________________________________________________
3. ________________________________________________

### Minor Issues Found
1. ________________________________________________
2. ________________________________________________
3. ________________________________________________

### Performance Observations
- **Average Response Time:** ___________________
- **Slowest Response:** _______________________
- **System Stability:** _______________________

### Feature Completeness Assessment
- [ ] Scheduled Task System: Fully Functional
- [ ] RAG System: Fully Functional
- [ ] Conversation Handling: Fully Functional
- [ ] Error Recovery: Fully Functional
- [ ] System Persistence: Fully Functional

### Recommendations
1. ________________________________________________
2. ________________________________________________
3. ________________________________________________

### Next Steps
- [ ] Address critical issues immediately
- [ ] Schedule fixes for minor issues
- [ ] Document any workarounds needed
- [ ] Update user documentation if needed

---

**Testing Completed:** _____________________ (Date/Time)  
**Tester Signature:** ____________________  

**Notes:**
___________________________________________________________________
___________________________________________________________________
___________________________________________________________________