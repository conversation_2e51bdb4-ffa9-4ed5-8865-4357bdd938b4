from imports import *

from os import path as os_path
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTask_SingleAgent, SupervisorTaskState
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_meltano import MeltanoManager
from managers.manager_users import ZairaUserManager

class SupervisorTask_IMAP(SupervisorTask_Base):

    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        import imaplib
        import email
        from email.header import decode_header

        # Configuration
        IMAP_SERVER = await OAuth2Verifier.get_token("imap", "access_token")  # Change for other providers
        EMAIL_ACCOUNT = await OAuth2Verifier.get_token("imap", "refresh_token")
        IMAP_PORT = await OAuth2Verifier.get_token("imap", "expires_in")
        PASSWORD = await OAuth2Verifier.get_token("imap", "token_type")#"your_app_password"  # Use an App Password if 2FA is enabled
        if Globals.is_docker():
            path = '/meltano/output'
        else:
            from os import getcwd
            path = getcwd() + '/src/meltano/output'

        # Determine SSL usage from port (993 is standard SSL)
        USE_SSL = IMAP_PORT == 993

        # Connect based on SSL flag
        if USE_SSL:
            mail = imaplib.IMAP4_SSL(IMAP_SERVER, IMAP_PORT)
        else:
            mail = imaplib.IMAP4(IMAP_SERVER, IMAP_PORT)

        # Login to your account
        try:
            mail.login(EMAIL_ACCOUNT, PASSWORD)
        except Exception as e:
            # Some mail providers don't allow the extension as the username
            mail.login(EMAIL_ACCOUNT.rsplit(".", 1)[0], PASSWORD)

        # Select the mailbox you want to use
        mail.select("inbox")  # or 'ALL', 'INBOX', 'Sent', etc.

        # Search for all emails
        status, messages = mail.search(None, "ALL")

        # messages is a space-separated string of email IDs
        email_ids = messages[0].split()

        # Fetch and save each email
        for i, num in enumerate(email_ids, 1):
            status, data = mail.fetch(num, "(RFC822)")
            raw_email = data[0][1]

            # Save as .eml file
            filename = os_path.join(path, f"email_{i:04d}.eml")
            with open(filename, "wb") as f:
                f.write(raw_email)

            print(f"Saved: {filename}")

        # Logout
        mail.logout()

        # Mails retrieved, convert to vector store
        user = await ZairaUserManager.find_user(state.user_guid)
        await MeltanoManager.ConvertFilesToVectorStore(path, user)

        return ""

async def create_task_imap_receiver() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_IMAP(name="imap_retrieval_task", prompt_id="Task_IMAP"))
