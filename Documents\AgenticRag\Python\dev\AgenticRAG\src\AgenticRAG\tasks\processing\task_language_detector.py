from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from langchain_core.tools import tool

from managers.manager_supervisors import SupervisorManager, SupervisorTask_SingleAgent, SupervisorTaskState
from managers.manager_prompts import Prompt<PERSON>anager

@tool
def detect_language(query: str, state: SupervisorTaskState):
    """Detects the language of the input text"""
    task = SupervisorManager.get_task("language_detector")
    result = task.model.invoke(f"{PromptManager.get_prompt('Task_Language_Detector_Tool_Detect_Langage')}: {query}")
    if isinstance(result, etc.helper_functions.get_any_message_as_type()):
        result = result.content
    return result

async def create_task_language_detector() -> SupervisorTask_SingleAgent:
    return SupervisorManager.register_task(SupervisorTask_SingleAgent(name="language_detector", tools=[], prompt_id="Task_Language_Detector"))
