# setup.py
from imports import *

#HKEY_LOCAL_MACHINE\SYSTEM\CurrentControlSet\Control\FileSystem\LongPathsEnabled = 1, then restart computer
from llama_index.embeddings.huggingface_optimum import OptimumEmbedding
import logging
from sys import stdout
from pathlib import Path

from managers.manager_logfire import LogFire
from managers.manager_postgreSQL import PostgreSQLManager
from managers.manager_qdrant import QDrantManager
from managers.manager_retrieval import RetrievalManager
from managers.manager_meltano import MeltanoManager
from managers.manager_agno import AgnoManager
from managers.manager_supervisors import SupervisorManager
from managers.manager_scheduled_tasks import get_persistence_manager
from managers.manager_multimodal import MultimodalManager
from endpoints.api_endpoint import APIEndpoint
from endpoints.oauth._verifier_ import OAuth2Verifier
# Conditional imports for communication endpoints
import os
from etc.helper_functions import is_claude_environment

if not is_claude_environment():
    from endpoints.discord_endpoint import MyDiscordBot
    from endpoints.teams_endpoint import MyTeamsBot
    from endpoints.slack_endpoint import MySlackBot
    from endpoints.whatsapp_endpoint import MyWhatsappBot
else:
    # Create mock classes when in Claude environment
    class MockBot:
        @staticmethod
        async def setup(): pass
        @staticmethod
        async def late_setup(): pass
    
    MyDiscordBot = MockBot
    MyTeamsBot = MockBot
    MySlackBot = MockBot
    MyWhatsappBot = MockBot
from fastembed import SparseTextEmbedding

# embedding_generate.py
from imports import *
from etc.helper_functions import *

from llama_index.core import (
    VectorStoreIndex,
    SimpleDirectoryReader,
    StorageContext,
    load_index_from_storage,
)
from qdrant_client.http import models
from llama_index.vector_stores.qdrant import QdrantVectorStore
from managers.manager_qdrant import QDrantManager
from managers.manager_postgreSQL import PostgreSQLManager

from llama_index.embeddings.fastembed import FastEmbedEmbedding
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.core import VectorStoreIndex

from tasks.task_top_level_supervisor import create_top_level_supervisor
from tasks.task_top_output_supervisor import create_top_output_supervisor
from tasks.processing.task_convert_to_language import create_task_language_verifier

from tasks.task_top_level_supervisor import create_top_level_supervisor
from tasks.task_top_output_supervisor import create_top_output_supervisor
from tasks.processing.task_convert_to_language import create_task_language_verifier

async def init(newProject: bool, DATA_DIR, PERSIST_DIR, parsers):
    await LogFire.setup() # LogFire needs to be initialised before anything that might use it!
    logging.basicConfig(stream=stdout, level=logging.DEBUG if ZairaSettings.IsDebugMode else logging.INFO)
    logging.getLogger().addHandler(logging.StreamHandler(stream=stdout))

    folder = Path(BASE_DIR() / "bge_onnx")
    if not folder.exists() or not folder.is_dir():
        OptimumEmbedding.create_and_save_optimum_model(
            EMBEDDING_MODEL, str(BASE_DIR() / "bge_onnx")
        )

    ZairaSettings.sparse_embed_model = SparseTextEmbedding(model_name="Qdrant/bm25",output_name="sparse")
    ZairaSettings.OllamaSettings().embed_model = OptimumEmbedding(folder_name=str(BASE_DIR() / "bge_onnx"))
    #if Globals.get_debug() == True:
    from langchain_openai import ChatOpenAI
    llm = ChatOpenAI(model_name="gpt-4o-mini", temperature=0.5, timeout=TIMEOUT_LIMIT)
    #else:
    #   from langchain_ollama.llms import OllamaLLM
    #    llm = OllamaLLM(model=LLM_MODEL, temperature=0.5, timeout=TIMEOUT_LIMIT)
    ZairaSettings.llm = llm
    ZairaSettings.OllamaSettings().llm = llm

    await PostgreSQLManager.setup()
    await APIEndpoint.setup()
    await OAuth2Verifier.setup()
    Globals.set_debug_values(OAuth2Verifier.get_token("debug", "access_token"))
    #Once OAuth is set-up including the mandatory SQL and HTTP, set up the rest

    await QDrantManager.setup()
    await RetrievalManager.setup()
    await MultimodalManager.setup()
    await MeltanoManager.setup()
    # poetry remove llama-index-llms-langchain
    await SupervisorManager.setup()
    await AgnoManager.setup()
    
    await MyDiscordBot.setup()
    await MyTeamsBot.setup()
    await MySlackBot.setup()
    await MyWhatsappBot.setup()
    
    # Initialize scheduled task persistence and recovery
    await get_persistence_manager()

    await APIEndpoint.late_setup()
    await MySlackBot.late_setup()
    
    if newProject:
        index = await generateEmbedding(DATA_DIR=DATA_DIR, PERSIST_DIR=PERSIST_DIR, parsers=parsers)
    else:
        index = await loadEmbedding(DATA_DIR=DATA_DIR, PERSIST_DIR=PERSIST_DIR, parsers=parsers)
    Globals.set_index(index)
    
    await late_init()

async def late_init():
    await create_top_level_supervisor()
    await create_top_output_supervisor()
    #await create_task_language_verifier()
    LogFire.log("INIT", "Setup has completed.")

# class BGEEmbeddings(BaseEmbedding):
#     def __init__(self):
#         super().__init__()
#         model_name = model_name = OptimumEmbedding(folder_name=str(BASE_DIR() / "bge_onnx"))
#         #model_name = FastEmbedEmbedding(model_name="BAAI/bge-base-en-v1.5")
#         BGEEmbeddings.model_name = model_name

#     def _get_text_embedding(self, text: str) -> List[float]:
#         """Generate embedding for a single text string."""
#         return BGEEmbeddings.model_name.get_text_embedding(text)

#     def _get_query_embedding(self, query: str) -> List[float]:
#         """Generate embedding for a query (same as text embedding here)."""
#         return BGEEmbeddings.model_name.get_text_embedding(query)

#     def _get_text_embeddings(self, texts: List[str]) -> List[List[float]]:
#         """Generate embeddings for a list of texts."""
#         return [BGEEmbeddings.model_name.get_text_embedding(text) for text in texts]

#     async def _aget_text_embedding(self, text: str) -> List[float]:
#         """Async version of text embedding (optional, for async support)."""
#         return BGEEmbeddings._get_text_embedding(text)

#     async def _aget_query_embedding(self, query: str) -> List[float]:
#         """Async version of query embedding."""
#         return BGEEmbeddings._get_query_embedding(query)




async def generateEmbedding(DATA_DIR, PERSIST_DIR, parsers):
    #bge_embedding = BGEEmbeddings()
    print("Generating index. This may take a WHILE!")
    from managers.manager_prompts import PromptManager
    await PromptManager.setDefaultPrompts()
    if etc.helper_functions.folder_has_files(DATA_DIR):
        documents = SimpleDirectoryReader(DATA_DIR,recursive=True, file_extractor=parsers.file_extractor).load_data()

    if Globals.is_docker() == False:
        await PostgreSQLManager.delete_database("meltanodb")
        await PostgreSQLManager.create_database("meltanodb")
        from subprocess import run as subprocess_run
        result = subprocess_run('cmd /c playwright install', shell=True, capture_output=True, text=True)
    
    await QDrantManager.GetAsyncClient().delete_collection(collection_name="mainCollection")
    try:
        await QDrantManager.GetAsyncClient().create_collection(
            collection_name="mainCollection",
            vectors_config={
                "text-dense": models.VectorParams( # We kunnen "dense" gebruiken als naam voor de dichte vector
                    size=EMBEDDING_SIZE,
                    distance=models.Distance.COSINE,
                )
            },
            sparse_vectors_config={
                "text-sparse": models.SparseVectorParams() # We gebruiken "sparse" als naam voor de sparse vector, wat de standaard output_name is
                  
                    
                
            },
            hnsw_config=models.HnswConfigDiff(m=16, ef_construct=100)
        )
    except Exception as e:
        # Does cause an error on the server since we use client AND aclient, but doesn't actually cause a problem
        # @Simon: The error itself is a warning from what I can tell? An actual error would stop the create_collection trigger?
        etc.helper_functions.exception_triggered(e)

    # Setup the ONNX embedding model


    # Setup the sparse BM25 embedding model
    #sparse_embed_model = FastEmbedEmbedding(model_name="Qdrant/bm25")

    vector_store = QdrantVectorStore(client=QDrantManager.GetClient(), aclient=QDrantManager.GetAsyncClient(), collection_name="mainCollection",enable_hybrid=True,sparse_encoder=ZairaSettings.sparse_embed_model,batch_size=20,vector_field_name="text-dense",sparse_vector_field_name="text-sparse")
    storage_context = StorageContext.from_defaults(vector_store=vector_store)
    # Create index from documents using the ONNX embedding model
    index = VectorStoreIndex.from_documents(
        documents,
        storage_context=storage_context,
        embed_model=ZairaSettings.OllamaSettings().embed_model,
        vector_store=vector_store,
        vector_field_name="text-dense"
    )
    #index = VectorStoreIndex([], storage_context=storage_context, embed_model=ZairaSettings.OllamaSettings().embed_model)
    index.set_index_id(index_id="mainIndex")
    # Manually embed each document before adding to ChromaDB
    #new_nodes = []
    #if helper_functions.folder_has_files(DATA_DIR):
    #    for i, doc in enumerate(documents):
    #        text = str(doc.text) if doc.text is not None else ""
    #        embedding = bge_embedding([text])
    #        embedding = embedding[0]  # Ensure this returns the correct format for Chroma
    #        new_nodes.append(TextNode(text=text, id_=f"new-id-{i}", metadata=doc.metadata, embedding=embedding))
    #vector_store.add(new_nodes)  # Or use async_add if you're dealing with asynchronous operations



    index.storage_context.persist(persist_dir=str(PERSIST_DIR))

    print("Index saved to SSD")
    return index

async def loadEmbedding(DATA_DIR, PERSIST_DIR, parsers):
    #bge_embedding = BGEEmbeddings()
    print("Loading stored index")
    from managers.manager_prompts import PromptManager
    await PromptManager.loadDefaultPrompts()

    vector_store = QdrantVectorStore(client=QDrantManager.GetClient(), aclient=QDrantManager.GetAsyncClient(), collection_name="mainCollection",enable_hybrid=True,sparse_encoder=ZairaSettings.sparse_embed_model,batch_size=20,vector_field_name="text-dense",sparse_vector_field_name="text-sparse")

    # Create a fresh storage context that only uses the vector store
    # Don't load from persisted docstore/index_store as they may be outdated after crawling
    storage_context = StorageContext.from_defaults(vector_store=vector_store)

    # Try to load existing index, but if it fails, create a new one from the vector store
    # try:
    #     index = load_index_from_storage(index_id="mainIndex", storage_context=storage_context)
    #     print("Index loaded from storage")
    # except Exception as e:
    #     print(f"Could not load index from storage ({e}), creating new index from vector store")
    #     # Create a new index from the existing vector store data
    #     index = VectorStoreIndex.from_vector_store(
    #         vector_store=vector_store,
    #         storage_context=storage_context,
    #         embed_model=ZairaSettings.OllamaSettings().embed_model
    #     )
    #     index.set_index_id(index_id="mainIndex")
    if etc.helper_functions.folder_has_files(DATA_DIR):
        documents = SimpleDirectoryReader(DATA_DIR,recursive=True, file_extractor=parsers.file_extractor).load_data()
    index = VectorStoreIndex.from_vector_store(
        vector_store=vector_store,
        storage_context=storage_context,
        embed_model=ZairaSettings.OllamaSettings().embed_model
    )
    index.set_index_id(index_id="mainIndex")

    # Persist the updated storage context
    # try:
    #     index.storage_context.persist(persist_dir=str(PERSIST_DIR))
    #     print("Storage context persisted")
    # except Exception as e:
    #     print(f"Warning: Could not persist storage context: {e}")

    #Check for any additional files that need to be added to the database
    #reader = SimpleDirectoryReader(DATA_DIR,recursive=True, file_extractor=parsers.file_extractor)
    #new_docs = reader.load_data()
    #new_nodes = []
    #for i, doc in enumerate(new_docs):
    #    embedding = bge_embedding([doc.text])[0]  # Ensure this returns the correct format for Chroma
    #    new_nodes.append(TextNode(text=doc.text, id_=f"new-id-{i}", metadata=doc.metadata, embedding=embedding))
    #vector_store.add(new_nodes)  # Or use async_add if you're dealing with asynchronous operations
    #vector_store.persist(persist_dir=str(PERSIST_DIR))
    print("Index loaded")
    return index
