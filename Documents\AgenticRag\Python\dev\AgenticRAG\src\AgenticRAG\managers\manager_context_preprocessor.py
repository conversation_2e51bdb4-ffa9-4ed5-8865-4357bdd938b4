"""
Context Preprocessor Manager for Supervisor Routing

This module provides sophisticated context preprocessing to solve supervisor routing 
inconsistencies by transforming chaotic conversation history into clean, structured 
input for consistent supervisor decision-making.

Key Features:
- Smart memory management to prevent context window overflow
- Conversation thread tracking and topic shift detection
- Coreference resolution and entity tracking
- Structured output format for supervisor consumption
- Configurable message limits and filtering
"""

from imports import *

import asyncio
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
from datetime import datetime
import re

from langchain_core.messages import HumanMessage, SystemMessage, AnyMessage
from langchain_core.language_models.base import BaseLanguageModel

# Import from existing managers and functions
from inputs.user_query import rag_data
from userprofiles.ZairaUser import ZairaUser
from userprofiles.ZairaMessage import ZairaMessage


class MessageClassification(Enum):
    """Classification types for conversation messages."""
    META_CONVERSATIONAL = "meta_conversational"
    CONTINUATION = "continuation" 
    NEW_TOPIC = "new_topic"
    CLARIFICATION = "clarification"
    TASK_EXECUTION = "task_execution"
    INFORMATION_SEEKING = "information_seeking"


class UserIntentType(Enum):
    """User intent classification for supervisor routing."""
    INFORMATION_SEEKING = "information_seeking"
    TASK_EXECUTION = "task_execution"
    CLARIFICATION = "clarification"
    META_DISCUSSION = "meta_discussion"


@dataclass
class ProcessedMessage:
    """Structured representation of a processed conversation message."""
    content: str
    classification: MessageClassification
    context: str
    timestamp: datetime
    is_user: bool
    entities_mentioned: List[str]
    topics: List[str]


@dataclass
class ActiveContext:
    """Active conversation context tracking."""
    entities: List[str]
    topics: List[str]
    conversation_state: str
    user_intent: UserIntentType
    established_facts: List[str]
    pending_requests: List[str]


@dataclass
class PreprocessedContext:
    """Final preprocessed context for supervisor consumption."""
    summary: str
    recent_messages: List[ProcessedMessage]
    active_context: ActiveContext
    formatted_output: str
    token_count_estimate: int


class ContextPreprocessor:
    """
    Sophisticated context preprocessor for supervisor routing optimization.
    
    Transforms raw chat history into structured, clean input for consistent
    supervisor decision-making while managing context window limits.
    """
    
    def __init__(self, llm: BaseLanguageModel):
        """Initialize the context preprocessor."""
        self.logger = logging.getLogger(__name__)
        self.llm = llm 
        
        # Configuration
        self.max_messages = 10
        self.max_relevant_messages = 5
        self.max_summary_length = 200
        self.max_total_tokens = 4000
        
        # Entity tracking patterns
        self.entity_patterns = {
            'person': re.compile(r'\b[A-Z][a-z]+ [A-Z][a-z]+\b'),
            'email': re.compile(r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'),
            'date': re.compile(r'\b(?:today|tomorrow|yesterday|\d{1,2}[/-]\d{1,2}[/-]\d{2,4})\b'),
            'project': re.compile(r'\b(?:project|task|assignment)\s+[A-Z][a-zA-Z0-9_-]+\b', re.IGNORECASE),
            'document': re.compile(r'\b[A-Za-z0-9_-]+\.(?:pdf|doc|docx|txt|md)\b')
        }
        
        # Topic keywords for classification
        self.topic_keywords = {
            'email': ['email', 'message', 'send', 'reply', 'forward'],
            'calendar': ['meeting', 'appointment', 'schedule', 'calendar', 'agenda'],
            'search': ['find', 'search', 'look', 'retrieve', 'document'],
            'analysis': ['analyze', 'review', 'summarize', 'explain', 'compare'],
            'technical': ['code', 'programming', 'debug', 'error', 'implement'],
            'meta': ['help', 'how', 'what', 'explain', 'clarify']
        }

    async def preprocess_for_supervisor(
        self,
        chat_history: List[ZairaMessage],
        current_query: str,
        user: ZairaUser,
        max_messages: Optional[int] = None,
        include_rag: bool = True
    ) -> PreprocessedContext:
        """
        Main preprocessing function that transforms chat history into structured context.
        
        Args:
            chat_history: List of conversation messages
            current_query: Current user query/message
            user: ZairaUser object for context
            max_messages: Maximum messages to process (default: self.max_messages)
            include_rag: Whether to include RAG-enhanced context
            
        Returns:
            PreprocessedContext: Structured context for supervisor consumption
        """
        try:
            max_msgs = max_messages or self.max_messages
            
            # Step 1: Extract and limit recent messages
            recent_messages = self._extract_recent_messages(chat_history, current_query, max_msgs)
            
            # Step 2: Classify and process messages
            processed_messages = await self._classify_messages(recent_messages)
            
            # Step 3: Extract entities and topics
            active_context = await self._extract_active_context(processed_messages, current_query)
            
            # Step 4: Generate conversation summary
            summary = await self._generate_summary(processed_messages, active_context)
            
            # Step 5: Enhance with RAG data if requested
            if include_rag:
                rag_context = await self._get_rag_context(current_query, user)
                active_context = self._merge_rag_context(active_context, rag_context)
            
            # Step 6: Select most relevant messages
            relevant_messages = self._select_relevant_messages(processed_messages, active_context)
            
            # Step 7: Format final output
            formatted_output = self._format_preprocessed_context(
                summary, relevant_messages, active_context, current_query
            )
            
            # Step 8: Estimate token count
            token_estimate = self._estimate_token_count(formatted_output)
            
            return PreprocessedContext(
                summary=summary,
                recent_messages=relevant_messages,
                active_context=active_context,
                formatted_output=formatted_output,
                token_count_estimate=token_estimate
            )
            
        except Exception as e:
            self.logger.error(f"Error in context preprocessing: {e}")
            # Fallback to basic preprocessing
            return await self._fallback_preprocessing(chat_history, current_query)

    def _extract_recent_messages(
        self,
        chat_history: List[ZairaMessage],
        current_query: str,
        max_messages: int
    ) -> List[Tuple[str, bool, datetime]]:
        """Extract recent messages with metadata."""
        messages = []
        
        # Add current query
        messages.append((current_query, True, datetime.now()))
        
        # Add recent chat history (most recent first)
        for msg in reversed(chat_history[-max_messages:]):
            is_user = msg.is_from_user()
            messages.append((msg.content, is_user, msg.timestamp))
            
        return messages[:max_messages]

    async def _classify_messages(
        self,
        raw_messages: List[Tuple[str, bool, datetime]]
    ) -> List[ProcessedMessage]:
        """Classify messages and extract context."""
        processed = []
        
        for i, (content, is_user, timestamp) in enumerate(raw_messages):
            # Extract entities
            entities = self._extract_entities(content)
            
            # Extract topics
            topics = self._extract_topics(content)
            
            # Classify message type
            classification = self._classify_message_type(content, i == 0, topics)
            
            # Generate context description
            context = self._generate_message_context(content, classification, entities, topics)
            
            processed.append(ProcessedMessage(
                content=content,
                classification=classification,
                context=context,
                timestamp=timestamp,
                is_user=is_user,
                entities_mentioned=entities,
                topics=topics
            ))
            
        return processed

    def _extract_entities(self, content: str) -> List[str]:
        """Extract entities from message content."""
        entities = []
        
        for entity_type, pattern in self.entity_patterns.items():
            matches = pattern.findall(content)
            for match in matches:
                entities.append(f"{entity_type}:{match}")
                
        # Simple coreference resolution
        if any(pronoun in content.lower() for pronoun in ['he', 'she', 'it', 'they', 'this', 'that']):
            entities.append("coreference_detected")
            
        return entities

    def _extract_topics(self, content: str) -> List[str]:
        """Extract topics from message content."""
        topics = []
        content_lower = content.lower()
        
        for topic, keywords in self.topic_keywords.items():
            if any(keyword in content_lower for keyword in keywords):
                topics.append(topic)
                
        return topics if topics else ['general']

    def _classify_message_type(
        self,
        content: str,
        is_current: bool,
        topics: List[str]
    ) -> MessageClassification:
        """Classify the type of message."""
        content_lower = content.lower()
        
        if is_current:
            if any(word in content_lower for word in ['what', 'how', 'why', 'when', 'where']):
                return MessageClassification.INFORMATION_SEEKING
            elif any(word in content_lower for word in ['do', 'create', 'send', 'make', 'write']):
                return MessageClassification.TASK_EXECUTION
            else:
                return MessageClassification.CONTINUATION
                
        # For previous messages
        if any(word in content_lower for word in ['clarify', 'explain', 'sorry', 'confused']):
            return MessageClassification.CLARIFICATION
        elif 'meta' in topics:
            return MessageClassification.META_CONVERSATIONAL
        elif len(set(topics) & {'email', 'calendar', 'search', 'analysis'}) > 0:
            return MessageClassification.CONTINUATION
        else:
            return MessageClassification.NEW_TOPIC

    def _generate_message_context(
        self,
        content: str,
        classification: MessageClassification,
        entities: List[str],
        topics: List[str]
    ) -> str:
        """Generate contextual description for a message."""
        context_parts = [classification.value]
        
        if entities:
            context_parts.append(f"entities: {', '.join(entities[:3])}")
        if topics:
            context_parts.append(f"topics: {', '.join(topics)}")
            
        return f"[{' | '.join(context_parts)}]"

    async def _extract_active_context(
        self,
        processed_messages: List[ProcessedMessage],
        current_query: str
    ) -> ActiveContext:
        """Extract active conversation context."""
        # Aggregate entities and topics
        all_entities = []
        all_topics = []
        
        for msg in processed_messages:
            all_entities.extend(msg.entities_mentioned)
            all_topics.extend(msg.topics)
            
        # Deduplicate and prioritize recent mentions
        entities = list(dict.fromkeys(all_entities))[:10]
        topics = list(dict.fromkeys(all_topics))[:5]
        
        # Determine user intent
        current_msg = processed_messages[0] if processed_messages else None
        if current_msg:
            if current_msg.classification in [MessageClassification.CLARIFICATION]:
                user_intent = UserIntentType.CLARIFICATION
            elif current_msg.classification == MessageClassification.TASK_EXECUTION:
                user_intent = UserIntentType.TASK_EXECUTION
            elif current_msg.classification == MessageClassification.META_CONVERSATIONAL:
                user_intent = UserIntentType.META_DISCUSSION
            else:
                user_intent = UserIntentType.INFORMATION_SEEKING
        else:
            user_intent = UserIntentType.INFORMATION_SEEKING
            
        # Extract conversation state
        conversation_state = await self._summarize_conversation_state(processed_messages)
        
        return ActiveContext(
            entities=entities,
            topics=topics,
            conversation_state=conversation_state,
            user_intent=user_intent,
            established_facts=[],
            pending_requests=[]
        )

    async def _summarize_conversation_state(
        self,
        processed_messages: List[ProcessedMessage]
    ) -> str:
        """Summarize the current conversation state."""
        if not processed_messages:
            return "New conversation"
            
        recent_topics = []
        recent_actions = []
        
        for msg in processed_messages[:3]:  # Last 3 messages
            if msg.topics:
                recent_topics.extend(msg.topics)
            if msg.classification == MessageClassification.TASK_EXECUTION:
                recent_actions.append("task_requested")
                
        if recent_actions:
            return f"Task execution requested on {', '.join(set(recent_topics))}"
        elif recent_topics:
            return f"Discussing {', '.join(set(recent_topics))}"
        else:
            return "General conversation"

    async def _generate_summary(
        self,
        processed_messages: List[ProcessedMessage],
        active_context: ActiveContext
    ) -> str:
        """Generate a concise conversation summary."""
        if not processed_messages:
            return "New conversation starting"
            
        current_focus = active_context.topics[0] if active_context.topics else "general discussion"
        conversation_length = len(processed_messages)
        
        if conversation_length == 1:
            return f"User initiating {current_focus} discussion"
        else:
            return f"Ongoing {current_focus} conversation with {conversation_length-1} prior exchanges"

    async def _get_rag_context(self, query: str, user: ZairaUser) -> Dict[str, Any]:
        """Get RAG-enhanced context for the query."""
        try:
            rag_result = await rag_data(Globals.get_query_engine_default(), query, user)
            return {
                'rag_content': rag_result,
                'has_relevant_docs': bool(rag_result and len(rag_result.strip()) > 10)
            }
        except Exception as e:
            self.logger.warning(f"Failed to get RAG context: {e}")
            return {'rag_content': '', 'has_relevant_docs': False}

    def _merge_rag_context(
        self,
        active_context: ActiveContext,
        rag_context: Dict[str, Any]
    ) -> ActiveContext:
        """Merge RAG context into active context."""
        if rag_context.get('has_relevant_docs'):
            if 'document_retrieval' not in active_context.topics:
                active_context.topics.append('document_retrieval')
            active_context.established_facts.append('relevant_documents_found')
            
        return active_context

    def _select_relevant_messages(
        self,
        processed_messages: List[ProcessedMessage],
        active_context: ActiveContext
    ) -> List[ProcessedMessage]:
        """Select most relevant messages for context."""
        if len(processed_messages) <= self.max_relevant_messages:
            return processed_messages
            
        # Always include current message
        relevant = [processed_messages[0]]
        remaining_slots = self.max_relevant_messages - 1
        
        # Score other messages by relevance
        scored_messages = []
        for msg in processed_messages[1:]:
            score = self._calculate_relevance_score(msg, active_context)
            scored_messages.append((score, msg))
            
        # Sort by score and take top messages
        scored_messages.sort(reverse=True, key=lambda x: x[0])
        relevant.extend([msg for _, msg in scored_messages[:remaining_slots]])
        
        return relevant

    def _calculate_relevance_score(
        self,
        message: ProcessedMessage,
        active_context: ActiveContext
    ) -> float:
        """Calculate relevance score for message selection."""
        score = 0.0
        
        # Topic overlap
        topic_overlap = len(set(message.topics) & set(active_context.topics))
        score += topic_overlap * 2.0
        
        # Entity overlap
        entity_overlap = len(set(message.entities_mentioned) & set(active_context.entities))
        score += entity_overlap * 1.5
        
        # Classification relevance
        if message.classification in [MessageClassification.TASK_EXECUTION, MessageClassification.CLARIFICATION]:
            score += 3.0
        elif message.classification == MessageClassification.CONTINUATION:
            score += 1.0
            
        # Recency bonus (more recent = higher score)
        time_diff = (datetime.now() - message.timestamp).total_seconds()
        recency_score = max(0, 1.0 - (time_diff / 3600))  # Decay over 1 hour
        score += recency_score
        
        return score

    def _format_preprocessed_context(
        self,
        summary: str,
        relevant_messages: List[ProcessedMessage],
        active_context: ActiveContext,
        current_query: str
    ) -> str:
        """Format the final preprocessed context for supervisor consumption."""
        output_lines = []
        
        # Summary
        output_lines.append(f"SUMMARY: {summary}")
        output_lines.append("")
        
        # Recent messages
        output_lines.append("RECENT MESSAGES (most recent first):")
        for i, msg in enumerate(relevant_messages):
            prefix = "Current:" if i == 0 else f"Previous-{i}:"
            output_lines.append(f'{prefix} "{msg.content}" -> {msg.context}')
        output_lines.append("")
        
        # Active context
        output_lines.append("ACTIVE CONTEXT:")
        output_lines.append(f"Entities: {', '.join(active_context.entities[:8])}")
        output_lines.append(f"Topics: {', '.join(active_context.topics)}")
        output_lines.append(f"Conversation State: {active_context.conversation_state}")
        output_lines.append(f"User Intent: {active_context.user_intent.value}")
        
        return "\n".join(output_lines)

    def _estimate_token_count(self, text: str) -> int:
        """Estimate token count for the formatted text."""
        # Rough estimation: ~4 characters per token
        return len(text) // 4

    async def _fallback_preprocessing(
        self,
        chat_history: List[ZairaMessage],
        current_query: str
    ) -> PreprocessedContext:
        """Fallback preprocessing in case of errors."""
        summary = "Context preprocessing failed - using fallback"
        
        # Create minimal processed message for current query
        current_msg = ProcessedMessage(
            content=current_query,
            classification=MessageClassification.NEW_TOPIC,
            context="[fallback processing]",
            timestamp=datetime.now(),
            is_user=True,
            entities_mentioned=[],
            topics=['general']
        )
        
        active_context = ActiveContext(
            entities=[],
            topics=['general'],
            conversation_state="Fallback mode",
            user_intent=UserIntentType.INFORMATION_SEEKING,
            established_facts=[],
            pending_requests=[]
        )
        
        formatted_output = f"SUMMARY: {summary}\n\nRECENT MESSAGES:\nCurrent: \"{current_query}\" -> [fallback]\n\nACTIVE CONTEXT:\nEntities: \nTopics: general\nConversation State: Fallback mode\nUser Intent: information_seeking"
        
        return PreprocessedContext(
            summary=summary,
            recent_messages=[current_msg],
            active_context=active_context,
            formatted_output=formatted_output,
            token_count_estimate=len(formatted_output) // 4
        )


# Singleton instance for global access
_context_preprocessor_instance = None

def get_context_preprocessor(llm: BaseLanguageModel) -> ContextPreprocessor:
    """Get the global context preprocessor instance."""
    global _context_preprocessor_instance
    if _context_preprocessor_instance is None:
        _context_preprocessor_instance = ContextPreprocessor(llm)
    return _context_preprocessor_instance


# Convenience function for direct use
async def preprocess_for_supervisor(
    chat_history: List[ZairaMessage],
    current_query: str,
    user: ZairaUser,
    max_messages: int = 10,
    llm: BaseLanguageModel = None
) -> str:
    """
    Convenience function that returns formatted preprocessed context as string.
    """
    preprocessor = get_context_preprocessor(llm)
    result = await preprocessor.preprocess_for_supervisor(
        chat_history, current_query, user, max_messages
    )
    return result.formatted_output