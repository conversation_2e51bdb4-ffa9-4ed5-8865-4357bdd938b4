{"permissions": {"allow": ["Bash(grep:*)", "Bash(../../.venv/Scripts/python.exe tests/unit/test_scheduled_task.py)", "Bash(../../.venv/Scripts/python.exe tests/integration/test_imap_idle_workflow.py)", "Bash(../../.venv/Scripts/python.exe main.py:*)", "<PERSON><PERSON>(timeout:*)", "Bash(../../.venv/Scripts/python.exe tests/unit/test_scheduled_task_persistence.py)", "Bash(../../.venv/Scripts/python.exe tests/unit/test_scheduled_task_routing.py)", "Bash(../../.venv/Scripts/python.exe tests/unit/test_database_operations.py)", "Bash(../../.venv/Scripts/python.exe tests/unit/test_supervisor_framework.py:*)", "Bash(find:*)", "Bash(../../.venv/Scripts/python.exe:*)", "Bash(rg:*)", "Bash(ls:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_authentication_oauth.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_rag_system.py::TestRAGDocumentProcessing::test_document_chunking -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_rag_system.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/health/ -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ -v)", "Bash(../../.venv/Scripts/pytest.exe tests/health/test_system_health.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_imap_idle_workflow.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_chat_session.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_chat_session.py::TestChatSessionTools::test_new_chat_session_success -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_chat_session_simple.py -v)", "<PERSON><PERSON>(mv:*)"], "deny": []}}