from imports import *

from aiohttp import web

from endpoints.oauth._verifier_ import OAuth2App

class OAuth2New(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_oauth("input", [""], "client_id", "client_secret", "auth_url", "token_url")

    async def on_success_return(self) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return()
        ret_html += ""
        return ret_html

    async def on_success_execute(self) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        ret_html = await super().on_success_execute()
        ret_html += ""
        return ret_html
    
    async def on_success_execute_fail(self) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute_fail()
        ret_html += ""
        return ret_html
