"""
Unit tests for scheduled task routing functionality.
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from imports import *

@pytest.mark.unit
@pytest.mark.asyncio
class TestScheduledTaskRouting:
    """Test scheduled task routing and functionality"""
    
    async def test_scheduled_task_manager_creation(self):
        """Test that the scheduled task manager can be created"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        
        manager = ScheduledTaskPersistenceManager.get_instance()
        assert manager is not None
        assert hasattr(manager, 'get_active_tasks')
        assert hasattr(manager, 'save_task')
        assert hasattr(manager, 'cancel_task')
    
    async def test_list_active_tasks_query_routing(self):
        """Test that list queries are properly routed"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        
        manager = ScheduledTaskPersistenceManager.get_instance()
        
        # Test with mock data
        mock_tasks = [
            {
                'task_id': 'test-task-1',
                'schedule_prompt': 'check email every hour',
                'is_active': True
            }
        ]
        
        with patch.object(manager, 'get_active_tasks') as mock_get:
            mock_get.return_value = mock_tasks
            
            result = await manager.get_active_tasks("test-user-123")
            assert len(result) == 1
            assert result[0]['task_id'] == 'test-task-1'
    
    async def test_list_active_tasks_empty_result(self):
        """Test behavior when no scheduled tasks exist"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        
        manager = ScheduledTaskPersistenceManager.get_instance()
        
        with patch.object(manager, 'get_active_tasks') as mock_get:
            mock_get.return_value = []
            
            result = await manager.get_active_tasks("test-user-123")
            assert len(result) == 0
    
    async def test_list_active_tasks_with_imap_task(self):
        """Test listing when IMAP task exists"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        from datetime import datetime, timedelta
        
        manager = ScheduledTaskPersistenceManager.get_instance()
        
        # Mock IMAP task data
        mock_imap_task = {
            'task_id': 'imap_task_12345678',
            'schedule_prompt': 'trigger IMAP IDLE every 30 minutes',
            'target_prompt': 'check for new emails',
            'schedule_type': 'interval',
            'delay_seconds': 1800,  # 30 minutes
            'next_execution': datetime.now() + timedelta(minutes=25),
            'created_at': datetime.now() - timedelta(hours=2),
            'is_active': True
        }
        
        with patch.object(manager, 'get_active_tasks') as mock_get:
            mock_get.return_value = [mock_imap_task]
            
            result = await manager.get_active_tasks("test-user-123")
            assert len(result) == 1
            assert 'IMAP IDLE' in result[0]['schedule_prompt']
            assert result[0]['delay_seconds'] == 1800
    
    async def test_cancel_task_routing(self):
        """Test that cancel queries are properly routed"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        
        manager = ScheduledTaskPersistenceManager.get_instance()
        
        with patch.object(manager, 'cancel_task') as mock_cancel:
            mock_cancel.return_value = True
            
            result = await manager.cancel_task("test-task-123", "User requested")
            assert result is True
            mock_cancel.assert_called_once_with("test-task-123", "User requested")
    
    async def test_status_query_routing(self):
        """Test that status queries are properly routed"""
        from managers.manager_scheduled_tasks import ScheduledTaskPersistenceManager
        
        manager = ScheduledTaskPersistenceManager.get_instance()
        
        # Test load task functionality
        mock_task_data = {
            'task_id': 'test-task-123',
            'schedule_prompt': 'backup every day',
            'is_active': True
        }
        
        with patch.object(manager, 'load_task') as mock_load:
            mock_load.return_value = mock_task_data
            
            result = await manager.load_task("test-task-123")
            assert result['task_id'] == 'test-task-123'
            assert result['schedule_prompt'] == 'backup every day'