# tests/unit/test_multimodal_manager.py
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *
from managers.manager_multimodal import MultimodalManager
import pytest
import pytest_asyncio
from pydantic import ValidationError
from pathlib import Path
from uuid import uuid4
import base64
import asyncio

class TestMultimodalManager:
    
    @pytest.fixture
    def manager(self):
        """Get a fresh instance of MultimodalManager for testing"""
        return MultimodalManager.get_instance()
    
    @pytest.fixture
    async def setup_manager(self, manager):
        """Setup the manager for testing"""
        await manager.setup()
        return manager
    
    def test_singleton_pattern(self):
        """Test that MultimodalManager follows singleton pattern"""
        manager1 = MultimodalManager.get_instance()
        manager2 = MultimodalManager.get_instance()
        assert manager1 is manager2
        
    def test_singleton_instance_creation(self):
        """Test that direct instantiation still returns singleton"""
        manager1 = MultimodalManager()
        manager2 = MultimodalManager()
        assert manager1 is manager2
    
    @pytest.mark.asyncio
    async def test_setup_initialization(self, manager):
        """Test manager setup creates necessary directories and config"""
        await manager.setup()

        # Check that assets directory exists
        assets_dir = BASE_DIR() / "assets" / "documents"
        assert assets_dir.exists()
        assert assets_dir.is_dir()

        # Check configuration was set
        assert hasattr(manager, 'config')
        assert manager.config['vision_model'] == 'gpt-4o-mini'
        assert manager.config['enable_image_processing'] is True
        assert manager.config['enable_table_processing'] is True

    @pytest.mark.asyncio
    async def test_setup_idempotent(self, manager):
        """Test that setup can be called multiple times safely"""
        await manager.setup()
        config_1 = manager.config.copy()

        await manager.setup()  # Second call
        config_2 = manager.config.copy()

        assert config_1 == config_2
    
    def test_get_surrounding_context(self, manager):
        """Test context extraction from element list"""
        all_elements = [
            {"type": "Title", "text": "Document Title"},
            {"type": "NarrativeText", "text": "This is some intro text."},
            {"type": "Table", "text": "Table content"},
            {"type": "NarrativeText", "text": "This is text after the table."},
            {"type": "Image", "text": "Image description"}
        ]
        
        # Test context for table at index 2
        context = manager._get_surrounding_context(all_elements, 2, context_window=1)
        expected = "This is some intro text. This is text after the table."
        assert context == expected
    
    def test_get_surrounding_context_edge_cases(self, manager):
        """Test context extraction at boundaries"""
        all_elements = [
            {"type": "Title", "text": "Document Title"},
            {"type": "NarrativeText", "text": "Single element"}
        ]
        
        # Test context at start
        context = manager._get_surrounding_context(all_elements, 0, context_window=2)
        assert context == "Single element"
        
        # Test context at end
        context = manager._get_surrounding_context(all_elements, 1, context_window=2)
        assert context == "Document Title"
    
    def test_is_numeric(self, manager):
        """Test numeric value detection"""
        assert manager._is_numeric("123") is True
        assert manager._is_numeric("123.45") is True
        assert manager._is_numeric("$123.45") is True
        assert manager._is_numeric("123,456.78") is True
        assert manager._is_numeric("50%") is True
        assert manager._is_numeric("not a number") is False
        assert manager._is_numeric("") is False
    
    def test_is_date(self, manager):
        """Test date value detection"""
        assert manager._is_date("2024-12-01") is True
        assert manager._is_date("12/01/2024") is True
        assert manager._is_date("12-01-2024") is True
        assert manager._is_date("not a date") is False
        assert manager._is_date("123") is False
    
    @pytest.mark.asyncio
    async def test_infer_column_types(self, manager):
        """Test column type inference"""
        headers = ["Name", "Age", "Salary", "Start Date"]
        data_rows = [
            ["John", "25", "$50,000", "2024-01-15"],
            ["Jane", "30", "$60,000", "2023-06-01"],
            ["Bob", "35", "$70,000", "2022-03-12"]
        ]

        column_types = await manager._infer_column_types(data_rows, headers)

        assert column_types["Name"] == "text"
        assert column_types["Age"] == "numeric"
        assert column_types["Salary"] == "numeric"
        assert column_types["Start Date"] == "date"
    
    def test_structured_table_to_markdown(self, manager):
        """Test table to markdown conversion"""
        table_data = [
            ["Name", "Age", "City"],
            ["John", "25", "New York"],
            ["Jane", "30", "Los Angeles"]
        ]
        
        markdown = manager._structured_table_to_markdown(table_data)
        
        expected_lines = [
            "| Name | Age | City |",
            "| --- | --- | --- |",
            "| John | 25 | New York |",
            "| Jane | 30 | Los Angeles |"
        ]
        
        for expected_line in expected_lines:
            assert expected_line in markdown
    
    def test_structured_table_to_markdown_empty(self, manager):
        """Test table to markdown with empty data"""
        assert manager._structured_table_to_markdown([]) == ""
        assert manager._structured_table_to_markdown([[]]) == ""
    
    @pytest.mark.asyncio
    async def test_extract_table_key_info(self, manager):
        """Test table key information extraction"""
        table_markdown = """| Name | Age | City |
| --- | --- | --- |
| John | 25 | New York |
| Jane | 30 | Los Angeles |"""

        key_info = await manager._extract_table_key_info(table_markdown)

        assert key_info["headers"] == ["Name", "Age", "City"]
        assert key_info["num_columns"] == 3
        assert key_info["num_rows"] == 2
        assert key_info["total_cells"] == 6
        assert key_info["has_headers"] is True

    @pytest.mark.asyncio
    async def test_extract_table_key_info_invalid(self, manager):
        """Test table key information extraction with invalid data"""
        # Too few lines
        key_info = await manager._extract_table_key_info("| Header |")
        assert "error" in key_info

    @pytest.mark.asyncio
    async def test_encode_image_to_base64(self, manager):
        """Test image to base64 encoding"""
        # Create a simple test image file
        test_image_data = b"fake_image_data"
        test_file = BASE_DIR() / "test_image.png"

        try:
            with open(test_file, 'wb') as f:
                f.write(test_image_data)

            base64_result = await manager._encode_image_to_base64(str(test_file))
            expected = base64.b64encode(test_image_data).decode('utf-8')

            assert base64_result == expected
        finally:
            # Cleanup
            if test_file.exists():
                test_file.unlink()

    @pytest.mark.asyncio
    async def test_encode_image_to_base64_nonexistent(self, manager):
        """Test image encoding with nonexistent file"""
        result = await manager._encode_image_to_base64("/nonexistent/file.png")
        assert result == ""
    
    @pytest.mark.asyncio
    async def test_cleanup_assets(self, manager):
        """Test asset cleanup functionality"""
        # Create test asset directory
        doc_id = str(uuid4())
        doc_assets_dir = BASE_DIR() / "assets" / "documents" / doc_id
        doc_assets_dir.mkdir(parents=True, exist_ok=True)

        # Create test file
        test_file = doc_assets_dir / "test_asset.png"
        test_file.write_text("test content")

        # Verify file exists
        assert test_file.exists()

        # Cleanup
        await manager.cleanup_assets(doc_id)

        # Verify directory is removed
        assert not doc_assets_dir.exists()

    @pytest.mark.asyncio
    async def test_get_asset_path(self, manager):
        """Test asset path retrieval"""
        # Create test asset
        doc_id = str(uuid4())
        element_id = "test_element"
        doc_assets_dir = BASE_DIR() / "assets" / "documents" / doc_id
        doc_assets_dir.mkdir(parents=True, exist_ok=True)

        test_file = doc_assets_dir / f"{element_id}_hash123.png"
        test_file.write_text("test content")

        try:
            # Test finding asset
            asset_path = await manager.get_asset_path(doc_id, element_id)
            assert asset_path == str(test_file)

            # Test nonexistent asset
            nonexistent_path = await manager.get_asset_path(doc_id, "nonexistent")
            assert nonexistent_path is None

        finally:
            # Cleanup
            await manager.cleanup_assets(doc_id)
    
    @pytest.mark.asyncio
    async def test_save_image_asset(self, manager):
        """Test image asset saving"""
        doc_id = str(uuid4())
        element_id = "test_image"
        image_data = b"fake_image_data"

        try:
            asset_path = await manager._save_image_asset(image_data, doc_id, element_id)

            # Verify file was created
            assert os_path.exists(asset_path)

            # Verify content
            with open(asset_path, 'rb') as f:
                saved_data = f.read()
            assert saved_data == image_data

            # Verify path structure
            assert doc_id in asset_path
            assert element_id in asset_path

        finally:
            # Cleanup
            await manager.cleanup_assets(doc_id)

# Integration test for complete multimodal processing
class TestMultimodalManagerIntegration:
    
    @pytest.fixture
    async def setup_manager(self):
        """Setup manager for integration tests"""
        manager = MultimodalManager.get_instance()
        await manager.setup()
        return manager
    
    # Note: This test requires actual document files which may not be available in test environment
    # It's designed to be run with sample documents when available
    async def test_extract_multimodal_elements_integration(self, setup_manager):
        """Test complete multimodal element extraction (requires sample documents)"""
        manager = setup_manager
        
        # This would test with actual PDF/DOCX files containing images and tables
        # For now, we'll test the method structure without actual files
        doc_id = str(uuid4())
        
        # Test with nonexistent file to verify error handling
        result = await manager.extract_multimodal_elements("/nonexistent/file.pdf", doc_id)
        
        # Should return error structure
        assert "error" in result
        assert result["doc_id"] == doc_id
        assert result["text_elements"] == []
        assert result["images"] == []
        assert result["tables"] == []

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])