from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from managers.manager_users import ZairaUserManager
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

class SupervisorTask_Email(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)

        # Send the email with the output message
        result = send_email(
            recipient="<EMAIL>",  # Default recipient
            subject="Message from AgenticRAG",
            content=input_message
        )
        LogFire.log("OUTPUT", "Mail:", input_message)

        if result:
            await user.my_task.send_response("Email sent successfully!")
        else:
            await user.my_task.send_response("Failed to send email. Please check the logs for details.")

def send_email(recipient, subject, content):
    """
    Send an email directly using SMTP
    """
    try:
        # SMTP Configuration
        smtp_server = "smtp-mail.outlook.com"  # Outlook SMTP server
        smtp_port = 587  # Outlook SMTP port for TLS
        smtp_username = "<EMAIL>"  # Your email
        smtp_password = "bfhcgsvztdzbptta"  # Your password or app password
        sender = smtp_username

        # Create a MIME message
        msg = MIMEMultipart()
        msg['From'] = sender
        msg['To'] = recipient
        msg['Subject'] = subject

        # Attach the email body
        msg.attach(MIMEText(content, 'html'))

        # Connect to the SMTP server
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()  # Secure the connection
        server.login(smtp_username, smtp_password)

        # Send the email
        server.send_message(msg)
        server.quit()
        print(f"Email sent to {recipient} with subject: {subject}")
        return True
    except Exception as e:
        print(f"Failed to send email: {str(e)}")
        return False

async def create_out_task_email() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_Email(name="email_out", prompt_id="Output_Sender_Mail"))