from imports import *

if TYPE_CHECKING:
    # This only runs for type checkers, not at runtime — safe to "reach inside"
    from endpoints.oauth._verifier_ import OAuth2Verifier, OAuth2App

async def oauth_handle_commands(self: "OAuth2Verifier", app: "OAuth2App", tokens):
    """ Only used for things that are used for more than 1 OAuth task """
    ret_val = True
    if len(app.commands) > 0:
        for command in app.commands:
            if command == "exit":
                if Globals.is_docker():
                    async def delayed_exit():
                        from asyncio import sleep
                        await sleep(15)
                        exit()

                    from asyncio import create_task
                    create_task(delayed_exit())
                    ret_val = False
    return ret_val
