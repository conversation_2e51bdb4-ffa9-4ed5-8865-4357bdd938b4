from imports import *

from langgraph.types import Command
from langgraph.graph import END
from langchain_core.messages import HumanMessage, SystemMessage

from managers.manager_supervisors import Supervisor<PERSON>anager, SupervisorSupervisor, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from managers.manager_prompts import PromptManager

from tasks.outputs.task_supervisor_output_sender import create_supervisor_output_sender
from tasks.outputs.task_supervisor_output_processing import create_supervisor_output_processing

class SupervisorTopOutputSupervisor(SupervisorSupervisor):
    _instance = None
    _initialized = False

    output_converter_supervisor: SupervisorSupervisor = None
    output_sender_supervisor: SupervisorSupervisor = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
            cls._instance.__init__()
        return cls._instance

    @classmethod
    def get_instance(cls) -> "SupervisorTopOutputSupervisor":
        return cls()

    def __init__(self):
        super().__init__(name="top_output_supervisor", prompt_id="Output_Supervisor_Prompt")
        # Additional initialization if needed

    @classmethod
    async def create_small_tasks(cls):
        pass
        
    @classmethod
    async def create_supervisors(cls):
        instance = cls.get_instance()
        instance.output_converter_supervisor = await create_supervisor_output_processing()
        instance.output_sender_supervisor = await create_supervisor_output_sender()
        
    @classmethod
    async def create_top_output_supervisor(cls) -> SupervisorSupervisor:
        instance = cls.get_instance()
        await instance.create_small_tasks()
        await instance.create_supervisors()

        ret_val = SupervisorManager.register_supervisor(SupervisorTopOutputSupervisor()) \
            .add_task(instance.output_converter_supervisor) \
            .add_task(instance.output_sender_supervisor) \
            .compile()
        
        #ret_val.always_call_LAST = True
        return ret_val
        
    async def llm_call_router(self, state: SupervisorTaskState):
        user = await ZairaUserManager.find_user(state.user_guid)
        result1 = await self.output_converter_supervisor.call_supervisor_with_state(state)
        
        # Check if this is a completion, cancellation, or status message that shouldn't generate follow-up questions
        result_text = result1["result"].lower()
        completion_keywords = ["succesvol", "aangemaakt", "geannuleerd", "voltooid", "klaar", "finished", "completed", "cancelled", "success", "wacht", "timeout"]
        
        if any(keyword in result_text for keyword in completion_keywords):
            # Don't generate follow-up questions for completion/status messages
            response = result1["result"]
        else:
            continue_question = await ZairaSettings.llm.ainvoke(
                input=[
                    SystemMessage(PromptManager.get_prompt("AskZaira_Prompt") + ". Try and find a question that would be likely to help the user in his search for his yet-to-be-requested next prompt or problem."),
                    HumanMessage(result1["result"])
                ]
            )
            response = result1["result"] + "\n\n" + continue_question.content

        output_task = SupervisorManager.get_task(f"{str(state.sections['OriginalSource'].get_description()).lower()}_out")
        state.messages.append(HumanMessage(response))
        await output_task.llm_call(state)

        result2 = (await self.output_sender_supervisor.call_supervisor(f"Route the following only to the likely outputs: {response}", user))
        call_trace = state.call_trace + result1["call_trace"] + result2["call_trace"]

        LogFire.log("OUTPUT", f"Task finished with question of length: {len(response)}.\nCall trace: \n" + "\n".join(call_trace), f"Question: {response}", user, "top_output")
        return Command(update={"messages": [result1["result"]], "call_trace": call_trace}, goto=END)

async def create_top_output_supervisor() -> SupervisorSupervisor:
    return await SupervisorTopOutputSupervisor.create_top_output_supervisor()
    