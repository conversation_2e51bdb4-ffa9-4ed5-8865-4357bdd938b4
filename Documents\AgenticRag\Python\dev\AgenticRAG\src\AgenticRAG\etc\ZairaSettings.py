from imports import *

from dataclasses import dataclass
from typing import TYPE_CHECKING

if TYPE_CHECKING:
    # This only runs for type checkers, not at runtime — safe to "reach inside"
    from llama_index.core.settings import _Settings
    from langchain_core.language_models.chat_models import BaseChatModel
    from fastembed import SparseTextEmbedding

@dataclass
class ZairaSettingsClass:
    """Settings for Zaira"""
    IsDebugMode: bool
    llm: "BaseChatModel" = None
    sparse_embed_model: "SparseTextEmbedding" = None

    @classmethod
    def OllamaSettings(cls) -> "_Settings":
        from llama_index.core import Settings
        return Settings

# Singleton
ZairaSettings = ZairaSettingsClass
