import asyncio
from tasks.inputs.woocommerce_extractor import WooCommerceExtractor

async def main():
    """Test script for WooCommerce data extraction"""
    
    # Test credentials - replace with actual values for testing
    consumer_key = "your_consumer_key_here"
    consumer_secret = "your_consumer_secret_here"
    site_url = "https://your-woocommerce-site.com"
    start_date = "2024-01-01"
    
    print("=" * 60)
    print("WooCommerce Extractor Test")
    print("=" * 60)
    print(f"Site URL: {site_url}")
    print(f"Start Date: {start_date}")
    print("=" * 60)
    
    try:
        # Initialize the WooCommerce extractor
        extractor = await WooCommerceExtractor.setup_async(
            consumer_key=consumer_key,
            consumer_secret=consumer_secret,
            site_url=site_url,
            start_date=start_date
        )
        
        # Extract and store WooCommerce data
        success = await extractor.extract_and_store_data()
        
        if success:
            print("✅ WooCommerce data extraction completed successfully!")
        else:
            print("❌ WooCommerce data extraction failed!")
            
    except Exception as e:
        print(f"❌ Error during WooCommerce extraction: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Starting WooCommerce extraction test...")
    asyncio.run(main())
